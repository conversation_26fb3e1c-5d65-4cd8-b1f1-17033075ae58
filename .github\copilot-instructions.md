<SYSTEM>
You are an AI programming assistant that is specialized in applying code changes to an existing document.
Follow Microsoft content policies.
Avoid content that violates copyrights.
If you are asked to generate content that is harmful, hateful, racist, sexist, lewd, violent, or completely irrelevant to software engineering, only respond with "Sorry, I can't assist with that."
Keep your answers short and impersonal.
The user has the following code open in the editor, starting from line 1.
````instructions
> **Terminal Environment:** All terminal commands must be compatible with PowerShell on Windows 11. Use backslashes (`\`) for local paths and ensure scripts work in PowerShell.

# GitHub Copilot Instructions for MUNygo Project

useClientLogger.ts:58 
 [ERROR] Vue component error 
{componentName: 'Button', lifecycleHook: 'native event handler', errorType: 'VUE_COMPONENT_ERROR'}
 TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'ComputedRefImpl'
    |     property 'dep' -> object with constructor 'Dep'
    --- property 'computed' closes the circle
    at JSON.stringify (<anonymous>)
    at Object.toDisplayString [as interpolate] (shared.mjs:110:20)
    at formatMessagePart (core-base.mjs:146:28)
    at core-base.mjs:127:73
    at Array.reduce (<anonymous>)
    at formatMessageParts (core-base.mjs:127:45)
    at formatParts (core-base.mjs:116:16)
    at msg (core-base.mjs:98:26)
    at evaluateMessage (core-base.mjs:1728:22)
    at translate (core-base.mjs:1547:22)
useClientLogger.ts:58 
 [ERROR] Vue component error 
{componentName: 'Dialog', lifecycleHook: 'render function', errorType: 'VUE_COMPONENT_ERROR'}
 TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'ComputedRefImpl'
    |     property 'dep' -> object with constructor 'Dep'
    --- property 'computed' closes the circle
    at JSON.stringify (<anonymous>)
    at Object.toDisplayString [as interpolate] (shared.mjs:110:20)
    at formatMessagePart (core-base.mjs:146:28)
    at core-base.mjs:127:73
    at Array.reduce (<anonymous>)
    at formatMessageParts (core-base.mjs:127:45)
    at formatParts (core-base.mjs:116:16)
    at msg (core-base.mjs:98:26)
    at evaluateMessage (core-base.mjs:1728:22)
    at translate (core-base.mjs:1547:22)

## Circular Dependency Prevention & Dependency Injection Best Practices

*   **Avoid Circular Dependencies:** Never import service instances from the main entry point (`index.ts`) within service modules. This creates circular import chains that lead to undefined dependencies at runtime.
    *   **❌ Bad Pattern:**
      ```typescript
      // In a service file
      import { io, someServiceInstance } from '../index';
      export const myService = new MyService(); // Singleton at module level
      ```
    *   **✅ Good Pattern:**
      ```typescript
      // In a service file
      export class MyService {
        constructor(
          private dependency1: SomeService,
          private dependency2: AnotherService
        ) {
          // Dependencies injected via constructor
        }
      }
      ```

*   **Constructor Dependency Injection:** All services should receive their dependencies through constructor parameters rather than importing them directly. This ensures:
    *   Dependencies are available when the service is instantiated
    *   Better testability (easy to mock dependencies)
    *   Clear dependency relationships
    *   No timing issues during module initialization

*   **Centralized Service Creation:** Create all service instances in `index.ts` in proper dependency order:
    ```typescript
    // Create services in dependency order
    const serviceA = new ServiceA();
    const serviceB = new ServiceB(serviceA); // ServiceB depends on ServiceA
    const serviceC = new ServiceC(serviceA, serviceB); // ServiceC depends on both
    ```

*   **Route Factory Pattern:** Routes that need service instances should be factory functions that accept the service as a parameter:
    ```typescript
    // routes/myRoutes.ts
    export default function createMyRoutes(myService: MyService) {
      const router = new Hono();
      // Use myService in route handlers
      return router;
    }
    
    // index.ts
    app.route('/api/my-routes', createMyRoutes(myServiceInstance));
    ```

*   **Service Constructors:** When creating new services, ensure their constructors match the actual implementation. Check existing service constructor signatures before instantiating:
    ```powershell
    # Check constructor signatures in PowerShell
    Get-Content .\backend\src\services\myService.ts | Select-String -Pattern "constructor"
    ```

*   **No Module-Level Singletons:** Avoid creating service instances at the module level (outside of functions/classes). Instead, export the class and create instances in `index.ts`:
    ```typescript
    // ❌ Avoid this
    export const myService = new MyService();
    
    // ✅ Do this instead
    export class MyService { /* ... */ }
    // Then in index.ts: const myService = new MyService(dependencies);
    ```

## Project Structure

*   The project is organized into two main directories: `backend/` for the Hono API and `frontend/` for the Vue.js application.
*   Configuration files like `tsconfig.json`, `vite.config.ts` (frontend), and `prisma/schema.prisma` (backend) define project settings.

## Backend Directory Structure (`backend/`)

*   `package.json`: Manages Node.js project dependencies, scripts (e.g., `npm run dev`, `npm test`, `npm run build`), and project metadata.
*   `tsconfig.json`: TypeScript compiler configuration for the backend, defining how `.ts` files are transpiled to JavaScript.
*   `prisma/`:
    *   `schema.prisma`: The heart of the database definition. Contains comprehensive Prisma schema including User, Offer, Interest, ChatSession, ChatMessage, Transaction, PayerNegotiation, PaymentReceivingInfo, and Notification models with complex relationships. Changes here are applied to the database using `npx prisma migrate dev`.
    *   `migrations/`: Stores the SQL migration files generated by Prisma Migrate. Each sub-directory represents a specific migration applied to the database schema.
    *   `dev.db`: (If present and using SQLite for development) The SQLite database file used during local development.
*   `src/`: The main source code directory for the backend application.
    *   `index.ts`: The primary entry point of the Hono application. Initializes the server, registers middleware, mounts routes, handles Socket.IO connections with centralized event handling for chat, transactions, and notifications.
    *   `client.ts`: Prisma client configuration and initialization.
    *   `middleware/`: Contains custom middleware functions used in the Hono application.
        *   `auth.ts`: Provides `authMiddleware` to protect routes by verifying JWTs. Extracts and attaches JWT payload to request context for downstream handlers.
    *   `routes/`: Defines the API endpoints. Each file groups related routes.
        *   `auth.ts`: Handles complete authentication lifecycle including registration with email verification, login with JWT generation, phone verification with OTP, and user profile management with dynamic reputation calculation.
        *   `chatRoutes.ts`: Manages chat sessions, message history, and transaction-related chat operations including transaction status updates and system message handling.
        *   `notificationRoutes.ts`: Exposes endpoints for fetching pending notifications and managing notification state. Includes comprehensive notification types for interests, transactions, and system events.
        *   `offer.ts`: Manages comprehensive CRUD operations for offers and interest processing. Handles offer creation/updates, browsing with dynamic rate calculation, interest expression/processing, and extensive Socket.IO event emissions for real-time updates.
    *   `services/`: Houses the business logic of the application.
        *   `chatService.ts`: Manages chat message handling, system message generation, and Socket.IO integration for real-time chat functionality.
        *   `email.ts`: Handles email sending with nodemailer. Supports both development (Ethereal) and production SMTP configurations for verification emails.
        *   `interestService.ts`: Core business logic for interest processing including acceptance (creates chat sessions, emits events) and decline handling with reason codes.
        *   `notificationService.ts`: Comprehensive notification management including creation, persistence, and Socket.IO emission for various notification types (interests, transactions, system events).
        *   `payerNegotiationService.ts`: Advanced payer designation system managing payment readiness gates, system recommendations based on reputation/currency, and proposal handling.
        *   `transactionService.ts`: Core transaction flow management including status transitions, payment declarations, confirmations, timer handling, and comprehensive Socket.IO event emissions.
        *   `twilio.ts`: Phone verification interface supporting both production Twilio integration and development mock modes with configurable OTP handling.
    *   `scripts/`: Contains utility scripts for development and administrative tasks (database inspection, data migration, testing utilities).
    *   `test/`: Includes unit and integration tests using Vitest for routes, services, and business logic validation.
    *   `types/`: Holds TypeScript type definitions and Socket.IO event schemas.
        *   `declarations.d.ts`: Ambient module declarations for external libraries without TypeScript definitions.
        *   `socketEvents.ts`: Comprehensive Socket.IO event name constants and TypeScript interfaces for all payloads including offers, interests, chat, transactions, and notifications.
    *   `utils/`: Contains reusable utility functions.
        *   `otp.ts`: OTP generation and verification utilities using otplib for custom OTP implementation.
        *   `rateLimiter.ts` / `rateLimiter_new.ts`: Advanced rate limiting for OTP operations with phone number normalization, attempt tracking, and blocking mechanisms.
        *   `token.ts`: Cryptographically secure token generation for email verification and security purposes.

## Frontend Directory Structure (`frontend/`)

*   `package.json`: Manages frontend project dependencies (Vue.js, Pinia, Vue Router, Naive UI, Vue I18n, Vite, etc.), scripts (e.g., `npm run dev`, `npm run build`, `npm test`), and project metadata.
*   `vite.config.ts`: Configuration file for Vite, the build tool used for the frontend. Defines build options, plugins, and development server settings.
*   `tsconfig.json` (and variants like `tsconfig.app.json`, `tsconfig.node.json`): TypeScript compiler configurations for different aspects of the frontend project.
*   `index.html`: The main HTML file that serves as the entry point for the single-page application (SPA). Vite injects the bundled JavaScript and CSS into this file.
*   `public/`: Contains static assets that are copied directly to the build output directory without processing by Vite (e.g., `vite.svg`, favicons).
*   `src/`: The main source code directory for the Vue.js application.
    *   `main.ts`: The primary entry point of the Vue application. Initializes the Vue app instance, sets up Vue Router, Pinia store, Naive UI, Vue I18n, and mounts the root component (`App.vue`) to the DOM.
    *   `App.vue`: The root Vue component of the application. Defines the main layout structure, including navbars, footers, and a `<router-view>` to display routed components.
    *   `style.css`: Global CSS styles for styling the entire application.
    *   `i18n.ts`: Vue I18n configuration with Persian (FA) and English (EN) language support.
    *   `vite-env.d.ts`: TypeScript declaration file for Vite-specific environment variables.
    *   `assets/`: Contains static assets processed by Vite during the build (e.g., images, fonts). Can be imported directly into Vue components.
    *   `locales/`: Contains translation files for internationalization (fa.json, en.json).
    *   `components/`: Houses reusable Vue components used across different parts of the application.
        *   `AppContent.vue`: Main layout component that wraps application content and initializes centralized socket management.
        *   `ConnectionStatus.vue`: Displays real-time connection status for Socket.IO.
        *   `DeclineInterestModal.vue`: Modal component for declining interests with reason selection.
        *   `InterestRequestCard.vue`: Displays interest request details with accept/decline actions.
        *   `LanguageSelector.vue`: Component for switching between supported languages (FA/EN).
        *   `NavBar.vue`: Main navigation bar with user controls and language/theme selection.
        *   `NotificationBell.vue`: Notification bell icon with badge indicating unread notifications.
        *   `OfferCard.vue`: Card component for displaying offer summaries in browse view.
        *   `OfferDetailsModal.vue`: Modal component for detailed offer information.
        *   `OfferForm.vue`: Form component for creating and editing offers.
        *   `OfferSummaryCard.vue`: Condensed offer display for user's own offers.
        *   `PaymentReadinessGate.vue`: Component managing payment information collection for transaction readiness.
        *   `ReputationIcon.vue`: Displays user reputation level with appropriate visual indicators.
        *   `ThemeToggle.vue`: Component for switching between light and dark themes.
        *   `TransactionDetailCard.vue`: Displays detailed transaction information.
        *   `TransactionFlowCard.vue` / `TransactionFlowCardV2.vue` / `TransactionFlowCardV3.vue`: Evolution of transaction flow management UI with progressive enhancement.
        *   `TransactionInfoCard.vue`: Shows transaction summary information.
        *   `TransactionTimelineLog.vue`: Displays chronological transaction event history.
    *   `composables/`: Contains Vue composition functions for reusable logic.
    *   `router/`: Contains the Vue Router configuration.
        *   `index.ts`: Defines all application routes with lazy-loading, authentication guards, and phone verification requirements. Includes navigation protection for authenticated and unauthenticated users.
    *   `services/`: Modules responsible for interacting with backend APIs and managing real-time communication.
        *   `apiClient.ts`: Configured Axios instance with automatic JWT token attachment and global error handling for 401 responses.
        *   `authService.ts`: Authentication-related API calls.
        *   `centralizedSocketManager.ts`: **Critical**: Singleton socket manager preventing connection conflicts, managing event registration/cleanup, and providing type-safe event handling across all stores.
        *   `eventBus.ts`: Internal event bus for cross-store communication with type definitions for common events.
        *   `interestService.ts`: API calls for interest acceptance and decline operations.
        *   `offerService.ts`: Comprehensive offer-related API interactions including CRUD operations, browsing, and interest expression.
        *   `offerStatusService.ts`: API calls for offer status management.
        *   `socketService.ts`: Legacy socket service (largely replaced by centralizedSocketManager).
        *   `transactionApiService.ts`: API calls for transaction flow operations including agreement, payment declarations, and confirmations.
    *   `stores/`: Contains Pinia store modules for state management.
        *   `auth.ts`: Authentication state management with JWT persistence, user profile management, and socket initialization integration.
        *   `chatStore.ts`: Chat session state management with message history, real-time updates, and transaction integration.
        *   `connection.ts`: Connection status monitoring for Socket.IO.
        *   `index.ts`: Main Pinia instance configuration.
        *   `interestStore.ts`: Manages user's interests in others' offers with real-time status updates and cross-store event coordination.
        *   `language.ts`: Language selection and persistence for internationalization.
        *   `myOffersStore.ts`: Manages user's own offers and received interests with comprehensive Socket.IO integration and real-time updates.
        *   `notificationStore.ts`: Global notification management with persistent storage, real-time updates, and comprehensive notification types.
        *   `offerStore.ts`: Browsable offers state management with real-time updates, filtering, and event bus integration.
        *   `payerNegotiation.ts`: Advanced payer negotiation state management for first-payer designation process.
        *   `theme.ts`: Theme selection and persistence.
        *   `transaction.ts` / `transactionStore.ts`: Transaction flow state management with status tracking, timer management, and comprehensive real-time updates.
    *   `types/`: TypeScript type definitions specific to the frontend application.
        *   `api.ts`: API response interfaces including rate limiting, OTP operations, and generic responses.
        *   `offer.ts`: Comprehensive offer and interest type definitions including frontend-specific interfaces for different contexts.
        *   `socketEvents.ts`: **Critical**: Complete Socket.IO event definitions with type-safe payload interfaces for all real-time events including offers, interests, chat, transactions, and notifications.
        *   `transaction.ts`: Transaction-specific type definitions including status enums and comprehensive transaction data structures.
    *   `utils/`: Reusable utility functions and helper modules.
        *   `errorHandler.ts`: Centralized error handling with user-friendly message extraction and Naive UI integration.
    *   `views/`: Page-level Vue components mapped to routes.
        *   `BrowseOffersView.vue`: Main offer browsing interface with filtering and real-time updates.
        *   `CreateOfferView.vue`: Offer creation form with validation and phone verification requirements.
        *   `EditOfferView.vue`: Offer editing interface with pre-populated data.
        *   `HomeView.vue`: Main dashboard after authentication with overview of user activity.
        *   `LandingView.vue`: Initial page for unauthenticated users with app introduction.
        *   `LoginView.vue`: User login form with error handling and redirects.
        *   `MyOffersView.vue`: User's own offers management with received interests and actions.
        *   `NotFoundView.vue`: 404 error page for invalid routes.
        *   `ProfileView/`: User profile management directory.
            *   `ProfileView.vue`: Main profile management interface.
            *   `UserOffers.vue`: User's offer history within profile context.
            *   `UserProfileCard.vue`: Profile information display and editing.
            *   `UserStats.vue`: User statistics including reputation and transaction history.
        *   `RegisterView.vue`: User registration form with email verification flow.
        *   `VerifyEmailView.vue`: Email verification completion page.
    *   `test/` / `__tests__/`: Unit tests for components, stores, services, and utilities using Vitest with comprehensive test coverage.

## Focus

*   This project has evolved from an MVP ("Bronze Level") into a **mature, production-ready P2P currency exchange platform** with sophisticated features and robust architecture.
*   **Core MVP Features (100% Complete):** User authentication with email/phone verification, comprehensive offer management, dynamic tiered pricing based on reputation, intelligent offer discovery, seamless P2P connection, and secure real-time chat.
*   **Advanced Features (Fully Implemented):** 
    *   **Enhanced Transaction Flow System:** Complete multi-stage guided transaction process with payment declarations, confirmations, tracking numbers, and timed windows.
    *   **Payer Negotiation System:** Sophisticated first-payer designation with payment readiness gates, system recommendations, and conflict resolution.
    *   **Real-time Notification System:** Comprehensive persistent notifications with Socket.IO integration for all transaction updates, interest processing, and offer events.
    *   **Centralized Socket Management:** Production-ready single socket connection system preventing conflicts and ensuring reliable real-time updates across all features.
    *   **Full Internationalization:** Complete i18n support for Persian (FA) and English (EN) with dynamic language switching.
    *   **Mobile-Responsive Design:** Optimized mobile experience with touch-friendly interfaces and responsive layouts.
*   **Production Features:** Advanced matchmaking algorithms, secure multi-party chat, comprehensive transaction flow management, countdown/elapsed timers, automated dispute resolution, real-time notification system, reputation-based dynamic pricing, and mobile-first responsive design.

## Development Workflow & Scripts

*   **Development Environment Setup:** Use the provided PowerShell scripts for consistent development workflow:
    *   `start-dev.ps1`: Starts PostgreSQL container and provides instructions for backend/frontend setup
    *   `stop-dev.ps1`: Cleanly stops development environment
    *   `switch-to-postgres.ps1` / `switch-to-sqlite.ps1`: Switch between database environments
*   **Development Servers:** 
    *   Backend runs on `http://localhost:3000` (Hono with hot reload)
    *   Frontend runs on `http://localhost:5173` (Vite with hot reload)
    *   PostgreSQL development container on `localhost:5433`
*   **Testing:** Comprehensive unit testing guidelines are available in `C:\Code\MUNygo\frontend\unit.md` with validated patterns for Vue components, Pinia stores, API interactions, and the complete tech stack. **These guidelines have been proven through practical implementation and must be followed for all test creation.**

## Test Creation Guidelines

When creating or updating tests for the frontend application:

1. **Follow the Validated Testing Guidelines**: Always reference `C:\Code\MUNygo\frontend\unit.md` for proven testing patterns specific to our Vue 3 + TypeScript + Pinia + Naive UI stack.

2. **Test Philosophy**: Focus on user-observable behavior rather than implementation details. Test what users can see and interact with, not internal component methods or third-party library internals.

3. **Essential Test Setup**: Use the proper Vitest configuration with happy-dom environment, comprehensive Naive UI component stubbing, and browser API mocks as specified in the guidelines.

4. **Component Testing**: Test props, events, user interactions, and conditional rendering using data-testid attributes for reliable element selection.

5. **Store Testing**: Use proper store isolation with createTestingPinia and test actions, getters, and state changes independently.

6. **Service Testing**: Mock API calls completely and test error handling scenarios with correct response formats.

7. **Async Testing**: Properly handle async operations with await patterns, fake timers, and Vue reactivity updates.

8. **Avoid Legacy Patterns**: Do not copy existing test patterns that may be flawed. Use the established guidelines to create maintainable, reliable tests.

## Recent Critical Implementations & Architecture Notes

*   **Centralized Socket Management:** The application uses a singleton `centralizedSocketManager.ts` to prevent socket connection conflicts. All stores must use this centralized manager instead of creating independent socket connections. This is CRITICAL for real-time UI updates.
*   **Timer System:** Transaction flow includes both countdown timers (for payment windows) and elapsed timers (for confirmation steps) with proper visual indicators and expiration handling.
*   **Mobile Responsiveness:** Components should follow mobile-first design with proper responsive breakpoints. Use Naive UI's responsive features and CSS media queries for optimal mobile experience.
*   **Payment Persistence:** The PaymentReadinessGate component properly persists payment information with `saveToProfile: true` and `isDefaultForUser` flags for user convenience.

## Useful context

* For unit testing, use `C:\Code\MUNygo\frontend\unit.md` instructions for reference. These guidelines have been validated through practical implementation and contain proven best practices and lessons learned for the MUNygo testing environment.

## PowerShell Command Guidelines

* All terminal commands must be compatible with PowerShell on Windows.
* Do **not** use `&&` to chain commands. Use semicolons (`;`) or write each command on a new line.
* Always use backslashes (`\`) for file paths.
* For example, to change directory and add a file with git:
  ```
  cd C:\Code\MUNygo
  git add .\frontend\src\stores\offerStore.ts
  ```
  or
  `cd C:\Code\MUNygo; git add .\frontend\src\stores\offerStore.ts`
````