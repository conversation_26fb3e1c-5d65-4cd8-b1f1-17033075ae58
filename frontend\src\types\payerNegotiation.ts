export enum ReceivingInfoStatus {
  PENDING_INPUT = 'PENDING_INPUT',
  PROVIDED = 'PROVIDED',
  CONFIRMED_FROM_PROFILE = 'CONFIRMED_FROM_PROFILE'
}

export enum NegotiationStatus {
  AWAITING_PARTY_A_RECEIVING_INFO = 'AWAITING_PARTY_A_RECEIVING_INFO',
  AWAITING_PARTY_B_RECEIVING_INFO = 'AWAITING_PARTY_B_RECEIVING_INFO',
  READY_TO_NEGOTIATE = 'READY_TO_NEGOTIATE',
  PENDING_RESPONSE = 'PENDING_RESPONSE',
  FINALIZED = 'FINALIZED',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED'
}

export interface PaymentReceivingInfo { // Represents a stored payment method entity
  id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  isDefaultForUser: boolean; // Indicates if this stored method is the user's default
}

export interface PaymentReceivingSubmitPayload { // Payload for submitting form data
  id?: string; 
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  saveToProfile?: boolean; 
  isDefaultForUser?: boolean; // When true, makes this the user's default payment info
}

export interface PayerNegotiation {
  negotiationId: string;
  transactionId: string;
  partyA_Id: string;
  partyB_Id: string;
  partyA_receivingInfoStatus: ReceivingInfoStatus;
  partyB_receivingInfoStatus: ReceivingInfoStatus;
  // Add payment receiving info for both parties if they are part of the payload
  partyA_PaymentReceivingInfo?: PaymentReceivingInfo | null;
  partyB_PaymentReceivingInfo?: PaymentReceivingInfo | null;
  systemRecommendedPayerId: string | null;
  systemRecommendationRule: string | null;
  systemRecommendationReason: string | null;
  systemRecommendationDetails: string | null; // Should be object or stringified JSON
  currentProposal_PayerId: string | null;
  currentProposal_ById: string | null;
  currentProposal_Message: string | null; // Ensure this is present
  partyA_agreedToCurrentProposal: boolean; // Changed from PayerId/ById to boolean flags
  partyB_agreedToCurrentProposal: boolean; // Changed from PayerId/ById to boolean flags
  finalizedPayerId: string | null;
  negotiationStatus: NegotiationStatus;
  paymentTimerDueDate: string | null; // ISO string
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}
