import { PrismaClient, Notification as PrismaNotification, NotificationType as PrismaNotificationType } from '@prisma/client';
import { Server as SocketIOServer } from 'socket.io';

const prisma = new PrismaClient();

// Define or import CreateNotificationInput ensuring it uses PrismaNotificationType
export interface CreateNotificationInput {
  userId: string;
  type: PrismaNotificationType; // Use Prisma's enum here
  message: string;
  relatedEntityType?: string | null;
  relatedEntityId?: string | null;
  actorId?: string | null;
  actorUsername?: string | null;
  data?: string | null; // 🔧 Fixed to match Prisma schema (String?)
}

export class NotificationService {
  private io: any; // Consider using a more specific type for your Socket.IO server instance

  constructor(io?: any) {
    this.io = io;
  }

  /**
   * Creates a new notification and saves it to the database.
   * Optionally emits a socket event to the user if they are connected.
   * @param input - Data for the new notification.
   * @returns The created notification.
   */
  async createNotification(input: CreateNotificationInput): Promise<PrismaNotification> {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId: input.userId,
          type: input.type, // This now correctly expects PrismaNotificationType
          message: input.message,
          relatedEntityType: input.relatedEntityType,
          relatedEntityId: input.relatedEntityId,
          actorId: input.actorId,
          actorUsername: input.actorUsername,
          data: input.data ? JSON.stringify(input.data) : null, // 🔧 Stringify the data object
        },
      });

      // Emit a socket event to the specific user if the io server is available
      // Assumes the user is in a room named after their userId
      if (this.io) {
        this.io.to(input.userId).emit('NEW_NOTIFICATION', notification);
        console.log(`[NotificationService] Emitted NEW_NOTIFICATION to userId: ${input.userId}`);
      }
      
      console.log(`[NotificationService] Notification created: ${notification.id} for user ${input.userId}`);
      return notification;
    } catch (error: any) {
      console.error('[NotificationService] Error creating notification:', error);
      // It's good practice to throw a new error or a more specific custom error
      throw new Error(`Failed to create notification. Details: ${error.message}`);
    }
  }

  /**
   * Retrieves notifications for a specific user.
   * @param userId - The ID of the user.
   * @param options - Options for pagination and filtering (e.g., limit, offset, unreadOnly).
   * @returns A list of notifications.
   */
  async getNotificationsForUser(
    userId: string,
    options: { limit?: number; offset?: number; unreadOnly?: boolean } = {}
  ): Promise<PrismaNotification[]> {
    const { limit = 20, offset = 0, unreadOnly = false } = options;
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          userId: userId,
          ...(unreadOnly && { isRead: false }),
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      return notifications;
    } catch (error) {
      console.error(`[NotificationService] Error fetching notifications for user ${userId}:`, error);
      throw new Error('Failed to fetch notifications.');
    }
  }

  /**
   * Marks a single notification as read.
   * @param notificationId - The ID of the notification to mark as read.
   * @param userId - The ID of the user who owns the notification (for verification).
   * @returns The updated notification or null if not found or not owned by the user.
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<PrismaNotification | null> {
    try {
      const notification = await prisma.notification.findUnique({
        where: { id: notificationId },
      });

      if (!notification || notification.userId !== userId) {
        console.warn(`[NotificationService] Notification ${notificationId} not found or not owned by user ${userId}.`);
        return null; // Or throw an error indicating not found or forbidden
      }

      if (notification.isRead) {
        return notification; // Already read
      }

      const updatedNotification = await prisma.notification.update({
        where: { id: notificationId },
        data: { isRead: true },
      });
      console.log(`[NotificationService] Notification ${notificationId} marked as read for user ${userId}.`);
      return updatedNotification;
    } catch (error) {
      console.error(`[NotificationService] Error marking notification ${notificationId} as read:`, error);
      throw new Error('Failed to mark notification as read.');
    }
  }

  /**
   * Marks all unread notifications for a user as read.
   * @param userId - The ID of the user.
   * @returns An object with the count of notifications updated.
   */
  async markAllNotificationsAsRead(userId: string): Promise<{ count: number }> {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId: userId,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      });
      console.log(`[NotificationService] Marked ${result.count} notifications as read for user ${userId}.`);
      return { count: result.count };
    } catch (error) {
      console.error(`[NotificationService] Error marking all notifications as read for user ${userId}:`, error);
      throw new Error('Failed to mark all notifications as read.');
    }
  }
}
