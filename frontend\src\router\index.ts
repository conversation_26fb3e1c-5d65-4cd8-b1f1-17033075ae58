import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useClientLogger } from '../composables/useClientLogger';
import { checkAdminAccess } from '../utils/adminUtils';

const routes = [
  {
    path: '/browse-offers',
    name: 'BrowseOffers',
    component: () => import('../views/BrowseOffersView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Browse Offers - Arz.Ani'
    }
  },
  {
    path: '/my-offers/:offerId/edit',
    name: 'EditOffer',
    component: () => import('../views/EditOfferView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Edit Offer - Arz.Ani'
    }
  },
  {
    path: '/my-offers',
    name: 'MyOffers',
    component: () => import('../views/MyOffersView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'My Offers - Arz.Ani'
    }
  },
  {
    path: '/create-offer',
    name: 'CreateOffer',
    component: () => import('../views/CreateOfferView.vue'),
    meta: { 
      requiresAuth: true, 
      requiresPhoneVerified: true,
      title: 'Create Offer - Arz.Ani'
    }
  },  
  {
    path: '/',
    name: 'landing',
    component: () => import('../views/LandingView.vue'),
    meta: { 
      public: true,
      title: 'Welcome to Arz.Ani'
    }
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('../views/HomeView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Home - Arz.Ani'
    }
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('../views/ProfileView/ProfileView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Profile - Arz.Ani'
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/RegisterView.vue'),
    meta: { title: 'Register - Arz.Ani' }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue'),
    meta: { title: 'Login - Arz.Ani' }  },
  {
    path: '/verify-email',
    name: 'verify-email',
    component: () => import('../views/VerifyEmailView.vue'),
    meta: { title: 'Verify Email - Arz.Ani' }
  },
  {
    path: '/offers/:id',
    name: 'OfferDetails',
    component: () => import('../views/OfferDetailsView.vue'),
    meta: { 
      requiresAuth: true,
      title: 'Offer Details - Arz.Ani'
    }
  },
  {
    path: '/chat/:chatSessionId',
    name: 'ChatSession',
    component: () => import('../views/ChatView.vue'), // Assuming ChatView.vue will be created
    meta: { 
      requiresAuth: true,
      title: 'Chat Session - Arz.Ani'
    }
  },  {
    path: '/admin/debug-dashboard',
    name: 'AdminDebugDashboard',
    component: () => import('../views/admin/DebugDashboardViewCustom.vue'),
    meta: { 
      requiresAuth: true,
      requiresAdmin: true,
      title: 'Debug Dashboard - Arz.Ani Admin'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFoundView.vue'),
    meta: { 
      title: 'Page Not Found - Arz.Ani'
    }
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
});

// Navigation guards with logging
router.beforeEach((to, from, next) => {
  // Initialize logger inside the guard to avoid circular dependencies
  const logger = useClientLogger();
  
  // Log route navigation
  logger.logInfo('Route navigation started', {
    from: from.fullPath,
    to: to.fullPath,
    routeName: to.name as string,
    requiresAuth: to.matched.some((record: any) => record.meta && record.meta.requiresAuth),
    requiresPhoneVerified: to.matched.some((record: any) => record.meta && record.meta.requiresPhoneVerified),
  });

  // Set the document title if specified in the route meta
  document.title = to.meta.title as string || 'Arz.Ani';

  const authStore = useAuthStore();
  const requiresAuth = to.matched.some((record: any) => record.meta && record.meta.requiresAuth);
  const isAuthenticated = authStore.isAuthenticated;

  // If route requires auth and not authenticated, go to landing
  if (requiresAuth && !isAuthenticated) {
    logger.logWarn('Route access denied - authentication required', {
      route: to.fullPath,
      isAuthenticated: false,
    });
    next({ name: 'landing' });
    return;
  }
    // If route requires phone verification and user is not phone-verified, redirect to profile
  if (to.matched.some((record: any) => record.meta && record.meta.requiresPhoneVerified)) {
    if (!authStore.user?.phoneVerified) {
      logger.logWarn('Route access denied - phone verification required', {
        route: to.fullPath,
        phoneVerified: false,
        userId: authStore.user?.id,
      });
      next({ name: 'profile', query: { phoneRequired: '1' } });
      return;
    }
  }

  // If route requires admin access and user is not admin, redirect to home
  if (to.matched.some((record: any) => record.meta && record.meta.requiresAdmin)) {
    if (!checkAdminAccess(authStore.user?.email)) {
      logger.logWarn('Route access denied - admin privileges required', {
        route: to.fullPath,
        userEmail: authStore.user?.email,
        isAdmin: false,
      });
      next({ name: 'home' });
      return;
    }
  }
  
  // If already logged in and trying to access auth pages, redirect to home
  // But allow access to login/register/landing if coming from a logout (no auth token)
  if ((to.name === 'login' || to.name === 'register' || to.name === 'landing') && isAuthenticated) {
    // If already logged in, redirect to home from login/register/landing
    logger.logInfo('Redirecting authenticated user from auth page to home', {
      attemptedRoute: to.name as string,
      isAuthenticated: true,
    });
    next({ name: 'home' });
    return;
  }
  
  next();
});

// Log successful navigation completion
router.afterEach((to, from) => {
  // Initialize logger for afterEach hook
  const logger = useClientLogger();
  
  // Use enhanced navigation logging
  logger.logNavigation(to.fullPath, from.fullPath);
  
  logger.logInfo('Route navigation completed', {
    from: from.fullPath,
    to: to.fullPath,
    routeName: to.name as string,
  });
});

// Log navigation errors
router.onError((error) => {
  // Initialize logger for error hook
  const logger = useClientLogger();
  
  logger.logError('Router navigation error', error, {
    errorType: 'ROUTER_ERROR',
  });
});

export default router;
