import { createI18n } from 'vue-i18n'
import type { Language } from '@/stores/language'

// Import locale files
import fa from '@/locales/fa.json'
import en from '@/locales/en.json'

export type MessageSchema = typeof fa

const messages = {
  fa,
  en
}

// Create i18n instance
export const i18n = createI18n<[MessageSchema], Language>({
  legacy: false, // Use Composition API
  locale: 'fa', // Default language
  fallbackLocale: 'en',
  messages,
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
  warnHtmlMessage: false
})

export default i18n

// Export types for better TypeScript support
export type AvailableLocale = keyof typeof messages
export type LocaleMessage = typeof messages['fa']
