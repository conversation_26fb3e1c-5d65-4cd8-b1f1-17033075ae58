import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// Helper function to get a date N days ago
function daysAgo(days: number): Date {
  return new Date(Date.now() - days * 24 * 60 * 60 * 1000);
}

// Helper function to calculate reputation level based on score
// This should ideally match the logic in your application (e.g., backend/src/routes/auth.ts)
function calculateReputationLevel(score: number): number {
  if (score < 10) return 1; // Newcomer
  if (score < 25) return 2; // Verified
  if (score < 40) return 3; // Reliable
  if (score < 60) return 4; // Trusted
  return 5; // Elite
}

async function main() {
  const saltRounds = 10;
  const commonPassword = '********';
  const hashedPassword = await bcrypt.hash(commonPassword, saltRounds);

  const usersToSeed = [
    {
      email: '<EMAIL>',
      username: 'h',
      password: hashedPassword,
      emailVerified: true,
      phoneNumber: '+***********', // Fake phone number
      phoneVerified: true,
      createdAt: daysAgo(40), // Account older than 30 days for higher reputation potential
      // For Level 5, we ensure criteria are met and set a high score
      // (emailVerified: +10, phoneVerified: +15, age (capped at 30 days): +30 = 55 base)
      // We'll set score to 100 to ensure Level 5, similar to setTestUserLevel.ts
      reputationScore: 100,
      get reputationLevel() { return calculateReputationLevel(this.reputationScore); },
    },
    {
      email: '<EMAIL>',
      username: 'h2',
      password: hashedPassword,
      emailVerified: true, // Assuming email should be verified for usability
      phoneNumber: '+***********', // Different fake phone number
      phoneVerified: true,
      createdAt: daysAgo(2), // A recent account, 2 days old
      // Score: emailVerified: +10, phoneVerified: +15, age: +2 = 27
      get reputationScore() {
        let score = 0;
        if (this.emailVerified) score += 10;
        if (this.phoneVerified) score += 15;
        const accountAgeInDays = Math.floor((Date.now() - new Date(this.createdAt).getTime()) / (1000 * 60 * 60 * 24));
        score += Math.min(accountAgeInDays, 30);
        return score;
      },
      get reputationLevel() { return calculateReputationLevel(this.reputationScore); },
    },
  ];

  console.log('Starting to seed test users...');

  for (const userData of usersToSeed) {
    // Access getters to resolve score and level before DB operation
    const finalReputationScore = userData.reputationScore;
    const finalReputationLevel = userData.reputationLevel;

    try {
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: {
          password: userData.password,
          username: userData.username,
          emailVerified: userData.emailVerified,
          phoneNumber: userData.phoneNumber,
          phoneVerified: userData.phoneVerified,
          createdAt: userData.createdAt, // Keep original creation date on update if desired
          reputationScore: finalReputationScore,
          reputationLevel: finalReputationLevel,
          verificationToken: null, // Clear verification token if any
          updatedAt: new Date(),
        },
        create: {
          email: userData.email,
          password: userData.password,
          username: userData.username,
          emailVerified: userData.emailVerified,
          phoneNumber: userData.phoneNumber,
          phoneVerified: userData.phoneVerified,
          createdAt: userData.createdAt,
          reputationScore: finalReputationScore,
          reputationLevel: finalReputationLevel,
          verificationToken: null,
        },
      });
      console.log(`Successfully upserted user: ${user.email} (ID: ${user.id}) with Reputation Score: ${user.reputationScore}, Level: ${user.reputationLevel}`);
    } catch (error) {
      console.error(`Failed to upsert user ${userData.email}:`, error);
    }
  }

  console.log('Test user seeding process complete.');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Seeding script failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });