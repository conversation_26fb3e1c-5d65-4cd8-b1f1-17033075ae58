<template>
  <n-config-provider :theme="themeStore.naiveTheme" :theme-overrides="themeStore.themeOverrides">
    <n-global-style />
    <n-dialog-provider>
      <n-message-provider>
        <n-notification-provider :placement="notificationPlacement">
          <AppContent />
        </n-notification-provider>
      </n-message-provider>
    </n-dialog-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NGlobalStyle
} from 'naive-ui';
import AppContent from '@/components/AppContent.vue';
import { watch, onMounted, computed } from 'vue';
import { useRtl } from '@/utils/rtl';
import { useThemeStore } from '@/stores/theme';

const { direction } = useRtl();
const themeStore = useThemeStore();

// Compute notification placement based on direction
const notificationPlacement = computed(() => {
  return direction.value === 'rtl' ? 'top-left' : 'top-right';
});

// Initialize theme on app start
onMounted(() => {
  themeStore.initializeTheme();
});

// Update document direction when language changes
watch(direction, (newDir) => {
  document.documentElement.setAttribute('dir', newDir);
}, { immediate: true });
</script>

<style>
/* Global styles from original App.vue can remain here or be moved if preferred */
.n-layout-header {
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.n-layout-content {
  margin-top: 64px;
  min-height: calc(100vh - 64px);
}

html, body, #app {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.main-content {
  height: calc(100vh - 64px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>

<style scoped>
/* Scoped styles from original App.vue can remain here or be moved if preferred */
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 64px;
}

.header-left {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 16px;
}
</style>
