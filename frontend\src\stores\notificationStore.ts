// src/stores/notificationStore.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import apiClient from '@/services/apiClient';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { NEW_NOTIFICATION } from '@/types/socketEvents';

// Corresponds to the backend's Notification model and NotificationType enum
// Ensure these NotificationType values match exactly with your backend Prisma enum
export enum FrontendNotificationType {
  NEW_INTEREST_ON_YOUR_OFFER = 'NEW_INTEREST_ON_YOUR_OFFER',
  YOUR_INTEREST_ACCEPTED = 'YOUR_INTEREST_ACCEPTED',
  YOUR_INTEREST_DECLINED = 'YOUR_INTEREST_DECLINED',
  CHAT_MESSAGE_RECEIVED = 'CHAT_MESSAGE_RECEIVED', // If you handle chat notifications here
  OFFER_STATUS_UPDATED_BY_OWNER = 'OFFER_STATUS_UPDATED_BY_OWNER',
  OFFER_STATUS_CHANGED = 'OFFER_STATUS_CHANGED',
  // Add other types as needed
}

export interface FrontendNotification {
  id: string; // Notification ID
  userId: string; // Recipient
  type: FrontendNotificationType;
  message: string;
  isRead: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string

  relatedEntityType?: string;
  relatedEntityId?: string;
  actorId?: string;
  actorUsername?: string;
  data?: Record<string, any>; // For additional context
}

export const useNotificationStore = defineStore('notificationStore', () => {
  const notifications = ref<FrontendNotification[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const lastFetchedTimestamp = ref<number | null>(null);

  // Socket event unsubscribe function for cleanup
  let newNotificationUnsubscribe: (() => void) | null = null;

  const unreadNotificationsCount = computed(() => {
    return notifications.value.filter(n => !n.isRead).length;
  });

  async function fetchNotifications(options: { limit?: number; unreadOnly?: boolean } = {}) {
    console.log('[NotificationStore] Fetching notifications with options:', options);
    isLoading.value = true;
    error.value = null;
    try {
      const response = await apiClient.get<FrontendNotification[]>('/notifications', {
        params: {
          limit: options.limit || 20, // Default limit
          unreadOnly: options.unreadOnly === undefined ? true : options.unreadOnly, // Default to unread only
          // offset can be added if pagination beyond 'load more' or initial fetch is needed
        },
      });
      if (response.data && Array.isArray(response.data)) {
        // Replace or merge strategies can be chosen. Replacing for simplicity here.
        notifications.value = response.data;
        lastFetchedTimestamp.value = Date.now();
        console.log(`[NotificationStore] Fetched ${response.data.length} notifications.`);
      } else {
        notifications.value = [];
        console.warn('[NotificationStore] No data or invalid data format received.');
      }
    } catch (err: any) {
      console.error('[NotificationStore] Failed to fetch notifications:', err);
      error.value = err.response?.data?.message || err.message || 'Failed to fetch notifications.';
    } finally {
      isLoading.value = false;
    }
  }

  async function markNotificationAsRead(notificationId: string) {
    console.log(`[NotificationStore] Marking notification ${notificationId} as read.`);
    try {
      const response = await apiClient.post<FrontendNotification>(`/notifications/${notificationId}/mark-read`);
      // Only update local state if API call succeeds
      const index = notifications.value.findIndex(n => n.id === notificationId);
      if (index !== -1 && response.data) {
        notifications.value[index].isRead = true;
        // Optionally, re-sort or move read notifications
      }
      console.log(`[NotificationStore] Notification ${notificationId} marked as read.`);
    } catch (err: any) {
      console.error(`[NotificationStore] Failed to mark notification ${notificationId} as read:`, err);
      error.value = err.response?.data?.message || err.message || 'Failed to mark as read.';
      // Don't update local state on failure - this fixes the optimistic update issue
    }
  }

  async function markAllNotificationsAsRead() {
    console.log('[NotificationStore] Marking all notifications as read.');
    try {
      await apiClient.post<{ count: number }>('/notifications/mark-all-read');
      // Only update local state if API call succeeds
      notifications.value.forEach(n => n.isRead = true);
      console.log('[NotificationStore] All notifications marked as read locally.');
    } catch (err: any) {
      console.error('[NotificationStore] Failed to mark all notifications as read:', err);
      error.value = err.response?.data?.message || err.message || 'Failed to mark all as read.';
      // Don't update local state on failure - this fixes the optimistic update issue
    }
  }

  function addOrUpdateNotification(notification: FrontendNotification) {
    console.log('[NotificationStore] addOrUpdateNotification called with:', JSON.stringify(notification));
    const existingIndex = notifications.value.findIndex(n => n.id === notification.id);
    if (existingIndex !== -1) {
      notifications.value[existingIndex] = notification;
      console.log(`[NotificationStore] Updated existing notification ${notification.id}.`);
    } else {
      notifications.value.unshift(notification); // Add new notifications to the top
      console.log(`[NotificationStore] Added new notification ${notification.id}.`);
    }

    if (notification.type === FrontendNotificationType.YOUR_INTEREST_DECLINED) {
      console.log(`[NotificationStore] Processed YOUR_INTEREST_DECLINED notification: ${notification.id}. Message: ${notification.message}`);
      // Log data associated with the decline for debugging
      if (notification.data) {
        console.log(`[NotificationStore] Decline data: offerId=${notification.data.offerId}, reasonCode=${notification.data.reasonCode}, declinedBy=${notification.data.declinedByUsername}`);
      }
    }

    // Automatically mark certain important notifications as read once received by the store
    // RESTORING THIS BLOCK
    /*
    if (
      (notification.type === FrontendNotificationType.YOUR_INTEREST_DECLINED ||
       notification.type === FrontendNotificationType.YOUR_INTEREST_ACCEPTED) &&
      !notification.isRead
    ) {
      // Call without awaiting to prevent blocking notification display flow.
      // Errors are logged by markNotificationAsRead itself.
      markNotificationAsRead(notification.id).catch(err => {
        console.error(`[NotificationStore] Auto-mark read failed for ${notification.id}:`, err);
      });
      // Optimistically update local state for immediate UI feedback
      const localNotif = notifications.value.find(n => n.id === notification.id);
      if (localNotif) {
        localNotif.isRead = true;
      }
    }
    */
    // Optional: sort notifications by date again if needed
    // notifications.value.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
  
  function removeNotificationById(notificationId: string) {
    notifications.value = notifications.value.filter(n => n.id !== notificationId);
    console.log(`[NotificationStore] Notification ${notificationId} removed.`);
  }

  function clearAllLocalNotifications() {
    notifications.value = [];
    lastFetchedTimestamp.value = null;
    console.log('[NotificationStore] All local notifications cleared.');
  }

  function initializeNotificationListeners() {
    console.log('[NotificationStore] Initializing notification listeners via centralized socket manager');
    
    // Clean up existing listener
    if (newNotificationUnsubscribe) {
      newNotificationUnsubscribe();
      newNotificationUnsubscribe = null;
    }

    // Register NEW_NOTIFICATION handler using the centralized socket manager
    newNotificationUnsubscribe = centralizedSocketManager.on(NEW_NOTIFICATION, (notification: FrontendNotification) => {
      console.log('[NotificationStore] Received NEW_NOTIFICATION via centralized manager:', notification);
      addOrUpdateNotification(notification);
      // Potentially trigger a UI update or a subtle alert
    });

    console.log('[NotificationStore] Socket listeners initialized via centralized manager.');
  }

  // Cleanup function for socket listeners
  function cleanupNotificationListeners() {
    if (newNotificationUnsubscribe) {
      newNotificationUnsubscribe();
      newNotificationUnsubscribe = null;
    }
  }

  // Auto-initialize socket listeners when store is created
  initializeNotificationListeners();
  
  // Call this when the store is initialized or when a user logs in
  // fetchNotifications({ unreadOnly: true }); // Example: fetch unread on init

  return {
    notifications,
    isLoading,
    error,
    unreadNotificationsCount,
    fetchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    addOrUpdateNotification, // Expose if needed externally, e.g. for testing
    removeNotificationById,
    clearAllLocalNotifications,
    initializeNotificationListeners, // To be called from appropriate Vue component, e.g. App.vue onMount
    cleanupNotificationListeners,
    lastFetchedTimestamp,
  };
});
