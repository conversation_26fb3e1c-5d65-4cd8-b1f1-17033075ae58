<template>
  <n-card :title="cardTitle" :bordered="false" class="transaction-flow-card-v1">
    <n-spin :show="isLoadingFromComposable && !isActionLoadingFromComposable">
      <!-- Error Display -->
      <n-alert v-if="errorFromComposable" title="Error" type="error" closable @close="errorFromComposable = null" class="error-message">
        {{ errorFromComposable }}
      </n-alert>

      <!-- Loading/Empty States -->
      <div v-if="isLoadingFromComposable && !currentTransactionFromComposable">
        <n-text>Loading transaction details...</n-text>
      </div>
      <div v-else-if="!currentTransactionFromComposable">
        <n-empty description="No transaction data available for this chat session." />
      </div>

      <!-- Transaction Content -->
      <div v-else>
        <!-- Transaction Details Grid -->
        <div class="transaction-details-grid">
          <span class="label">Status:</span>
          <span class="value">
            <n-tag :type="formattedStatus.type">{{ formattedStatus.text }}</n-tag>
          </span>

          <span class="label">Your Role:</span>
          <span class="value">{{ userRoleDisplay }}</span>

          <span class="label">Time Left:</span>
          <span class="value">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-text :class="{ 'critical-timer': isTimerCritical, 'expired-timer': isTimerExpired }">
                  {{ timeLeftFromComposable }}
                </n-text>
              </template>
              <span v-if="isTimerExpired">The time limit for the current action has expired.</span>
              <span v-else-if="isTimerCritical">The time limit for the current action is about to expire.</span>
              <span v-else>Time remaining for the current action.</span>
            </n-tooltip>
          </span>
        </div>

        <!-- Info Message Area -->
        <n-alert :title="infoMessageV1.title" :type="infoMessageV1.type" class="info-message-area">
          <template #icon><n-icon :component="infoMessageV1.icon" /></template>
          {{ infoMessageV1.message }}
        </n-alert>

        <!-- Steps -->
        <n-steps :current="currentStepIndex" :status="steps.find(s => s.status === 'error') ? 'error' : (steps.find(s => s.status === 'process') ? 'process' : 'finish')" vertical>
          <n-step
            v-for="step in steps" 
            :key="step.key"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          >
            <template #icon><n-icon :component="step.icon" /></template>
            <!-- Action buttons within steps -->
            <div v-if="step.action && isUserTurnFromComposable && statusFromComposable === step.activeOnStatus && !isCurrentActionBlockedByTimerComputed" class="step-actions">
              <n-button 
                v-if="step.action === 'agree'" 
                @click="handleAgreeToTerms" 
                type="primary" 
                size="small"
                :loading="isActionLoadingFromComposable === 'agree'"
                :disabled="isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'agree'"
              >
                Agree to Terms
              </n-button>
              <n-button 
                v-if="step.action === 'designate'" 
                @click="openDesignateModal" 
                type="primary" 
                size="small"
                :loading="isActionLoadingFromComposable === 'designate'"
                :disabled="isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'designate'"
              >
                Designate First Payer
              </n-button>
              <!-- V1 might need inline form for designation if no modal -->
              <div v-if="step.action === 'designate' && showDesignateModalRef" class="inline-form">
                <n-radio-group v-model:value="selectedFirstPayerIdRef" name="firstPayerV1">
                    <n-space>                        <n-radio :value="currentTransactionFromComposable?.currencyAProviderId">{{ currentTransactionFromComposable?.currencyAProvider?.username }}</n-radio>
                        <n-radio :value="currentTransactionFromComposable?.currencyBProviderId">{{ currentTransactionFromComposable?.currencyBProvider?.username }}</n-radio>
                    </n-space>
                </n-radio-group>
                <n-button @click="agreeToTermsFromComposable" :disabled="!selectedFirstPayerIdRef" size="small">Confirm Designation</n-button> 
              </div>

              <n-button 
                v-if="step.action === 'declarePayment'" 
                @click="openDeclarePaymentModal" 
                type="primary" 
                size="small"
                :loading="isActionLoadingFromComposable === 'declarePayment'"
                :disabled="isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'declarePayment'"
              >
                Declare Payment
              </n-button>
              <!-- V1 might need inline form for payment declaration -->
               <div v-if="step.action === 'declarePayment' && showDeclarePaymentModalRef" class="inline-form">
                <n-input v-model:value="paymentTrackingNumberRef" placeholder="Tracking number (optional)" size="small" />
                <n-button @click="declarePaymentFromComposable" size="small">Submit Declaration</n-button>
              </div>

              <n-button 
                v-if="step.action === 'confirmReceipt'" 
                @click="confirmReceiptFromComposable" 
                type="primary" 
                size="small"
                :loading="isActionLoadingFromComposable === 'confirmReceipt'"
                :disabled="isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'confirmReceipt'"
              >
                Confirm Receipt
              </n-button>
            </div>
          </n-step>
        </n-steps>

        <!-- General Actions (Cancel/Dispute) -->
        <n-collapse style="margin-top: 20px;">
          <n-collapse-item title="Other Actions" name="other-actions">
            <n-space>
              <n-button 
                @click="openCancelModal" 
                type="warning" 
                ghost 
                size="small"
                :disabled="!currentTransactionFromComposable || currentTransactionFromComposable.status === 'COMPLETED' || currentTransactionFromComposable.status === 'CANCELLED' || currentTransactionFromComposable.status === 'DISPUTED' || (isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'cancel')"
                :loading="isActionLoadingFromComposable === 'cancel'"
              >
                Cancel Transaction
              </n-button>
              <!-- V1 might need inline form for cancel -->
              <div v-if="showCancelModalRef" class="inline-form">
                  <n-input type="textarea" v-model:value="cancelReasonRef" placeholder="Reason for cancellation" size="small" />
                  <n-button @click="cancelTransactionFromComposable" :disabled="!cancelReasonRef?.trim()" type="warning" size="small">Confirm Cancel</n-button>
              </div>

              <n-button 
                @click="openDisputeModal" 
                type="error" 
                ghost 
                size="small"
                :disabled="!currentTransactionFromComposable || currentTransactionFromComposable.status === 'COMPLETED' || currentTransactionFromComposable.status === 'CANCELLED' || currentTransactionFromComposable.status === 'DISPUTED' || (isActionLoadingFromComposable !== null && isActionLoadingFromComposable !== 'dispute')"
                :loading="isActionLoadingFromComposable === 'dispute'"
              >
                Dispute Transaction
              </n-button>
              <!-- V1 might need inline form for dispute -->
              <div v-if="showDisputeModalRef" class="inline-form">
                  <n-input type="textarea" v-model:value="disputeReasonRef" placeholder="Reason for dispute" size="small" />
                  <n-button @click="disputeTransactionFromComposable" :disabled="!disputeReasonRef?.trim()" type="error" size="small">Confirm Dispute</n-button>
              </div>
            </n-space>
          </n-collapse-item>
        </n-collapse>

        <!-- Transaction Log -->
        <n-collapse style="margin-top: 10px;">
          <n-collapse-item title="Transaction History" name="history">
            <TransactionTimelineLog :transaction="currentTransactionFromComposable" />
          </n-collapse-item>
        </n-collapse>
      </div>
    </n-spin>
  </n-card>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, toRefs } from 'vue'; // Removed defineComponent
import {
  NCard, NSpin, NAlert, NEmpty, NText, NTag, NTooltip, NIcon, NSteps, NStep, NButton, NCollapse, NCollapseItem, useMessage
} from 'naive-ui';
import {
  CheckmarkCircleOutline as CheckmarkIcon,
  CloseCircleOutline as CancelIcon,
  AlertCircleOutline as DisputeIcon,
  InformationCircleOutline as InfoIcon,
  TimeOutline as TimeIcon,
  ShieldCheckmarkOutline as ShieldIcon,
  DocumentTextOutline as DocumentIcon,
  HandRightOutline as HandIcon, // Changed from HandPaperOutline
  HourglassOutline as HourglassIcon,
  BuildOutline as BuildIcon,
} from '@vicons/ionicons5';
import { TransactionStatusEnum } from '@/types/transaction';
import TransactionTimelineLog from './TransactionTimelineLog.vue';

import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic';
import { useAuthStore } from '@/stores/auth'; // For userId

const props = defineProps<{
  chatSessionId: string | null;
}>();

const { chatSessionId: propChatSessionId } = toRefs(props);
const authStore = useAuthStore();

const naiveMessage = useMessage();

// --- Use Composable ---
const {
  currentTransaction: currentTransactionFromComposable,
  isLoading: isLoadingFromComposable,
  storeError: errorFromComposable, 
  timeLeft: timeLeftFromComposable,
  isTimerCritical,
  isTimerExpired,
  showDesignateModal: showDesignateModalRef, 
  selectedFirstPayerId: selectedFirstPayerIdRef,
  showDeclarePaymentModal: showDeclarePaymentModalRef,
  paymentTrackingNumber: paymentTrackingNumberRef,
  showCancelModal: showCancelModalRef,
  cancelReason: cancelReasonRef,
  showDisputeModal: showDisputeModalRef,
  disputeReason: disputeReasonRef,
  isActionLoading: isActionLoadingFromComposable,
  // Actions from composable
  handleDesignateFirstPayer: designateFirstPayerFromComposable, 
  handleDeclarePayment: declarePaymentFromComposable, 
  handleConfirmReceipt: confirmReceiptFromComposable,
  handleCancelTransaction: cancelTransactionFromComposable, 
  handleDisputeTransaction: disputeTransactionFromComposable, 
  isCurrentActionBlockedByTimer, // This is a ref from the composable
  // Modal control methods from composable
  openDesignateModal: openDesignateModalFromComposable, // Renamed to match composable export
  openDeclarePaymentModal: openDeclarePaymentModalFromComposable, 
  openCancelModal: openCancelModalFromComposable, 
  openDisputeModal: openDisputeModalFromComposable,
} = useTransactionFlowLogic(propChatSessionId, naiveMessage);


// --- V1 Specific Action Handlers (wrapping composable actions) ---
const handleAgreeToTerms = async () => {
  await agreeToTermsFromComposable();
};

const openDesignateModal = () => openDesignateModalFromComposable();
const handleDesignateFirstPayer = async () => {
  // The composable's handleDesignateFirstPayer already uses selectedFirstPayerIdRef
  await designateFirstPayerFromComposable(); 
};

const openDeclarePaymentModal = () => openDeclarePaymentModalFromComposable();
const handleDeclarePayment = async () => {
  // The composable's handleDeclarePayment already uses paymentTrackingNumberRef
  await declarePaymentFromComposable();
};

const handleConfirmReceipt = async () => {
  await confirmReceiptFromComposable();
};

const openCancelModal = () => openCancelModalFromComposable();
const handleCancelTransaction = async () => {
  // The composable's handleCancelTransaction already uses cancelReasonRef
  await cancelTransactionFromComposable(); 
};

const openDisputeModal = () => openDisputeModalFromComposable();
const handleDisputeTransaction = async () => {
  // The composable's handleDisputeTransaction already uses disputeReasonRef
  await disputeTransactionFromComposable();
};


// --- V1 Specific Computed Properties ---
const statusFromComposable = computed(() => currentTransactionFromComposable.value?.status);

const userRoleFromComposable = computed(() => {
  if (!currentTransactionFromComposable.value || !authStore.user?.id) return 'observer';
  if (authStore.user.id === currentTransactionFromComposable.value.currencyAProviderId) return 'currencyAProvider';
  if (authStore.user.id === currentTransactionFromComposable.value.currencyBProviderId) return 'currencyBProvider';
  return 'observer';
});

const cardTitle = computed(() => {
  if (!currentTransactionFromComposable.value) return "Transaction Details"; // Simplified for V1, or use a more specific title
  return `Transaction V1: ${currentTransactionFromComposable.value.id.substring(0, 8)}`;
});

interface StepDefinitionV1 {
  key: string; 
  title: string;
  description: string;
  status?: 'wait' | 'process' | 'finish' | 'error';
  icon?: any; 
  action?: 'agree' | 'designate' | 'declarePayment' | 'confirmReceipt' | null;
  activeOnStatus?: TransactionStatusEnum; 
}

const stepDefs = computed((): Record<string, StepDefinitionV1> => ({
  START: { key: 'START', title: 'Transaction Initiated', description: 'Preparing transaction details.', icon: DocumentIcon, status: 'process' },
  [TransactionStatusEnum.PENDING_AGREEMENT]: { 
    key: TransactionStatusEnum.PENDING_AGREEMENT, 
    title: 'Agree to Terms', 
    description: 'Both parties must agree to the transaction terms.', 
    icon: HandIcon, 
    action: 'agree',
    activeOnStatus: TransactionStatusEnum.PENDING_AGREEMENT,
  },
  [TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION]: { 
    key: TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION, 
    title: 'Designate First Payer', 
    description: 'One party needs to be designated to make the first payment.', 
    icon: HandIcon, 
    action: 'designate',
    activeOnStatus: TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION,
  },
  [TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT]: { 
    key: TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT, 
    title: 'Awaiting First Payment', 
    description: `Waiting for ${currentTransactionFromComposable.value?.agreedFirstPayerId === currentTransactionFromComposable.value?.currencyAProviderId ? currentTransactionFromComposable.value?.currencyAProvider?.username : currentTransactionFromComposable.value?.currencyBProvider?.username || 'first payer'} to pay.`, 
    icon: HourglassIcon,
    action: 'declarePayment', 
    activeOnStatus: TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT,
  },
  [TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION]: { 
    key: TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION, 
    title: 'Confirm First Payment', 
    description: `Waiting for ${currentTransactionFromComposable.value?.agreedFirstPayerId !== currentTransactionFromComposable.value?.currencyAProviderId ? currentTransactionFromComposable.value?.currencyAProvider?.username : currentTransactionFromComposable.value?.currencyBProvider?.username || 'second payer'} to confirm receipt.`, 
    icon: ShieldIcon,
    action: 'confirmReceipt', 
    activeOnStatus: TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION,
  },
  [TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT]: { 
    key: TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT, 
    title: 'Awaiting Second Payment', 
    description: `Waiting for ${currentTransactionFromComposable.value?.agreedFirstPayerId !== currentTransactionFromComposable.value?.currencyAProviderId ? currentTransactionFromComposable.value?.currencyAProvider?.username : currentTransactionFromComposable.value?.currencyBProvider?.username || 'second payer'} to pay.`, 
    icon: HourglassIcon,
    action: 'declarePayment', 
    activeOnStatus: TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT,
  },
  [TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION]: { 
    key: TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION, 
    title: 'Confirm Second Payment', 
    description: `Waiting for ${currentTransactionFromComposable.value?.agreedFirstPayerId === currentTransactionFromComposable.value?.currencyAProviderId ? currentTransactionFromComposable.value?.currencyAProvider?.username : currentTransactionFromComposable.value?.currencyBProvider?.username || 'first payer'} to confirm receipt.`, 
    icon: ShieldIcon,
    action: 'confirmReceipt', 
    activeOnStatus: TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION,
  },
  [TransactionStatusEnum.COMPLETED]: { key: TransactionStatusEnum.COMPLETED, title: 'Transaction Completed', description: 'Both payments made and confirmed.', icon: CheckmarkIcon, status: 'finish' },
  [TransactionStatusEnum.CANCELLED]: { key: TransactionStatusEnum.CANCELLED, title: 'Transaction Cancelled', description: `Reason: ${currentTransactionFromComposable.value?.cancellationReason || 'N/A'}`, icon: CancelIcon, status: 'error' },
  [TransactionStatusEnum.DISPUTED]: { key: TransactionStatusEnum.DISPUTED, title: 'Transaction Disputed', description: `Reason: ${currentTransactionFromComposable.value?.disputeReason || 'N/A'}`, icon: DisputeIcon, status: 'error' },
  UNKNOWN: { key: 'UNKNOWN', title: 'Unknown State', description: 'The transaction is in an unrecognized state.', icon: BuildIcon, status: 'error' }
}));

const steps = computed((): StepDefinitionV1[] => {
  if (!currentTransactionFromComposable.value || !statusFromComposable.value) return [stepDefs.value.START];

  const txStatus = statusFromComposable.value;
  const currentStepDefs = stepDefs.value; // Access .value for the computed ref
  const definedSteps: StepDefinitionV1[] = [];

  const orderedStatusKeys: TransactionStatusEnum[] = [
    TransactionStatusEnum.PENDING_AGREEMENT,
    TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION,
    TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT,
    TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION,
    TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT,
    TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION,
    TransactionStatusEnum.COMPLETED,
  ];
  
  if (txStatus === TransactionStatusEnum.COMPLETED) {
    return orderedStatusKeys.map(k => ({ ...currentStepDefs[k], status: 'finish' as 'finish' })).concat({ ...currentStepDefs[TransactionStatusEnum.COMPLETED], status: 'finish' as 'finish' });
  }
  if (txStatus === TransactionStatusEnum.CANCELLED) {
    const baseSteps = orderedStatusKeys.map(k => ({ ...currentStepDefs[k], status: 'wait' as 'wait' | 'process' | 'finish' | 'error'}));
    return baseSteps.concat({ ...currentStepDefs[TransactionStatusEnum.CANCELLED], status: 'error' as 'error'});
  }
  if (txStatus === TransactionStatusEnum.DISPUTED) {
    const baseSteps = orderedStatusKeys.map(k => ({ ...currentStepDefs[k], status: 'wait' as 'wait' | 'process' | 'finish' | 'error'}));
    return baseSteps.concat({ ...currentStepDefs[TransactionStatusEnum.DISPUTED], status: 'error' as 'error'});
  }

  let currentReached = false;
  for (const key of orderedStatusKeys) {
    const stepDef = currentStepDefs[key];
    if (!stepDef) continue;

    let stepStatusVal: 'wait' | 'process' | 'finish' | 'error' = 'wait';

    if (txStatus === key) {
      stepStatusVal = 'process';
      currentReached = true;
    } else if (!currentReached) {
      stepStatusVal = 'finish'; 
    } else {
      stepStatusVal = 'wait'; 
    }
    definedSteps.push({ ...stepDef, status: stepStatusVal });
  }
  
   if (!orderedStatusKeys.includes(txStatus as TransactionStatusEnum) && txStatus) {
    const unknownStepDef = currentStepDefs[txStatus] || currentStepDefs.UNKNOWN;
    definedSteps.push({ ...unknownStepDef, status: 'error' });
  }
  return definedSteps;
});

const currentStepIndex = computed(() => {
  if (!statusFromComposable.value) return 1;
  const activeStepIndex = steps.value.findIndex(s => s.status === 'process');
  if (activeStepIndex !== -1) return activeStepIndex + 1;

  if (statusFromComposable.value === TransactionStatusEnum.COMPLETED) return steps.value.length;
  if (statusFromComposable.value === TransactionStatusEnum.CANCELLED || statusFromComposable.value === TransactionStatusEnum.DISPUTED) {
    const terminalStepIndex = steps.value.findIndex(s => s.key === statusFromComposable.value);
    return terminalStepIndex !== -1 ? terminalStepIndex + 1 : steps.value.length;
  }
  
  const currentStatusStepIndex = steps.value.findIndex(s => s.key === statusFromComposable.value);
  return currentStatusStepIndex !== -1 ? currentStatusStepIndex + 1 : steps.value.length;
});

const activeStepBeforeTerminalState = computed(() => {
    if (statusFromComposable.value === TransactionStatusEnum.COMPLETED || 
        statusFromComposable.value === TransactionStatusEnum.CANCELLED || 
        statusFromComposable.value === TransactionStatusEnum.DISPUTED) {
        return steps.value.length;
    }
    return currentStepIndex.value;
});

const infoMessageV1 = computed(() => {
  if (!currentTransactionFromComposable.value || !statusFromComposable.value) return { title: "Status", message: "Loading transaction details...", type: 'info' as const, icon: InfoIcon };
  
  const tx = currentTransactionFromComposable.value;
  const currentStatus = statusFromComposable.value;
  const currentStepDefs = stepDefs.value; // Access .value

  if (currentStatus === TransactionStatusEnum.COMPLETED) return { title: "Completed", message: "This transaction is complete.", type: 'success' as const, icon: CheckmarkIcon };
  if (currentStatus === TransactionStatusEnum.CANCELLED) return { title: "Cancelled", message: `Transaction cancelled. Reason: ${tx.cancellationReason || 'Not specified'}.`, type: 'warning' as const, icon: CancelIcon };
  if (currentStatus === TransactionStatusEnum.DISPUTED) return { title: "Disputed", message: `Transaction disputed. Reason: ${tx.disputeReason || 'Not specified'}. Admin will review.`, type: 'error' as const, icon: DisputeIcon };

  if (isUserTurnFromComposable.value) {
    return { title: "Your Action Needed", message: `It's your turn to act. Current step: ${stepDefs.value[currentStatus as string]?.title || currentStatus}.`, type: 'info' as const, icon: InfoIcon };
  }
  return { title: "Waiting", message: `Waiting for the other party. Current step: ${stepDefs.value[currentStatus as string]?.title || currentStatus}.`, type: 'info' as const, icon: TimeIcon };
});

const userRoleDisplay = computed(() => {
  if (userRoleFromComposable.value === 'currencyAProvider') return "You are Provider A";
  if (userRoleFromComposable.value === 'currencyBProvider') return "You are Provider B";
  if (userRoleFromComposable.value === 'observer') return "You are observing";
  return "Your Role: Unknown";
});

const formattedStatus = computed(() => {
  if (!statusFromComposable.value) return { text: "Loading...", type: 'default' as const };
  const currentStatusKey = statusFromComposable.value as keyof typeof stepDefs.value;
  const stepDef = stepDefs.value[currentStatusKey];
  let type: 'default' | 'success' | 'error' | 'warning' | 'info' = 'default';
  if (stepDef?.status === 'finish') type = 'success';
  else if (stepDef?.status === 'error') type = 'error';
  else if (stepDef?.status === 'process') type = 'info';
  else if (currentStatusKey === TransactionStatusEnum.CANCELLED) type = 'warning';
  else if (currentStatusKey === TransactionStatusEnum.DISPUTED) type = 'error';

  return {
    text: stepDef?.title || statusFromComposable.value.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase()),
    type: type
  };
});

const isCurrentActionBlockedByTimerComputed = computed(() => {
  return isCurrentActionBlockedByTimer.value;
});

// V1 specific onMounted/onUnmounted if needed
onMounted(() => {
  console.log("TransactionFlowCard V1 Mounted with chatSessionId:", propChatSessionId.value);
});

onUnmounted(() => {
  console.log("TransactionFlowCard V1 Unmounted");
});

</script>

<style scoped>
.transaction-flow-card-v1 {
  margin-bottom: 20px;
}
.error-message {
  margin-bottom: 15px;
}
.info-message-area {
  margin-top: 15px;
  margin-bottom: 15px;
}
.step-actions {
  margin-top: 10px;
  margin-bottom: 10px;
}
.transaction-details-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px 16px;
  align-items: center;
  margin-bottom: 16px;
}
.transaction-details-grid .label {
  font-weight: bold;
  /* text-align: right; */ /* Optional: if you want labels right-aligned */
}
.transaction-details-grid .value {
  text-align: left;
}

.critical-timer {
  color: red;
  font-weight: bold;
}
.expired-timer {
  color: orange;
  font-weight: bold;
}

.inline-form {
    margin-top: 8px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #f9f9f9;
}
.inline-form .n-input, .inline-form .n-radio-group {
    margin-bottom: 8px;
}

</style>
