<template>
  <NCard v-if="currentTransaction" class="transaction-flow-card-v3" :bordered="false">
    <div class="card-header">
      <h2>{{ headerTitle }}</h2>
      <span class="transaction-id">{{ t('transactionFlow.transactionId') }}: {{ transactionIdDisplay }}</span>
    </div>    <div class="user-info">
      <span>Your Role: <strong>{{ currentUserRoleDisplay }}</strong></span>
      <span>{{ otherUserDisplayName }}: <strong>{{ otherUserRoleDisplay }}</strong></span>
    </div>

    <!-- Vertical Transaction Stepper -->
    <div class="stepper">
      <NSteps
        :current="currentStepIndex"
        :status="currentStepStatus"
        vertical
        size="small"
        style="max-width: 100%;"
      >
        <NStep
          v-for="(step, index) in visualSteps"
          :key="index"
          :title="step.title"
          :description="step.description"
          :status="stepStatuses[index]"
        >
          <template #icon>
            <div class="step-icon-custom">{{ step.icon }}</div>
          </template>
        </NStep>
      </NSteps>
    </div>

    <!-- Current Step Details -->
    <div class="current-step-details">
      <h3 v-html="currentActionTitle"></h3>
      <div class="info-text" v-html="currentActionInfoText"></div>

      <!-- Timer -->
      <div v-if="timerDisplayValue" class="timer-area" :class="{ 
        'critical': isTimerCritical, 
        'info': !isTimerCritical && !isTimerExpired && !isElapsedTimer,
        'expired': isTimerExpired,
        'elapsed': isElapsedTimer
      }">
        <div class="timer-content">
          <span class="timer-label">{{ timerLabel }}</span>
          <span class="timer-value" :class="{ 
            'timer-critical': isTimerCritical, 
            'timer-expired': isTimerExpired,
            'timer-elapsed': isElapsedTimer
          }">
            {{ timerDisplayValue }}
          </span>
        </div>
      </div>

      <!-- Payment Readiness Gate -->
      <PaymentReadinessGate
        v-if="showPaymentReadinessGate"
        :transaction-id="currentTransaction.id"
        :user-profile-details="userProfileDetailsForGate"
        :current-status="currentUserReceivingInfoStatus"
        @submit="handleReceivingInfoSubmit"
        @use-profile="handleUseProfileDetails"
        style="margin-bottom: 15px;"
      />      <!-- Negotiation UI (only if payment gate passed and ready to negotiate) -->
      <div v-if="canNegotiate" class="negotiation-ui">
        <!-- System Recommendation (only when it's a system recommendation) -->
        <div v-if="payerNegotiationStore.currentNegotiation?.currentProposal_ById === 'system'" class="recommendation-section">
          <div class="recommendation-header">
            <h4 class="recommendation-title">{{ t('transactionFlow.negotiation.systemProposal') }}</h4>
          </div>
          <div class="recommendation-content">
            <p class="recommendation-text" v-html="systemRecommendationDisplay"></p>
          </div>
        </div>

        <!-- User Proposal (only when it's not a system recommendation) -->
        <div v-else-if="payerNegotiationStore.currentNegotiation?.currentProposal_ById && payerNegotiationStore.currentNegotiation?.currentProposal_ById !== 'system'" class="proposal-section">
          <div class="proposal-header">
            <h4 class="proposal-title">{{ t('transactionFlow.negotiation.userProposal') }}</h4>
          </div>
          <div class="proposal-content">
            <p class="proposal-text">
              {{ t('transactionFlow.negotiation.proposalFrom', { username: currentProposalUsername }) }}
            </p>
            <p class="proposal-details">
              {{ t('transactionFlow.negotiation.proposedPayer', { username: currentProposalPayerUsername }) }}
            </p>
          </div>
        </div>

        <!-- Agreement Status -->
        <div v-if="agreementStatusText" class="proposal-info">
          <NAlert :title="agreementStatusTitle" :type="agreementStatusType" style="margin-bottom: 15px;">
            {{ agreementStatusText }}
          </NAlert>
        </div><!-- Action Buttons for Negotiation -->        <div class="action-buttons" v-if="payerNegotiationStore.currentNegotiation?.negotiationStatus !== 'FINALIZED'">
          <NButton 
            v-if="canAgreeToCurrentProposal" 
            type="primary" 
            @click="handleAgreeToProposal"
            :loading="!!isActionLoading"
          >
            {{ payerNegotiationStore.currentNegotiation?.currentProposal_ById === 'system' 
               ? t('transactionFlow.buttons.agreeToSystemRecommendation')
               : t('transactionFlow.buttons.agreeToUserProposal', { username: currentProposalUsername }) }}
          </NButton>

          <NButton 
            v-if="canProposeAlternative" 
            type="default" 
            @click="openProposalModal"
            :loading="!!isActionLoading"
          >
            {{ t('transactionFlow.buttons.proposeOtherPaysFirst', { username: otherUser?.username || t('auth.otherUser') }) }}
          </NButton>
        </div>
      </div>

      <!-- Finalized Negotiation Details -->
      <div v-if="payerNegotiationStore.currentNegotiation?.negotiationStatus === 'FINALIZED'" class="finalized-negotiation-details">
        <NAlert :title="t('transactionFlow.negotiation.finalized')" type="success" style="margin-bottom: 15px;">
          {{ t('transactionFlow.negotiation.negotiationFinalized') }} {{ finalizedPayerUsername }}
        </NAlert>

        <!-- Payment Details Section (only show for the party who needs to pay) -->        <div v-if="recipientPaymentDetails" class="payment-details">
          <h4>{{ t('transactionFlow.paymentDetails.recipientDetails') }}</h4>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountNumber') }}:</strong> {{ recipientPaymentDetails.accountNumber }}</p>
          <p><strong>{{ t('transactionFlow.paymentDetails.bankName') }}:</strong> {{ recipientPaymentDetails.bankName }}</p>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountHolderName') }}:</strong> {{ recipientPaymentDetails.accountHolderName }}</p>
        </div>

        <!-- Payment Due Date Display -->
        <div v-if="paymentDueDateDisplay" class="payment-due-info">
          <p><strong>{{ t('transactionFlow.paymentDetails.paymentDueDate') }}</strong> {{ paymentDueDateDisplay }}</p>
        </div>        <!-- Second Payer Payment Details (for second payment phase) -->
        <div v-if="secondPayerRecipientDetails && currentTransaction?.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT" class="payment-details">
          <h4>{{ t('transactionFlow.paymentDetails.recipientDetails') }} ({{ firstPayerUsername }})</h4>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountNumber') }}:</strong> {{ secondPayerRecipientDetails.accountNumber }}</p>
          <p><strong>{{ t('transactionFlow.paymentDetails.bankName') }}:</strong> {{ secondPayerRecipientDetails.bankName }}</p>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountHolderName') }}:</strong> {{ secondPayerRecipientDetails.accountHolderName }}</p>
        </div>

        <!-- My Payment Details for Sharing -->
        <div v-if="myPaymentDetailsForSharing && (currentTransaction?.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION || currentTransaction?.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION)" class="payment-details">
          <h4>{{ t('transactionFlow.paymentDetails.recipientDetails') }} ({{ t('auth.you') }})</h4>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountNumber') }}:</strong> {{ maskedSharedAccountNumber }}</p>          <p><strong>{{ t('transactionFlow.paymentDetails.bankName') }}:</strong> {{ myPaymentDetailsForSharing.bankName }}</p>
          <p><strong>{{ t('transactionFlow.paymentDetails.accountHolderName') }}:</strong> {{ myPaymentDetailsForSharing.accountHolderName }}</p>
        </div>
      </div>

      <!-- Sub-steps for current stage -->
      <div v-if="currentSubSteps.length > 0" class="sub-steps">
        <ul>
          <li v-for="step in currentSubSteps" :key="step.text" :class="step.status">
            <span class="icon">{{ step.icon }}</span> {{ step.text }}
          </li>
        </ul>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">        <!-- Declare Payment Button -->
        <NButton 
          v-if="isUserTurnForPayment" 
          type="primary" 
          @click="openDeclarePaymentModal"
          :loading="isActionLoading === 'declarePayment'"
          :disabled="isCurrentActionBlockedByTimer"
        >
          {{ t('transactionFlow.buttons.declarePayment') }}
        </NButton>        <!-- Confirm Receipt Button -->
        <NButton 
          v-if="isUserTurnForConfirmation" 
          type="success" 
          @click="handleConfirmReceipt"
          :loading="isActionLoading === 'confirmReceipt'"
          :disabled="isCurrentActionBlockedByTimer"
        >
          {{ t('transactionFlow.buttons.confirmReceipt') }}
        </NButton>
      </div>

      <!-- Secondary Actions -->
      <div class="secondary-actions">        <!-- Cancel Transaction Button -->
        <NButton 
          v-if="canCancel" 
          type="error" 
          size="small"
          @click="openCancelModal"
          :loading="isActionLoading === 'cancel'"
        >
          {{ t('transactionFlow.buttons.cancelTransaction') }}
        </NButton>        <!-- Dispute Transaction Button -->
        <NButton 
          v-if="canDispute" 
          type="warning" 
          size="small"
          @click="openDisputeModal"
          :loading="isActionLoading === 'dispute'"
        >
          {{ t('transactionFlow.buttons.disputeTransaction') }}
        </NButton>
      </div>
    </div>

    <!-- Declare Payment Modal -->
    <NModal v-model:show="showDeclarePaymentModal" preset="dialog" :title="t('transactionFlow.modals.declarePaymentTitle')">
      <div>
        <p>{{ t('transactionFlow.modals.trackingNumberLabel') }}</p>
        <NInput 
          v-model:value="paymentTrackingNumber" 
          :placeholder="t('transactionFlow.modals.trackingNumberPlaceholder')"
        />
      </div>      <template #action>
        <NButton @click="showDeclarePaymentModal = false">{{ t('app.cancel') }}</NButton>
        <NButton type="primary" @click="handleDeclarePayment" :loading="isActionLoading === 'declarePayment'">
          {{ t('app.confirm') }}
        </NButton>
      </template>
    </NModal>

    <!-- Cancel Transaction Modal -->
    <NModal v-model:show="showCancelModal" preset="dialog" :title="t('transactionFlow.modals.cancelTransactionTitle')">
      <div>
        <p>{{ t('transactionFlow.modals.cancelReasonLabel') }}</p>
        <NInput 
          v-model:value="cancelReason" 
          type="textarea"
          :placeholder="t('transactionFlow.modals.cancelReasonPlaceholder')"
        />
      </div>      <template #action>
        <NButton @click="showCancelModal = false">{{ t('app.cancel') }}</NButton>
        <NButton type="error" @click="handleCancelTransaction" :loading="isActionLoading === 'cancel'">
          {{ t('app.confirm') }}
        </NButton>
      </template>
    </NModal>

    <!-- Dispute Transaction Modal -->
    <NModal v-model:show="showDisputeModal" preset="dialog" :title="t('transactionFlow.modals.disputeTransactionTitle')">
      <div>
        <p>{{ t('transactionFlow.modals.disputeReasonLabel') }}</p>
        <NInput 
          v-model:value="disputeReason" 
          type="textarea"
          :placeholder="t('transactionFlow.modals.disputeReasonPlaceholder')"
        />
      </div>      <template #action>
        <NButton @click="showDisputeModal = false">{{ t('app.cancel') }}</NButton>
        <NButton type="warning" @click="handleDisputeTransaction" :loading="isActionLoading === 'dispute'">
          {{ t('app.confirm') }}
        </NButton>
      </template>
    </NModal>    <!-- Proposal Modal -->
    <NModal v-model:show="showDesignateModal" preset="dialog" :title="t('transactionFlow.modals.proposalTitle', { username: otherUser?.username || t('auth.otherUser') })">
      <div>
        <NAlert type="info" style="margin-bottom: 15px;">
          {{ t('transactionFlow.modals.proposalExplanation', { username: otherUser?.username || t('auth.otherUser') }) }}
        </NAlert>
        <p>{{ t('transactionFlow.modals.proposalMessageLabel') }}</p>
        <NInput 
          v-model:value="proposalMessage" 
          type="textarea"
          :placeholder="t('transactionFlow.modals.proposalMessagePlaceholder')"
        />
      </div>      <template #action>
        <NButton @click="showDesignateModal = false">{{ t('app.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmitProposal" :loading="isActionLoading === 'dispute'">
          {{ t('transactionFlow.modals.submitProposal', { username: otherUser?.username || t('auth.otherUser') }) }}
        </NButton>
      </template>
    </NModal>
  </NCard>
  <NSpin v-else-if="isLoading" size="large" style="margin-top: 20px;" />
  <NAlert v-else :title="t('app.error')" type="error" style="margin-top: 20px;">
    {{ t('transactionFlow.error') }}
  </NAlert>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'; // Removed openDesignateModal as it's unused
import {
  NCard, NSteps, NStep, NButton, NModal, NInput, NSpin, NAlert, useMessage
} from 'naive-ui';
import { useI18n } from 'vue-i18n';
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic';
import { useUserResolver } from '@/composables/useUserResolver';
import { TransactionStatusEnum } from '@/types/transaction';
import { usePayerNegotiationStore } from '@/stores/payerNegotiation';
import { useTransactionStore } from '@/stores/transactionStore';
import PaymentReadinessGate from '@/components/PaymentReadinessGate.vue';
import type { PaymentReceivingInfo, PaymentReceivingSubmitPayload } from '@/types/payerNegotiation'; // Ensure PaymentReceivingSubmitPayload is imported
import { ReceivingInfoStatus } from '@/types/payerNegotiation'; // Ensure this is imported
import { celebrateTransactionCompletion } from '@/utils/confetti';

const props = defineProps<{
  chatSessionId: string | null;
}>();

const { t } = useI18n();
const naiveMessage = useMessage(); // Added semicolon

// Initialize user resolver for getting usernames from stores
const { resolveUsername } = useUserResolver(); // Added semicolon

// Component local state
const showDesignateModal = ref(false); // Added semicolon
const proposalMessage = ref(''); // Added semicolon

const {
  currentTransaction,
  isLoading,
  storeError,
  userId,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer,
  isActionLoading,
  showDeclarePaymentModal,
  paymentTrackingNumber,
  showCancelModal,
  cancelReason,
  showDisputeModal,
  disputeReason,
  isUserTurnForPayment,
  isUserTurnForConfirmation,
  canCancel,
  canDispute,
  isCurrentActionBlockedByTimer,
  handleDeclarePayment,
  handleConfirmReceipt,
  handleCancelTransaction,
  handleDisputeTransaction,
  openDeclarePaymentModal,
  openCancelModal,
  openDisputeModal,
} = useTransactionFlowLogic(computed(() => props.chatSessionId), naiveMessage);

const payerNegotiationStore = usePayerNegotiationStore();
const transactionStore = useTransactionStore();

// Define userProfileDetailsForGate based on store data
// This is what will be passed to the PaymentReadinessGate component.
const userProfileDetailsForGate = computed<PaymentReceivingInfo | null | undefined>(() => {
  // ASSUMPTION: payerNegotiationStore has a reactive getter `currentUserDefaultReceivingInfo`.
  // This getter needs to be implemented in your `payerNegotiationStore.ts`.
  // Example in store:
  // state: () => ({ _currentUserDefaultReceivingInfo: null as PaymentReceivingInfo | null, /* ... */ }),
  // getters: { currentUserDefaultReceivingInfo: (state) => state._currentUserDefaultReceivingInfo, /* ... */ },
  // actions: { async fetchCurrentUserDefaultPaymentInfo() { /* fetch and set _currentUserDefaultReceivingInfo */ } }
  //
  // Remember to call the action to fetch this data, e.g., in onMounted or when appropriate.
  return payerNegotiationStore.currentUserDefaultReceivingInfo;
});

// Example of how you might fetch the data in onMounted (adapt as needed)
onMounted(async () => {
  // Socket listeners are already initialized by the stores when created or when user logs in
  // No need to call initializeSocketListeners() here as it would create duplicates
  
  // Fetch the user's default payment info when component mounts
  if (typeof payerNegotiationStore.fetchCurrentUserDefaultPaymentInfo === 'function') {
    await payerNegotiationStore.fetchCurrentUserDefaultPaymentInfo();
  }
});


// Add watching for transaction ID changes to fetch negotiation state
watch(
  () => currentTransaction.value?.id,
  async (newId) => {
    if (newId) {
      await payerNegotiationStore.fetchNegotiation(newId);
    }
  },
  { immediate: true }
);

// Watch for transaction completion and trigger confetti
watch(
  () => currentTransaction.value?.status,
  (newStatus, oldStatus) => {
    if (newStatus === TransactionStatusEnum.COMPLETED && oldStatus !== TransactionStatusEnum.COMPLETED) {
      // Small delay to ensure UI has updated before confetti
      setTimeout(() => {
        try {
          celebrateTransactionCompletion();
          console.log('[TransactionFlowCardV3] Confetti celebration triggered for transaction completion! 🎉');
        } catch (error) {
          console.error('[TransactionFlowCardV3] Error triggering confetti celebration:', error);
        }
      }, 500); // 500ms delay to let UI settle
    }
  }
);

// Helper to get username from user ID
// Get username using the userResolver composable instead of transaction provider objects
const getUsername = (userIdToFind: string): string => {
  if (!userIdToFind) {
    return t('common.unknownUser');
  }
  // Use the userResolver which gets data from authStore and chatStore
  const resolvedUsername = resolveUsername(userIdToFind);
  
  if (import.meta.env.DEV) {
    console.debug('[getUsername] Looking for userId:', userIdToFind);
    console.debug('[getUsername] Resolved username:', resolvedUsername);
  }

  return resolvedUsername || t('common.unknownUser');
};

// Computed properties for template username lookups to prevent excessive re-calculations
const currentProposalPayerUsername = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (negotiation?.currentProposal_PayerId) {
    return getUsername(negotiation.currentProposal_PayerId);
  }
  return 'N/A';
});

const finalizedPayerUsername = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (negotiation?.finalizedPayerId) {
    return getUsername(negotiation.finalizedPayerId);
  }
  return 'N/A';
});

// Computed property for first payer username (used in second payment phase)
const firstPayerUsername = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (negotiation?.finalizedPayerId) {
    return getUsername(negotiation.finalizedPayerId);
  }
  return 'First Payer';
});

// Computed property for transaction ID display
const transactionIdDisplay = computed(() => {
  return currentTransaction.value?.id?.substring(0, 8) + '...' || t('auth.notAvailable');
});

// Computed property for masked account number
const maskedSharedAccountNumber = computed(() => {
  const accountNumber = myPaymentDetailsForSharing.value?.accountNumber;
  if (!accountNumber) return 'N/A';
  if (accountNumber.length <= 4) return accountNumber;
  return `****${accountNumber.slice(-4)}`;
});

// Computed property for payment due date
const paymentDueDateDisplay = computed(() => {
  const dueDate = payerNegotiationStore.currentNegotiation?.paymentTimerDueDate;
  if (!dueDate) return null;
  return new Date(dueDate).toLocaleString();
});

// Timer display for payment and confirmation steps
const timerDisplayValue = computed(() => {
  const tx = currentTransaction.value;
  if (!tx) return null;
  
  // Show timer for payment and confirmation steps
  const showTimerForStatuses = [
    TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT,
    TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION,
    TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT,
    TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION
  ];
  
  if (!showTimerForStatuses.includes(tx.status)) return null;
  
  return timeLeft.value;
});

const timerLabel = computed(() => {
  const tx = currentTransaction.value;
  if (!tx) return '';
  
  if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT || 
      tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT) {
    if (isElapsedTimer.value) {
      return t('transactionFlow.timer.timeElapsed');
    } else {
      return t('transactionFlow.timer.paymentWindow');
    }
  } else if (tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION || 
             tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION) {
    return t('transactionFlow.timer.timeElapsed');
  }
  
  return t('transactionFlow.timer.timeRemaining');
});


// Computed property for system recommended payer username
const systemRecommendedPayerUsername = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (negotiation?.systemRecommendedPayerId) {
    return getUsername(negotiation.systemRecommendedPayerId);
  }
  return 'N/A';
});

const systemRecommendationDisplay = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  const tx = currentTransaction.value;
  if (!negotiation?.systemRecommendedPayerId || !tx) {
    return t('transactionFlow.negotiation.systemRecommendationDetermining');
  }
  
  // Main recommendation text
  const mainText = t('transactionFlow.negotiation.systemRecommendation', { username: systemRecommendedPayerUsername.value });
  
  // Get the reason with emphasis
  let reasonText = '';
  if (negotiation.systemRecommendationRule) {
    const reasonKey = getReasonTranslationKey(negotiation.systemRecommendationRule, negotiation);
    if (reasonKey) {
      reasonText = `<div class="recommendation-reason"><strong>${t('transactionFlow.negotiation.reasonLabel')}:</strong> ${reasonKey}</div>`;
    }
  }
  
  return `<div class="recommendation-main">${mainText}</div>${reasonText}`;
});

// Computed property for current proposal username
const currentProposalUsername = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (negotiation?.currentProposal_ById && negotiation.currentProposal_ById !== 'system') {
    return getUsername(negotiation.currentProposal_ById);
  }
  return 'System';
});

const canAgreeToCurrentProposal = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  const currentUserId = userId.value;
  
  // Basic requirements: negotiation exists, user ID exists, and current user has passed payment gate
  if (!negotiation || !currentUserId || !payerNegotiationStore.isPaymentGatePassedForCurrentUser) {
    return false;
  }
  
  // There must be a proposal, and it must not be by the current user
  if (!negotiation.currentProposal_ById || negotiation.currentProposal_ById === currentUserId) {
    return false;
  }
  
  // Current user must not have already agreed to this specific proposal
  const hasAgreed = (negotiation.partyA_Id === currentUserId && negotiation.partyA_agreedToCurrentProposal) ||
                    (negotiation.partyB_Id === currentUserId && negotiation.partyB_agreedToCurrentProposal);
  if (hasAgreed) {
    return false;
  }
  
  // Negotiation must be in an active state (not finalized, cancelled, or expired)
  if (['FINALIZED', 'CANCELLED', 'EXPIRED'].includes(negotiation.negotiationStatus)) {
    return false;
  }
  
  // If all above conditions pass, the user should be able to agree
  // This allows agreement in READY_TO_NEGOTIATE or PENDING_RESPONSE states
  return true;
});



const canProposeAlternative = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  const currentUserId = userId.value;
  
  if (!negotiation || !currentUserId || !payerNegotiationStore.isPaymentGatePassedForCurrentUser) {
    return false;
  }
  
  if (negotiation.negotiationStatus === 'FINALIZED' || negotiation.negotiationStatus === 'CANCELLED') {
    return false;
  }
  
  // Only the user who is currently recommended/proposed to pay first can propose an alternative
  let currentlyRecommendedPayerId: string | null = null;
  
  if (negotiation.currentProposal_ById === 'system' && negotiation.systemRecommendedPayerId) {
    // System recommendation case
    currentlyRecommendedPayerId = negotiation.systemRecommendedPayerId;
  } else if (negotiation.currentProposal_PayerId) {
    // Active proposal case
    currentlyRecommendedPayerId = negotiation.currentProposal_PayerId;
  }
  
  // User can only propose if they are currently the one suggested to pay first
  return currentlyRecommendedPayerId === currentUserId;
});

const agreementStatusText = computed(() => {
    const negotiation = payerNegotiationStore.currentNegotiation;
    const currentUserId = userId.value;

    if (!negotiation || !currentUserId) return '';

    const amIPartyA = negotiation.partyA_Id === currentUserId;
    const myAgreementFlag = amIPartyA ? negotiation.partyA_agreedToCurrentProposal : negotiation.partyB_agreedToCurrentProposal;
    const otherPartyAgreementFlag = amIPartyA ? negotiation.partyB_agreedToCurrentProposal : negotiation.partyA_agreedToCurrentProposal;
    const otherPartyUsername = otherUser.value?.username || t('auth.otherParty', 'the other party');

    if (negotiation.negotiationStatus === 'PENDING_RESPONSE') {
        if (negotiation.currentProposal_ById === currentUserId) {
            // My proposal is active
            return t('transactionFlow.negotiation.waitingForResponse');
        } else if (negotiation.currentProposal_ById && negotiation.currentProposal_ById !== 'system') {
            // Other user's proposal is active
            if (myAgreementFlag) {
                return t('transactionFlow.negotiation.youAgreedWaitingForFinalize', { username: getUsername(negotiation.currentProposal_ById) });
            }
            // Not my proposal, and I haven't agreed yet - covered by action buttons
            return t('transactionFlow.negotiation.awaitingYourResponse', { username: getUsername(negotiation.currentProposal_ById) });
        } else { // System proposal is active
             if (myAgreementFlag && !otherPartyAgreementFlag) {
                return t('transactionFlow.negotiation.youAgreedSystemWaiting', { username: otherPartyUsername });
            } else if (!myAgreementFlag && otherPartyAgreementFlag) {
                return t('transactionFlow.negotiation.otherPartyAgreedSystemWaiting', { username: otherPartyUsername });
            } else if (myAgreementFlag && otherPartyAgreementFlag) {
                // This state should ideally transition to FINALIZED quickly
                return t('transactionFlow.negotiation.bothPartiesAgreedFinalizing');
            }
        }
    } else if (negotiation.negotiationStatus === 'FINALIZED') {
        return t('transactionFlow.negotiation.agreementReached', { username: getUsername(negotiation.finalizedPayerId!) });
    }
    return '';
});

const agreementStatusTitle = computed(() => {
    const negotiation = payerNegotiationStore.currentNegotiation;
    if (!negotiation) return t('transactionFlow.negotiation.negotiationStatus');
    switch(negotiation.negotiationStatus) {
        case 'PENDING_RESPONSE': return t('transactionFlow.negotiation.proposalActive');
        case 'FINALIZED': return t('transactionFlow.negotiation.agreementReachedTitle');
        default: return t('transactionFlow.negotiation.negotiationStatus');
    }
});

const agreementStatusType = computed<"info" | "success" | "warning">(() => {
    const negotiation = payerNegotiationStore.currentNegotiation;
    if (!negotiation) return "info";
     switch(negotiation.negotiationStatus) {
        case 'PENDING_RESPONSE': return "warning";
        case 'FINALIZED': return "success";
        default: return "info";
    }
});


const handleReceivingInfoSubmit = async (info: PaymentReceivingSubmitPayload) => {
  if (!currentTransaction.value) return;
  
  try {
    // The 'info' here now correctly reflects the payload from PaymentReadinessGate
    // This assumes payerNegotiationStore.provideReceivingInfo can handle PaymentReceivingSubmitPayload
    // (which typically includes 'saveToProfile' rather than 'isDefaultForUser' for new/edited submissions)
    await payerNegotiationStore.provideReceivingInfo(currentTransaction.value.id, info);
    naiveMessage.success(t('transactionFlow.messages.paymentDetailsSubmitted'));
  } catch (error) {
    naiveMessage.error(t('transactionFlow.messages.errorOccurred'));
  }
};

// Adjusted to use userProfileDetailsForGate
const handleUseProfileDetails = async () => {
  if (!currentTransaction.value) return;

  const profileDetailsToUse = userProfileDetailsForGate.value;

  if (!profileDetailsToUse) {
    naiveMessage.warning('No profile details available to use. Please provide them manually.');
    return;
  }

  try {
    // Construct PaymentReceivingSubmitPayload from PaymentReceivingInfo
    // IMPORTANT: The following properties (swiftCode, iban, etc.) were removed
    // because they are not defined on PaymentReceivingSubmitPayload or PaymentReceivingInfo types.
    // You need to update these type definitions in 'frontend/src/types/payerNegotiation.ts'
    // if these fields are intended to be part of the payload.
    const payload: PaymentReceivingSubmitPayload = {
      id: profileDetailsToUse.id, // Pass the ID of the existing record
      bankName: profileDetailsToUse.bankName,
      accountNumber: profileDetailsToUse.accountNumber,
      accountHolderName: profileDetailsToUse.accountHolderName,
      saveToProfile: false, // Don't save again since we're using existing profile data
    };

    await payerNegotiationStore.provideReceivingInfo(currentTransaction.value.id, payload);
    naiveMessage.success(t('transactionFlow.messages.profileDetailsConfirmed'));
  } catch (error) {
    naiveMessage.error(t('transactionFlow.messages.errorOccurred'));
  }
};

const handleAgreeToProposal = async () => {
  if (!currentTransaction.value) return;

  try {
    await payerNegotiationStore.acceptCurrentProposal(currentTransaction.value.id);
    naiveMessage.success(t('transactionFlow.messages.agreementConfirmed'));
  } catch (error) {
    naiveMessage.error(t('transactionFlow.messages.errorOccurred'));
  }
};

const otherUser = computed(() => {
  if (!currentTransaction.value || !userId.value) return null;
  // Assuming currencyAProviderId and currencyBProviderId are the two participant IDs
  const tx = currentTransaction.value;
  if (userId.value === tx.currencyAProviderId) {
    return { id: tx.currencyBProviderId, username: tx.currencyBProvider?.username };
  } else if (userId.value === tx.currencyBProviderId) {
    return { id: tx.currencyAProviderId, username: tx.currencyAProvider?.username };
  }
  return null;
});

// Display names for users
const otherUserDisplayName = computed(() => {
  return otherUser.value?.username || t('auth.otherUser');
});

// User role display computed properties
const currentUserRoleDisplay = computed(() => {
  if (!currentTransaction.value || !userId.value) return t('transactionFlow.userRoles.loadingRole');

  const tx = currentTransaction.value;
  // Use currencyAProvider and currencyBProvider from the transaction structure
  if (!tx.currencyAProvider || !tx.currencyBProvider) return t('transactionFlow.userRoles.userDataMissing');

  if (userId.value === tx.currencyAProviderId) {
    return t('transactionFlow.userRoles.providerOf', { currency: tx.currencyA });
  } else if (userId.value === tx.currencyBProviderId) {
    return t('transactionFlow.userRoles.providerOf', { currency: tx.currencyB });
  }
  return t('transactionFlow.userRoles.determiningRole');
});

const otherUserRoleDisplay = computed(() => {
  if (!currentTransaction.value || !userId.value) return t('transactionFlow.userRoles.loadingRole');

  const tx = currentTransaction.value;
  // Use currencyAProvider and currencyBProvider from the transaction structure
  if (!tx.currencyAProvider || !tx.currencyBProvider) return t('transactionFlow.userRoles.userDataMissing');

  const otherPartyId = userId.value === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;

  if (otherPartyId === tx.currencyAProviderId) {
    return t('transactionFlow.userRoles.providerOf', { currency: tx.currencyA });
  } else if (otherPartyId === tx.currencyBProviderId) {
    return t('transactionFlow.userRoles.providerOf', { currency: tx.currencyB });
  }
  return t('transactionFlow.userRoles.determiningRole');
});

// Show the Payment Readiness Gate if the current user has not provided/confirmed their info
const showPaymentReadinessGate = computed(() => {
  if (!currentTransaction.value) {
    return false;
  }
  
  const transactionStatus = currentTransaction.value.status;
  const gatePassedForUser = payerNegotiationStore.isPaymentGatePassedForCurrentUser;
  
  const shouldShow = (
    transactionStatus === TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION &&
    !gatePassedForUser
  );
  
  return shouldShow;
});

// Enable negotiation UI for the current user as soon as they are ready, regardless of the other party
const canNegotiate = computed(() => {
  if (!currentTransaction.value) return false;
  return (
    currentTransaction.value.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION &&
    payerNegotiationStore.isPaymentGatePassedForCurrentUser
  );
});

const headerTitle = computed(() => {
  if (!currentTransaction.value) return t('transactionFlow.title');
  const tx = currentTransaction.value;
  switch (tx.status) {
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION: return t('transactionFlow.steps.designate');
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
      return userId.value === tx.agreedFirstPayerId ? t('transactionFlow.currentAction.yourTurnToPay') : t('transactionFlow.currentAction.waitingForPayment', { username: otherUser.value?.username || t('transactionFlow.userRoles.determiningRole') });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
      const secondPayerConfId = tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;
      return userId.value === secondPayerConfId ? t('transactionFlow.currentAction.yourTurnToConfirm') : t('transactionFlow.currentAction.waitingForConfirmation', { username: otherUser.value?.username || t('transactionFlow.userRoles.determiningRole') });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
      const secondPayerPayId = tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;
      return userId.value === secondPayerPayId ? t('transactionFlow.currentAction.yourTurnToPay') : t('transactionFlow.currentAction.waitingForPayment', { username: otherUser.value?.username || t('transactionFlow.userRoles.determiningRole') });
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
      return userId.value === tx.agreedFirstPayerId ? t('transactionFlow.currentAction.yourTurnToConfirm') : t('transactionFlow.currentAction.waitingForConfirmation', { username: otherUser.value?.username || t('transactionFlow.userRoles.determiningRole') });
    case TransactionStatusEnum.COMPLETED: return t('transactionFlow.currentAction.transactionCompleted');
    case TransactionStatusEnum.CANCELLED: return t('transactionFlow.currentAction.transactionCancelled');
    case TransactionStatusEnum.DISPUTED: return t('transactionFlow.currentAction.transactionDisputed');
    default: return t('transactionFlow.title');
  }
});

const visualSteps = computed(() => [
  { title: t('transactionFlow.steps.designate'), description: t('transactionFlow.steps.designateDescription'), icon: '⚖️', statusKey: [TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION] },
  { title: t('transactionFlow.steps.payment1'), description: t('transactionFlow.steps.payment1Description'), icon: '💸', statusKey: [TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT] },
  { title: t('transactionFlow.steps.confirmation1'), description: t('transactionFlow.steps.confirmation1Description'), icon: '🤝', statusKey: [TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION] },
  { title: t('transactionFlow.steps.payment2'), description: t('transactionFlow.steps.payment2Description'), icon: '💸', statusKey: [TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT] },
  { title: t('transactionFlow.steps.confirmation2'), description: t('transactionFlow.steps.confirmation2Description'), icon: '🤝', statusKey: [TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION] },
  { title: t('transactionFlow.steps.completed'), description: t('transactionFlow.steps.completedDescription'), icon: '🏁', statusKey: [TransactionStatusEnum.COMPLETED] },
]);

const currentStepIndex = computed(() => {
  if (!currentTransaction.value) return 0;
  const status = currentTransaction.value.status;

  switch (status) {
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION: return 1;
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT: return 2;
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION: return 3;
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT: return 4;
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION: return 5;
    case TransactionStatusEnum.COMPLETED: return 6;
    case TransactionStatusEnum.CANCELLED:
    case TransactionStatusEnum.DISPUTED:
      return visualSteps.value.length; // Show as last step for error states
    default: return 0;
  }
});

const getStepStatus = (index: number): 'finish' | 'process' | 'wait' | 'error' => {
  const csi = currentStepIndex.value -1; // 0-indexed current step
  if (index < csi) return 'finish';
  if (index === csi) {
    const status = currentTransaction.value?.status;
    if (status === TransactionStatusEnum.CANCELLED || status === TransactionStatusEnum.DISPUTED) {
      return 'error';
    }
    return 'process';
  }
  return 'wait';
};

// Computed property for step statuses to prevent recalculation on every render
const stepStatuses = computed(() => {
  return visualSteps.value.map((_, index) => getStepStatus(index));
});

const currentStepStatus = computed((): 'finish' | 'process' | 'wait' | 'error' => {
  if (!currentTransaction.value) return 'process';
  const status = currentTransaction.value.status;
  if (status === TransactionStatusEnum.CANCELLED || status === TransactionStatusEnum.DISPUTED) return 'error';
  if (status === TransactionStatusEnum.COMPLETED) return 'finish';
  return 'process';
});


const currentActionTitle = computed(() => {
  if (!currentTransaction.value || !userId.value) return t('transactionFlow.currentAction.loadingAction');
  const tx = currentTransaction.value;
  const status = tx.status;
  
  // Determine if the current user is the agreed first payer
  const iAmAgreedFirstPayer = tx.agreedFirstPayerId && userId.value === tx.agreedFirstPayerId;
  // Determine if the current user is the "other" payer (second payer)
  let otherPayerId: string | null = null;
  if (tx.agreedFirstPayerId) {
    otherPayerId = tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProviderId : tx.currencyAProviderId;
  }
  const iAmOtherPayer = otherPayerId && userId.value === otherPayerId;

  switch (status) {
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION:
      return t('transactionFlow.currentAction.awaitingDesignation');
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
      return iAmAgreedFirstPayer 
        ? t('transactionFlow.currentAction.yourTurnToPay') 
        : t('transactionFlow.currentAction.waitingForPayment', { 
            username: tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username 
          });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
      return iAmOtherPayer 
        ? t('transactionFlow.currentAction.yourTurnToConfirm') 
        : t('transactionFlow.currentAction.waitingForConfirmation', { username: otherUser.value?.username || t('auth.otherParty') });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
      return iAmOtherPayer 
        ? t('transactionFlow.currentAction.yourTurnToPay') 
        : t('transactionFlow.currentAction.waitingForPayment', { username: otherUser.value?.username || t('auth.otherParty') });
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
      return iAmAgreedFirstPayer 
        ? t('transactionFlow.currentAction.yourTurnToConfirm') 
        : t('transactionFlow.currentAction.waitingForConfirmation', { 
            username: tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username          });
    case TransactionStatusEnum.COMPLETED: 
      return t('transactionFlow.currentAction.transactionCompleted');
    case TransactionStatusEnum.CANCELLED: 
      return t('transactionFlow.currentAction.transactionCancelled');
    case TransactionStatusEnum.DISPUTED: 
      return t('transactionFlow.currentAction.transactionDisputed');
    default: 
      return t('transactionFlow.currentAction.loadingAction');
  }
});

const currentActionInfoText = computed(() => {
  if (!currentTransaction.value || !userId.value) return "";
  const tx = currentTransaction.value;
  const { status, currencyA, currencyB, amountA, amountB, agreedFirstPayerId } = tx;

  // Initialize with explicit types and null values
  let firstPayerUsername: string | null = null;
  let firstPayerCurrency: string = '';
  let firstPayerAmount: number = 0;
  let secondPayerUsername: string | null = null;
  let secondPayerCurrency: string = ''; 
  let secondPayerAmount: number = 0;

  if (agreedFirstPayerId) {
    if (agreedFirstPayerId === tx.currencyAProviderId) {
      // Use getUsername helper instead of direct access to ensure consistency
      firstPayerUsername = getUsername(tx.currencyAProviderId);
      firstPayerCurrency = currencyA;
      firstPayerAmount = amountA;
      secondPayerUsername = getUsername(tx.currencyBProviderId);
      secondPayerCurrency = currencyB;
      secondPayerAmount = amountB;
    } else {
      // Use getUsername helper instead of direct access to ensure consistency
      firstPayerUsername = getUsername(tx.currencyBProviderId);
      firstPayerCurrency = currencyB;
      firstPayerAmount = amountB;
      secondPayerUsername = getUsername(tx.currencyAProviderId);
      secondPayerCurrency = currencyA;
      secondPayerAmount = amountA;
    }
  }
  switch (status) {
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION:
      return t('transactionFlow.actionInfo.designationPending');
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
      if (userId.value === agreedFirstPayerId) {
        return t('transactionFlow.actionInfo.youPayFirst', { 
          amount: firstPayerAmount, 
          currency: firstPayerCurrency, 
          username: secondPayerUsername 
        });
      }
      return t('transactionFlow.actionInfo.otherPartyPaying', { 
        username: firstPayerUsername, 
        amount: firstPayerAmount, 
        currency: firstPayerCurrency 
      });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
      if (userId.value !== agreedFirstPayerId) { // User is the second payer (receiver of first payment)
        return t('transactionFlow.actionInfo.youConfirmPayment', { 
          amount: firstPayerAmount, 
          currency: firstPayerCurrency, 
          username: firstPayerUsername 
        });
      }
      return t('transactionFlow.actionInfo.otherPartyConfirming', { 
        amount: firstPayerAmount, 
        currency: firstPayerCurrency, 
        username: secondPayerUsername 
      });
    case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
      if (userId.value !== agreedFirstPayerId) { // User is the second payer
        return t('transactionFlow.actionInfo.youPaySecond', { 
          amount: secondPayerAmount, 
          currency: secondPayerCurrency, 
          username: firstPayerUsername 
        });
      }
      return t('transactionFlow.actionInfo.waitingSecondPayment', { 
        username: secondPayerUsername, 
        amount: secondPayerAmount, 
        currency: secondPayerCurrency 
      });
    case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
      if (userId.value === agreedFirstPayerId) { // User is the first payer (receiver of second payment)
        return t('transactionFlow.actionInfo.youConfirmSecond', { 
          amount: secondPayerAmount, 
          currency: secondPayerCurrency, 
          username: secondPayerUsername 
        });
      }
      return t('transactionFlow.actionInfo.waitingSecondConfirmation', { 
        amount: secondPayerAmount, 
        currency: secondPayerCurrency, 
        username: firstPayerUsername 
      });
    case TransactionStatusEnum.COMPLETED:
      return `${t('transactionFlow.actionInfo.completedExchange', { 
        amountA: amountA, 
        currencyA: currencyA, 
        amountB: amountB, 
        currencyB: currencyB 
      })}`;
    case TransactionStatusEnum.CANCELLED:
      return `${t('transactionFlow.actionInfo.cancellationReason')}: ${tx.cancellationReason || t('transactionFlow.actionInfo.noReasonProvided')}`;
    case TransactionStatusEnum.DISPUTED:
      return `${t('transactionFlow.actionInfo.disputeReason')}: ${tx.disputeReason || t('transactionFlow.actionInfo.noReasonProvided')}. ${t('transactionFlow.actionInfo.adminReview')}`;
    default: 
      return t('transactionFlow.actionInfo.followInstructions');
  }
});

const currentSubSteps = computed(() => {
  if (!currentTransaction.value || !userId.value) return [];
  const tx = currentTransaction.value;
  const { status, agreedFirstPayerId, paymentDeclaredAtPayer1, firstPaymentConfirmedByPayer2At, paymentDeclaredAtPayer2, secondPaymentConfirmedByPayer1At } = tx;
  
  let steps: { text: string; status: string; icon: string }[] = [];

  // Sub-steps for AWAITING_FIRST_PAYER_DESIGNATION
  if (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION) {
    steps.push({
      text: t('transactionFlow.subSteps.designateFirstPayer'),
      status: agreedFirstPayerId ? 'done' : 'current-action',
      icon: agreedFirstPayerId ? '✔️' : '👉'
    });
  }

  // Sub-steps for Payment Leg 1
  if (agreedFirstPayerId) {
    // Use getUsername helper for consistent username resolution
    const firstPayerUser = getUsername(agreedFirstPayerId);
    const secondPayerUser = agreedFirstPayerId === tx.currencyAProviderId 
      ? getUsername(tx.currencyBProviderId) 
      : getUsername(tx.currencyAProviderId);

    if (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT || 
        status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT ||
        status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.COMPLETED) {
      steps.push({
        text: t('transactionFlow.subSteps.userMakesPayment', { username: firstPayerUser }),
        status: paymentDeclaredAtPayer1 ? 'done' : (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT ? 'current-action' : 'pending'),
        icon: paymentDeclaredAtPayer1 ? '✔️' : (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT && userId.value === agreedFirstPayerId ? '👉' : '⏳')
      });
    }
    if (paymentDeclaredAtPayer1 || 
        status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT ||
        status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.COMPLETED) {
      steps.push({
        text: t('transactionFlow.subSteps.userConfirmsReceipt', { username: secondPayerUser }),
        status: firstPaymentConfirmedByPayer2At ? 'done' : (status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION ? 'current-action' : 'pending'),
        icon: firstPaymentConfirmedByPayer2At ? '✔️' : (status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION && userId.value !== agreedFirstPayerId ? '👉' : '⏳')
      });
    }

    // Sub-steps for Payment Leg 2 (only if first leg is done)
    if (firstPaymentConfirmedByPayer2At ||
        status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT ||
        status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.COMPLETED) {
      steps.push({
        text: t('transactionFlow.subSteps.userMakesPayment', { username: secondPayerUser }),
        status: paymentDeclaredAtPayer2 ? 'done' : (status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT ? 'current-action' : 'pending'),
        icon: paymentDeclaredAtPayer2 ? '✔️' : (status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT && userId.value !== agreedFirstPayerId ? '👉' : '⏳')
      });
    }
    if (paymentDeclaredAtPayer2 ||
        status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION ||
        status === TransactionStatusEnum.COMPLETED) {
      steps.push({
        text: t('transactionFlow.subSteps.userConfirmsReceipt', { username: firstPayerUser }),
        status: secondPaymentConfirmedByPayer1At ? 'done' : (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION ? 'current-action' : 'pending'),
        icon: secondPaymentConfirmedByPayer1At ? '✔️' : (status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION && userId.value === agreedFirstPayerId ? '👉' : '⏳')
      });
    }
  }
  
  if (status === TransactionStatusEnum.COMPLETED) {
    steps.push({ text: t('transactionFlow.subSteps.transactionComplete'), status: 'done', icon: '🎉'});
  }
  if (status === TransactionStatusEnum.CANCELLED) {
    steps.push({ text: t('transactionFlow.subSteps.transactionCancelled'), status: 'error', icon: '❌'});
  }
  if (status === TransactionStatusEnum.DISPUTED) {
    steps.push({ text: t('transactionFlow.subSteps.transactionDisputed'), status: 'error', icon: '⚠️'});
  }

  return steps;
});

// Watch for errors from the composable
watch(storeError, (newError) => {
  if (newError) {
    naiveMessage.error(newError || t('transactionFlow.messages.errorOccurred'));
  }
});

const currentUserReceivingInfoStatus = computed<ReceivingInfoStatus>(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  const currentUserId = userId.value; // userId from useTransactionFlowLogic

  if (negotiation && currentUserId) {
    if (negotiation.partyA_Id === currentUserId) {
      return negotiation.partyA_receivingInfoStatus;
    }
    if (negotiation.partyB_Id === currentUserId) {
      return negotiation.partyB_receivingInfoStatus;
    }
  }
  // Default or fallback status if negotiation data isn't loaded yet,
  // or if the user is not a party (though this shouldn't happen in this context).
  // PENDING_INPUT is a sensible default as the PaymentReadinessGate is typically shown
  // when information is pending.
  return ReceivingInfoStatus.PENDING_INPUT;
});

const recipientPaymentDetails = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (!negotiation || negotiation.negotiationStatus !== 'FINALIZED' || !negotiation.finalizedPayerId || negotiation.finalizedPayerId !== userId.value) {
    return null;
  }
  // Determine who the recipient is (the other party)
  const recipientId = negotiation.partyA_Id === userId.value ? negotiation.partyB_Id : negotiation.partyA_Id;
  return recipientId === negotiation.partyA_Id ? negotiation.partyA_PaymentReceivingInfo : negotiation.partyB_PaymentReceivingInfo;
});

// For second payer payment: current user (second payer) needs first payer's banking details
const secondPayerRecipientDetails = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (!negotiation || negotiation.negotiationStatus !== 'FINALIZED' || !negotiation.finalizedPayerId) {
    return null;
  }
  // Current user is the second payer, need first payer's banking details
  const firstPayerId = negotiation.finalizedPayerId;
  return firstPayerId === negotiation.partyA_Id ? negotiation.partyA_PaymentReceivingInfo : negotiation.partyB_PaymentReceivingInfo;
});

const myPaymentDetailsForSharing = computed(() => {
  const negotiation = payerNegotiationStore.currentNegotiation;
  if (!negotiation || negotiation.negotiationStatus !== 'FINALIZED' || !negotiation.finalizedPayerId) {
    return null;
  }
  return negotiation.partyA_Id === userId.value ? negotiation.partyA_PaymentReceivingInfo : negotiation.partyB_PaymentReceivingInfo;
});

const openProposalModal = () => {
  proposalMessage.value = ''; // Reset message
  showDesignateModal.value = true; // Use the correct modal variable
};

const handleSubmitProposal = async () => {
  if (!currentTransaction.value || !otherUser.value?.id) {
    naiveMessage.error(t('transactionFlow.messages.errorOccurred'));
    return;
  }

  try {
    console.log('Submitting proposal to make other user pay first:', proposalMessage.value);
    await payerNegotiationStore.proposeFirstPayer(
      currentTransaction.value.id, 
      otherUser.value.id, // Always propose the other user as first payer
      proposalMessage.value || undefined
    );
    showDesignateModal.value = false;
    proposalMessage.value = ''; // Clear the message
    naiveMessage.success(t('transactionFlow.messages.proposalSubmitted'));
  } catch (error) {
    naiveMessage.error(t('transactionFlow.messages.errorOccurred'));
    console.error('Error submitting proposal:', error);
  }
};

// Cleanup socket listeners when component is unmounted
onUnmounted(() => {
  transactionStore.removeSocketListeners();
});

// Helper function to get translated reason text based on rule type
const getReasonTranslationKey = (rule: string, negotiation: any): string => {
  const tx = currentTransaction.value;
  if (!tx) return '';
  
  switch (rule) {
    case 'REPUTATION': {
      // Get the username of the recommended payer
      const recommendedPayerUsername = systemRecommendedPayerUsername.value;
      return t('transactionFlow.negotiation.reasonReputation', { username: recommendedPayerUsername });
    }
    case 'CURRENCY': {
      // Determine which currency is local (IRR)
      const localCurrency = tx.currencyA === 'IRR' ? tx.currencyA : (tx.currencyB === 'IRR' ? tx.currencyB : null);
      const recommendedPayerUsername = systemRecommendedPayerUsername.value;
      if (localCurrency) {
        return t('transactionFlow.negotiation.reasonCurrency', { 
          username: recommendedPayerUsername,
          currency: localCurrency 
        });
      }
      return t('transactionFlow.negotiation.reasonCurrencyGeneric', { username: recommendedPayerUsername });
    }
    case 'OFFER_CREATOR': {
      const recommendedPayerUsername = systemRecommendedPayerUsername.value;
      return t('transactionFlow.negotiation.reasonOfferCreator', { username: recommendedPayerUsername });
    }
    default:
      // Fallback to original reason if rule is unknown
      return negotiation.systemRecommendationReason ? 
        t('transactionFlow.negotiation.systemRecommendationReason', { reason: negotiation.systemRecommendationReason }) : '';
  }
};
</script>

<style scoped>
.transaction-flow-card-v3 {
  background-color: var(--n-card-color);
  padding: 10px;
  border-bottom: 1px solid var(--n-border-color);
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
  overflow-x: hidden;
  border-radius: 8px;
  box-shadow: var(--n-box-shadow-1);
}

/* Dark mode for transaction card */
[data-theme="dark"] .transaction-flow-card-v3 {
  background-color: var(--n-card-color);
  border-bottom: 1px solid var(--n-divider-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-header h2 {
  margin: 0;
  font-size: 1.2em;
  color: var(--n-text-color);
}

[data-theme="dark"] .card-header h2 {
  color: var(--n-text-color);
}

.transaction-id {
  font-size: 0.8em;
  color: var(--n-text-color-disabled);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.85em;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px dashed var(--n-border-color);
}
.user-info span { 
  color: var(--n-text-color-disabled);
}
.user-info strong { 
  color: var(--n-primary-color);
}

[data-theme="dark"] .user-info {
  border-bottom: 1px dashed var(--n-divider-color);
}

/* Vertical Stepper Adjustments */
.stepper {
  margin-bottom: 18px;
}

.stepper .n-step-content .n-step-description {
  font-size: 0.8em;
}

/* RTL Support for vertical stepper */
[dir="rtl"] .stepper :deep(.n-step-indicator) {
  left: auto;
  right: 0;
}

[dir="rtl"] .stepper :deep(.n-step-content) {
  padding-left: 0;
  padding-right: 40px;
  text-align: right;
}

[dir="rtl"] .stepper :deep(.n-step-splitter) {
  left: auto;
  right: 13px;
}

[dir="rtl"] .stepper :deep(.n-step-indicator-slot) {
  margin-right: 0;
}
.step-icon-custom {
  width: 28px; /* Slightly smaller icon */
  height: 28px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.9em; /* Smaller icon font */
  /* Naive UI step status will handle colors, or add custom if needed */
}


.current-step-details {
  padding: 12px;
  background-color: var(--n-color-embedded);
  border-radius: 6px;
  margin-bottom: 15px;
  max-height: 280px;
  overflow-y: auto;
  border: 1px solid var(--n-border-color);
}

[data-theme="dark"] .current-step-details {
  background-color: var(--n-color-embedded);
  border-color: var(--n-divider-color);
}

/* Enhanced scrollbar styling for current-step-details in dark mode */
[data-theme="dark"] .current-step-details::-webkit-scrollbar {
  width: 6px;
}

[data-theme="dark"] .current-step-details::-webkit-scrollbar-track {
  background: var(--n-color-embedded);
  border-radius: 3px;
}

[data-theme="dark"] .current-step-details::-webkit-scrollbar-thumb {
  background: var(--n-divider-color);
  border-radius: 3px;
}

[data-theme="dark"] .current-step-details::-webkit-scrollbar-thumb:hover {
  background: var(--n-border-color);
}

.current-step-details h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1.1em;
  color: var(--n-text-color);
}
.current-step-details .info-text {
  font-size: 0.9em;
  color: var(--n-text-color-disabled);
  margin-bottom: 12px;
  line-height: 1.4;
}
.current-step-details .n-button {
  margin-top: 10px;
}

.sub-steps ul {
  list-style: none;
  padding: 0;
  margin: 10px 0 0 0;
}
.sub-steps li {
  display: flex;
  align-items: center;
  font-size: 0.85em;
  margin-bottom: 6px;
  color: var(--n-text-color-disabled);
}
.sub-steps li .icon {
  margin-right: 8px;
  font-size: 1.1em;
}
.sub-steps li.done { color: var(--n-success-color); }
.sub-steps li.pending { color: var(--n-warning-color); }
.sub-steps li.current-action { color: var(--n-primary-color); font-weight: bold; }
.sub-steps li.error { color: var(--n-error-color); font-weight: bold; }


.action-buttons,
.action-buttons.secondary-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.action-buttons .n-button,
.secondary-actions .n-button {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  min-width: 0;
  overflow-x: hidden;
}

/* If you want secondary actions side-by-side, use flex-wrap and prevent overflow */
.secondary-actions {
  flex-direction: row;
  gap: 10px;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.secondary-actions .n-button {
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
}


.timer-area {
  text-align: center;
  font-size: 1em;
  padding: 8px;
  border-radius: 6px;
  margin-top: 10px;
  border: 1px solid var(--n-border-color);
}
.timer-area.critical { 
  background-color: var(--n-error-color-suppl); 
  color: var(--n-error-color); 
  font-weight: bold; 
  border-color: var(--n-error-color);
}
.timer-area.info { 
  background-color: var(--n-info-color-suppl); 
  color: var(--n-info-color); 
  border-color: var(--n-info-color);
}
.timer-area.expired { 
  background-color: var(--n-warning-color-suppl); 
  color: var(--n-warning-color); 
  font-weight: bold; 
  border-color: var(--n-warning-color);
}

[data-theme="dark"] .timer-area.critical { 
  background-color: rgba(208, 48, 80, 0.15); 
  color: var(--n-error-color-suppl);
  border-color: var(--n-error-color-suppl);
}
[data-theme="dark"] .timer-area.info { 
  background-color: rgba(24, 160, 251, 0.15); 
  color: var(--n-info-color-suppl);
  border-color: var(--n-info-color-suppl);
}
[data-theme="dark"] .timer-area.expired { 
  background-color: rgba(250, 173, 20, 0.15); 
  color: var(--n-warning-color-suppl);
  border-color: var(--n-warning-color-suppl);
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.9em;
  color: var(--n-text-color-placeholder);
  font-weight: 500;
}

.timer-value {
  font-size: 1.5em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: var(--n-text-color);
}

.timer-critical .timer-value {
  color: var(--n-warning-color);
}

.timer-expired .timer-value {
  color: var(--n-error-color);
}

.timer-elapsed .timer-value {
  color: var(--n-info-color);
}

[data-theme="dark"] .timer-critical .timer-value {
  color: var(--n-warning-color-suppl);
}

[data-theme="dark"] .timer-expired .timer-value {
  color: var(--n-error-color-suppl);
}

[data-theme="dark"] .timer-elapsed .timer-value {
  color: var(--n-info-color-suppl);
}

.proposal-info {
  margin-bottom: 16px;
}

.proposal-info p {
  margin-bottom: 12px;
  line-height: 1.5;
}

.proposal-info p:first-child {
  font-weight: 500;
  color: var(--n-text-color-1);
}

/* Enhanced dark mode support for nested UI elements */
[data-theme="dark"] .negotiation-ui {
  background-color: var(--n-color-embedded);
  border-color: var(--n-divider-color);
}

[data-theme="dark"] .finalized-negotiation-details {
  background-color: var(--n-color-embedded);
  border-color: var(--n-divider-color);
}

/* Better focus states for dark mode */
[data-theme="dark"] .action-buttons .n-button:focus,
[data-theme="dark"] .secondary-actions .n-button:focus {
  box-shadow: 0 0 0 2px var(--n-primary-color-suppl);
}

/* Enhanced styling for recommendation and proposal sections */
.recommendation-section {
  margin-bottom: 16px;
  border: 2px solid var(--n-primary-color);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--n-primary-color-hover-suppl);
}

.recommendation-header {
  margin-bottom: 12px;
}

.recommendation-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-primary-color);
}

.recommendation-content {
  line-height: 1.5;
}

.recommendation-text {
  margin: 0;
  color: var(--n-text-color-1);
}

.recommendation-main {
  font-weight: 500;
  margin-bottom: 8px;
}

.recommendation-reason {
  background-color: var(--n-info-color-suppl);
  border-left: 4px solid var(--n-info-color);
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
  font-size: 14px;
}

.proposal-section {
  margin-bottom: 16px;
  border: 1px solid var(--n-divider-color);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--n-card-color);
}

.proposal-header {
  margin-bottom: 12px;
}

.proposal-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.proposal-content {
  line-height: 1.5;
}

.proposal-text {
  margin: 0 0 8px 0;
  font-weight: 500;
  color: var(--n-text-color-1);
}

.proposal-details {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

/* Dark mode support for new sections */
[data-theme="dark"] .recommendation-section {
  background-color: var(--n-primary-color-suppl);
  border-color: var(--n-primary-color);
}

[data-theme="dark"] .recommendation-title {
  color: var(--n-primary-color-hover);
}

[data-theme="dark"] .recommendation-reason {
  background-color: var(--n-info-color-suppl);
  border-left-color: var(--n-info-color-hover);
}

[data-theme="dark"] .proposal-section {
  background-color: var(--n-color-embedded);
  border-color: var(--n-divider-color);
}
</style>
