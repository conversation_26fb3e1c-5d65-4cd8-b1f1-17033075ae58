<template>
  <div class="offer-details-view">
    <div v-if="isLoading" class="loading-container">
      <NSpin :show="true" size="large">
        <div class="loading-placeholder">
          {{ $t('common.loading') }}...
        </div>
      </NSpin>
    </div>

    <div v-else-if="error" class="error-container">
      <NAlert type="error" :title="$t('errors.failedToLoadOffer')">
        {{ error }}
      </NAlert>
      <NButton @click="router.back()" class="back-button">
        {{ $t('common.goBack') }}
      </NButton>
    </div>

    <div v-else-if="offer" class="offer-content">
      <div class="offer-header">
        <NButton @click="router.back()" quaternary circle>
          <template #icon>
            <NIcon :component="ArrowBackOutline" />
          </template>
        </NButton>
        <h1 class="offer-title">{{ $t('offerDetails.title') }}</h1>
      </div>

      <div class="offer-card-container">
        <NCard>
          <div class="offer-info">
            <div class="offer-type">
              <NTag :type="offer.type === 'BUY' ? 'success' : 'info'">
                {{ offer.type === 'BUY' ? $t('offers.buy') : $t('offerDetails.sellCAD') }}
              </NTag>
            </div>

            <div class="currency-info">
              <h2>{{ offer.currencyPair }}</h2>
              <p class="amount">{{ $t('offers.amount') }}: {{ offer.amount.toLocaleString() }}</p>
              <p class="rate" v-if="offer.rate">
                {{ $t('offers.rate') }}: {{ offer.rate.toLocaleString() }}
              </p>
            </div>

            <div class="offer-meta">
              <div class="creator-info">
                <ReputationIcon :level="offer.user?.reputationLevel || 1" />
                <span class="username">{{ offer.user?.username || offer.user?.email }}</span>
              </div>
              
              <div class="offer-status">
                <NTag :type="getStatusType(offer.status)">
                  {{ $t(`offers.status.${offer.status.toLowerCase()}`) }}
                </NTag>
              </div>

              <div class="created-date">
                <span>{{ $t('offers.createdAt') }}: {{ formatDate(offer.createdAt) }}</span>
              </div>
            </div>

            <div v-if="offer.description" class="offer-description">
              <h3>{{ $t('offers.description') }}</h3>
              <p>{{ offer.description }}</p>
            </div>
          </div>

          <template #action>
            <div class="offer-actions">
              <!-- Show edit button if user owns the offer -->
              <div v-if="isOwner" class="owner-actions">
                <NButton type="primary" @click="editOffer" data-testid="edit-offer-btn">
                  {{ $t('offers.edit') }}
                </NButton>
                <NButton v-if="offer.status === 'ACTIVE'" @click="deactivateOffer" :loading="isUpdating">
                  {{ $t('offers.deactivate') }}
                </NButton>
                <NButton v-else-if="offer.status === 'INACTIVE'" @click="activateOffer" :loading="isUpdating">
                  {{ $t('offers.activate') }}
                </NButton>
              </div>              <!-- Show interest button if user doesn't own the offer -->
              <div v-else-if="offer.status === 'ACTIVE'" class="interest-actions">
                <div v-if="offer.userInterest">
                  <NAlert type="info" :title="$t('interests.alreadyExpressed')">
                    {{ $t('interests.status') }}: {{ $t(`interests.statuses.${offer.userInterest.status.toLowerCase()}`) }}
                  </NAlert>
                </div>
                <div v-else>
                  <NButton type="primary" @click="showInterest" :loading="isShowingInterest">
                    {{ $t('offers.showInterest') }}
                  </NButton>
                </div>
              </div>

              <div v-else class="inactive-offer">
                <NAlert type="warning">
                  {{ $t('offers.inactiveOfferMessage') }}
                </NAlert>
              </div>
            </div>
          </template>
        </NCard>
      </div>

      <!-- Show interests section if user owns the offer -->
      <div v-if="isOwner && interests.length > 0" class="interests-section">
        <h3>{{ $t('offers.receivedInterests') }}</h3>
        <div class="interests-list">
          <InterestRequestCard
            v-for="interest in interests"
            :key="interest.id"
            :interest="interest"
            @accept="handleAcceptInterest"
            @decline="handleDeclineInterest"
          />
        </div>
      </div>
    </div>

    <div v-else class="not-found">
      <NAlert type="warning" :title="$t('errors.offerNotFound')">
        {{ $t('errors.offerNotFoundMessage') }}
      </NAlert>
      <NButton @click="router.push('/browse-offers')" type="primary">
        {{ $t('offers.browseOffers') }}
      </NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { 
  NCard, 
  NButton, 
  NTag, 
  NAlert, 
  NSpin, 
  NIcon,
  useMessage 
} from 'naive-ui';
import { ArrowBackOutline } from '@vicons/ionicons5';
import { useAuthStore } from '@/stores/auth';
import { offerService } from '@/services/offerService';
import { acceptInterest, declineInterest } from '@/services/interestService';
import ReputationIcon from '@/components/ReputationIcon.vue';
import InterestRequestCard from '@/components/InterestRequestCard.vue';
import type { OfferWithUser, Interest } from '@/types/offer';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const message = useMessage();
const authStore = useAuthStore();

const offer = ref<OfferWithUser | null>(null);
const interests = ref<Interest[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const isUpdating = ref(false);
const isShowingInterest = ref(false);

const isOwner = computed(() => {
  return offer.value?.isOwner || false;
});

const offerId = computed(() => route.params.id as string);

onMounted(async () => {
  await loadOffer();
  if (isOwner.value) {
    await loadInterests();
  }
});

async function loadOffer() {
  try {
    isLoading.value = true;
    error.value = null;
    
    const offerData = await offerService.getOfferById(offerId.value);
    offer.value = offerData;
  } catch (err: any) {
    console.error('Failed to load offer:', err);
    error.value = err.response?.data?.error || t('errors.failedToLoadOffer');
  } finally {
    isLoading.value = false;
  }
}

async function loadInterests() {
  if (!offer.value) return;
  
  try {
    // This would need to be implemented in offerService
    // const response = await offerService.getOfferInterests(offer.value.id);
    // interests.value = response.data;
  } catch (err) {
    console.error('Failed to load interests:', err);
  }
}

function editOffer() {
  router.push(`/my-offers/${offerId.value}/edit`);
}

async function activateOffer() {
  if (!offer.value) return;
  
  try {
    isUpdating.value = true;
    await offerService.updateOfferStatus(offer.value.id, 'ACTIVE');
    offer.value.status = 'ACTIVE';
    message.success(t('offers.activatedSuccessfully'));
  } catch (err: any) {
    console.error('Failed to activate offer:', err);
    message.error(err.response?.data?.error || t('errors.failedToUpdateOffer'));
  } finally {
    isUpdating.value = false;
  }
}

async function deactivateOffer() {
  if (!offer.value) return;
  
  try {
    isUpdating.value = true;
    await offerService.updateOfferStatus(offer.value.id, 'INACTIVE');
    offer.value.status = 'INACTIVE';
    message.success(t('offers.deactivatedSuccessfully'));
  } catch (err: any) {
    console.error('Failed to deactivate offer:', err);
    message.error(err.response?.data?.error || t('errors.failedToUpdateOffer'));
  } finally {
    isUpdating.value = false;
  }
}

async function showInterest() {
  if (!offer.value) return;
  
  try {
    isShowingInterest.value = true;
    await offerService.expressInterest(offer.value.id);
    message.success(t('offers.interestExpressedSuccessfully'));
  } catch (err: any) {
    console.error('Failed to express interest:', err);
    message.error(err.response?.data?.error || t('errors.failedToExpressInterest'));
  } finally {
    isShowingInterest.value = false;
  }
}

async function handleAcceptInterest(interestId: string) {
  try {
    await acceptInterest(interestId);
    message.success(t('interests.acceptedSuccessfully'));
    await loadInterests(); // Reload interests
  } catch (err: any) {
    console.error('Failed to accept interest:', err);
    message.error(err.response?.data?.error || t('errors.failedToAcceptInterest'));
  }
}

async function handleDeclineInterest(interestId: string, reason?: string) {
  try {
    await declineInterest(interestId, reason);
    message.success(t('interests.declinedSuccessfully'));
    await loadInterests(); // Reload interests
  } catch (err: any) {
    console.error('Failed to decline interest:', err);
    message.error(err.response?.data?.error || t('errors.failedToDeclineInterest'));
  }
}

function getStatusType(status: string) {
  switch (status) {
    case 'ACTIVE': return 'success';
    case 'INACTIVE': return 'warning';
    case 'CLOSED': return 'error';
    default: return 'default';
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString();
}
</script>

<style scoped>
.offer-details-view {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-placeholder {
  text-align: center;
  color: var(--text-color-2);
}

.error-container {
  text-align: center;
  margin: 2rem 0;
}

.back-button {
  margin-top: 1rem;
}

.offer-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.offer-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.offer-card-container {
  margin-bottom: 2rem;
}

.offer-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.offer-type {
  align-self: flex-start;
}

.currency-info h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
}

.currency-info p {
  margin: 0.25rem 0;
  color: var(--text-color-2);
}

.offer-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.username {
  font-weight: 500;
}

.created-date span {
  color: var(--text-color-3);
  font-size: 0.875rem;
}

.offer-description {
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

.offer-description h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.offer-description p {
  margin: 0;
  line-height: 1.5;
}

.offer-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.owner-actions,
.interest-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.interests-section {
  margin-top: 2rem;
}

.interests-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.interests-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.not-found {
  text-align: center;
  margin: 2rem 0;
}

.not-found .n-button {
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .offer-details-view {
    padding: 0.5rem;
  }
  
  .offer-header {
    margin-bottom: 1rem;
  }
  
  .offer-title {
    font-size: 1.25rem;
  }
  
  .offer-meta {
    gap: 0.5rem;
  }
  
  .offer-actions {
    flex-direction: column;
  }
  
  .owner-actions,
  .interest-actions {
    flex-direction: column;
  }
}
</style>
