import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { promises as fs } from 'fs';
import path from 'path';
import { ClientLogService } from '../../services/clientLogService';
import type { ClientReportPayload, ReportDetails, LogEntry, UserContext } from '../../types/schemas/debugSchemas';

// Mock fs operations
vi.mock('fs', () => ({
  promises: {
    access: vi.fn(),
    mkdir: vi.fn(),
    appendFile: vi.fn(),
    stat: vi.fn()
  }
}));

// Mock LogRotationService
const mockRotateIfNeeded = vi.fn().mockResolvedValue(undefined);
const mockCleanupOldLogs = vi.fn().mockResolvedValue(undefined);

vi.mock('../../services/logRotationService', () => ({
  LogRotationService: class MockLogRotationService {
    constructor(logDirectory: string) {}
    rotateIfNeeded = mockRotateIfNeeded;
    cleanupOldLogs = mockCleanupOldLogs;
  }
}));

describe('ClientLogService', () => {
  let clientLogService: ClientLogService;
  let testLogDirectory: string;
  let mockFs: any;
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset mock functions
    mockRotateIfNeeded.mockClear();
    mockCleanupOldLogs.mockClear();
    
    // Set up test directory
    testLogDirectory = '/tmp/test-logs';
    clientLogService = new ClientLogService(testLogDirectory);
    
    // Mock fs promises
    mockFs = vi.mocked(fs);
    mockFs.access.mockResolvedValue(undefined);
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.appendFile.mockResolvedValue(undefined);
    mockFs.stat.mockResolvedValue({ size: 1024 } as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('saveReport', () => {
    it('should save complete bug report with all form fields', async () => {
      // Create comprehensive test data matching what the frontend sends
      const userContext: UserContext = {
        currentPage: 'http://localhost:5173/browse-offers',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        viewport: { width: 1920, height: 1080 },
        timestamp: '2024-01-15T10:30:00.000Z',
        userActions: [
          { action: 'navigation', timestamp: '2024-01-15T10:29:00.000Z', details: { to: '/browse-offers' } },
          { action: 'opened_debug_report_modal', timestamp: '2024-01-15T10:30:00.000Z' }
        ],
        routeHistory: ['/home', '/browse-offers']
      };

      const reportDetails: ReportDetails = {
        type: 'bug',
        severity: 'medium',
        title: 'Button click not working properly',
        description: 'When I click the submit button, nothing happens and the form does not submit.',
        stepsToReproduce: '1. Navigate to the offer creation page\n2. Fill out the form\n3. Click submit button\n4. Notice nothing happens',
        expectedBehavior: 'Form should submit successfully and show confirmation message',
        actualBehavior: 'Button appears to be clicked but form does not submit, no error message shown',
        additionalNotes: 'This happens on both Chrome and Firefox browsers',
        userContext,
        correlatedLogEntries: [],
        reportTags: ['urgent', 'frontend', 'form-submission']
      };

      const logEntries: LogEntry[] = [
        {
          timestamp: '2024-01-15T10:29:30.000Z',
          level: 'INFO',
          message: 'User navigated to offer creation page',
          context: { page: '/create-offer' },
          url: 'http://localhost:5173/create-offer'
        },
        {
          timestamp: '2024-01-15T10:29:45.000Z',
          level: 'ERROR',
          message: 'Form submission failed',
          context: { formData: { title: 'Test Offer' } },
          url: 'http://localhost:5173/create-offer',
          stackTrace: 'Error: Network request failed\n    at submitForm (/src/views/CreateOfferView.vue:123:10)'
        }
      ];

      const clientReportPayload: ClientReportPayload = {
        logs: logEntries,
        reportDetails,
        timestamp: '2024-01-15T10:30:00.000Z',
        sessionId: 'session_1705312200000_abc123'
      };

      // Call the service method
      const reportId = await clientLogService.saveReport(clientReportPayload);

      // Verify that a report ID was generated
      expect(reportId).toMatch(/^report_\d+_[a-z0-9]+$/);

      // Verify that appendFile was called
      expect(mockFs.appendFile).toHaveBeenCalledTimes(1);

      // Get the actual log entry that was written
      const [filePath, logLine] = mockFs.appendFile.mock.calls[0];
      
      // Verify file path
      expect(filePath).toBe(path.join(testLogDirectory, 'client-reports.log'));
      
      // Parse and verify the log entry structure
      const logEntry = JSON.parse(logLine.trim());
      
      // Verify top-level log entry structure
      expect(logEntry).toMatchObject({
        reportId: expect.stringMatching(/^report_\d+_[a-z0-9]+$/),
        timestamp: expect.any(String),
        serverReceivedAt: expect.any(String),
        clientTimestamp: '2024-01-15T10:30:00.000Z',
        sessionId: 'session_1705312200000_abc123',
        reportType: 'bug',
        reportSeverity: 'medium',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        currentUrl: 'http://localhost:5173/browse-offers',
        userNotes: 'This happens on both Chrome and Firefox browsers',
        logCount: 2,
        logs: logEntries
      });

      // Verify that ALL form fields are preserved in the logs array
      const savedReportDetails = logEntry.logs.find((log: any) => 
        log.context && log.context.reportDetails
      );

      // If reportDetails are stored separately, verify they contain all fields
      // This tests the actual structure that gets saved
      expect(logEntry.logs).toEqual(logEntries);
      
      // Verify hasTags indicator
      expect(logEntry).toHaveProperty('hasTags', true);
    });

    it('should save feature request with minimal required fields', async () => {
      const reportDetails: ReportDetails = {
        type: 'feature-request',
        severity: 'low',
        title: 'Add dark mode toggle',
        description: 'Please add a dark mode option to the application',
        userContext: {
          currentPage: 'http://localhost:5173/settings',
          userAgent: 'Mozilla/5.0',
          viewport: { width: 1366, height: 768 },
          timestamp: '2024-01-15T11:00:00.000Z',
          userActions: [],
          routeHistory: ['/settings']
        },
        correlatedLogEntries: [],
        reportTags: ['enhancement']
      };

      const clientReportPayload: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T11:00:00.000Z',
        sessionId: 'session_feature_request'
      };

      const reportId = await clientLogService.saveReport(clientReportPayload);

      expect(reportId).toBeTruthy();
      expect(mockFs.appendFile).toHaveBeenCalledTimes(1);

      const [, logLine] = mockFs.appendFile.mock.calls[0];
      const logEntry = JSON.parse(logLine.trim());      expect(logEntry).toMatchObject({
        reportType: 'feature-request',
        reportSeverity: 'low',
        currentUrl: 'http://localhost:5173/settings',
        logCount: 0,
        userNotes: null // No additionalNotes provided
      });
    });

    it('should save critical performance issue with complete context', async () => {
      const performanceLogs: LogEntry[] = [
        {
          timestamp: '2024-01-15T12:00:00.000Z',
          level: 'WARN',
          message: 'Slow API response detected',
          context: { responseTime: 5000, endpoint: '/api/offers/browse' },
          url: 'http://localhost:5173/browse-offers'
        },
        {
          timestamp: '2024-01-15T12:00:05.000Z',
          level: 'ERROR',
          message: 'Request timeout',
          context: { timeout: 10000 },
          url: 'http://localhost:5173/browse-offers',
          stackTrace: 'TimeoutError: Request timed out after 10000ms'
        }
      ];

      const reportDetails: ReportDetails = {
        type: 'performance',
        severity: 'critical',
        title: 'Browse offers page extremely slow',
        description: 'The browse offers page takes more than 10 seconds to load and sometimes times out completely',
        stepsToReproduce: '1. Navigate to browse offers page\n2. Wait for loading\n3. Observe slow response times',
        expectedBehavior: 'Page should load within 2-3 seconds',
        actualBehavior: 'Page takes 10+ seconds to load, sometimes fails with timeout',
        additionalNotes: 'Issue started happening after recent deployment, affects all users',
        userContext: {
          currentPage: 'http://localhost:5173/browse-offers',
          userAgent: 'Mozilla/5.0 (compatible performance test)',
          viewport: { width: 1440, height: 900 },
          timestamp: '2024-01-15T12:00:00.000Z',
          userActions: [
            { action: 'page_load_start', timestamp: '2024-01-15T12:00:00.000Z' },
            { action: 'api_request_slow', timestamp: '2024-01-15T12:00:01.000Z', details: { endpoint: '/api/offers/browse' } }
          ],
          routeHistory: ['/home', '/browse-offers']
        },
        correlatedLogEntries: performanceLogs,
        reportTags: ['performance', 'critical', 'api-timeout']
      };

      const clientReportPayload: ClientReportPayload = {
        logs: performanceLogs,
        reportDetails,
        timestamp: '2024-01-15T12:00:10.000Z',
        sessionId: 'session_performance_critical'
      };

      const reportId = await clientLogService.saveReport(clientReportPayload);

      expect(reportId).toBeTruthy();
      expect(mockFs.appendFile).toHaveBeenCalledTimes(1);

      const [, logLine] = mockFs.appendFile.mock.calls[0];
      const logEntry = JSON.parse(logLine.trim());      // Verify all performance-related data is saved
      expect(logEntry).toMatchObject({
        reportType: 'performance',
        reportSeverity: 'critical',
        userNotes: 'Issue started happening after recent deployment, affects all users',
        logCount: 2
      });

      // Verify tags are properly saved
      expect(logEntry.hasTags).toBe(true);
      expect(logEntry.tags).toEqual(['performance', 'critical', 'api-timeout']);

      // Verify performance logs are preserved
      expect(logEntry.logs).toHaveLength(2);
      expect(logEntry.logs[0]).toMatchObject({
        level: 'WARN',
        message: 'Slow API response detected',
        context: { responseTime: 5000, endpoint: '/api/offers/browse' }
      });
    });

    it('should handle reports with no additional notes or tags', async () => {
      const reportDetails: ReportDetails = {
        type: 'ui-ux',
        severity: 'medium',
        title: 'Button alignment issue',
        description: 'Submit button is misaligned on mobile devices',
        userContext: {
          currentPage: 'http://localhost:5173/create-offer',
          userAgent: 'Mobile Safari',
          viewport: { width: 375, height: 667 },
          timestamp: '2024-01-15T13:00:00.000Z',
          userActions: [],
          routeHistory: ['/create-offer']
        },
        correlatedLogEntries: []
        // No additionalNotes or reportTags
      };

      const clientReportPayload: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T13:00:00.000Z',
        sessionId: 'session_ui_issue'
      };

      const reportId = await clientLogService.saveReport(clientReportPayload);

      expect(reportId).toBeTruthy();
      
      const [, logLine] = mockFs.appendFile.mock.calls[0];
      const logEntry = JSON.parse(logLine.trim());      expect(logEntry).toMatchObject({
        reportType: 'ui-ux',
        reportSeverity: 'medium',
        userNotes: null
      });
      
      // Should not have tags since none provided
      expect(logEntry.hasTags).toBe(false);
      expect(logEntry.tags).toBeUndefined();
    });

    it('should handle file system errors gracefully', async () => {
      // Mock file system error
      mockFs.appendFile.mockRejectedValue(new Error('Disk full'));

      const reportDetails: ReportDetails = {
        type: 'bug',
        severity: 'low',
        title: 'Test error handling',
        description: 'Testing error scenarios',
        userContext: {
          currentPage: 'http://localhost:5173/test',
          userAgent: 'Test agent',
          viewport: { width: 1024, height: 768 },
          timestamp: '2024-01-15T14:00:00.000Z',
          userActions: [],
          routeHistory: []
        },
        correlatedLogEntries: []
      };

      const clientReportPayload: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T14:00:00.000Z',
        sessionId: 'session_error_test'
      };

      // Expect the service to throw an error
      await expect(clientLogService.saveReport(clientReportPayload))
        .rejects
        .toThrow('Failed to save debug report to server');
    });
  });

  describe('Report ID Generation', () => {
    it('should generate unique report IDs for concurrent reports', async () => {
      const reportDetails: ReportDetails = {
        type: 'bug',
        severity: 'low',
        title: 'Concurrent test',
        description: 'Testing concurrent report submission',
        userContext: {
          currentPage: 'http://localhost:5173/test',
          userAgent: 'Test',
          viewport: { width: 1024, height: 768 },
          timestamp: '2024-01-15T15:00:00.000Z',
          userActions: [],
          routeHistory: []
        },
        correlatedLogEntries: []
      };

      const payload1: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T15:00:00.000Z',
        sessionId: 'session_1'
      };

      const payload2: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T15:00:01.000Z',
        sessionId: 'session_2'
      };

      const [reportId1, reportId2] = await Promise.all([
        clientLogService.saveReport(payload1),
        clientLogService.saveReport(payload2)
      ]);

      expect(reportId1).not.toBe(reportId2);
      expect(reportId1).toMatch(/^report_\d+_[a-z0-9]+$/);
      expect(reportId2).toMatch(/^report_\d+_[a-z0-9]+$/);
    });
  });

  describe('Log Directory Management', () => {
    it('should create log directory if it does not exist', async () => {
      // Mock directory not existing
      mockFs.access.mockRejectedValue(new Error('Directory not found'));

      const reportDetails: ReportDetails = {
        type: 'other',
        severity: 'low',
        title: 'Directory test',
        description: 'Testing directory creation',
        userContext: {
          currentPage: 'http://localhost:5173/test',
          userAgent: 'Test',
          viewport: { width: 1024, height: 768 },
          timestamp: '2024-01-15T16:00:00.000Z',
          userActions: [],
          routeHistory: []
        },
        correlatedLogEntries: []
      };

      const clientReportPayload: ClientReportPayload = {
        logs: [],
        reportDetails,
        timestamp: '2024-01-15T16:00:00.000Z',
        sessionId: 'session_dir_test'
      };

      await clientLogService.saveReport(clientReportPayload);

      // Verify that mkdir was called to create the directory
      expect(mockFs.mkdir).toHaveBeenCalledWith(testLogDirectory, { recursive: true });
    });
  });
});
