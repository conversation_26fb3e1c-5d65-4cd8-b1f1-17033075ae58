/**
 * Client-side logging types for debug reporting system
 */

export type LogLevel = 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';

export type ReportType = 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other';

export type ReportSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  url?: string;
  stackTrace?: string;
}

export interface ReportDetails {
  type: ReportType;
  severity: ReportSeverity;
  title: string;
  description: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  additionalNotes?: string;
  // Enhanced correlation fields
  userContext?: UserContext;
  correlatedLogEntries?: LogEntry[];
  reportTags?: string[];
}

export interface UserContext {
  currentPage: string;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
  timestamp: string;
  userActions: UserAction[];
  routeHistory: string[];
}

export interface UserAction {
  action: string;
  timestamp: string;
  details?: Record<string, any>;
}

export interface DiagnosticData {
  connectionStatus: {
    isConnected: boolean;
    connectionQuality: string;
    connectionStatus: string;
    transportType: string;
    reconnectAttempts: number;
    isReconnecting: boolean;
    lastDisconnectReason: string | null;
    socketId?: string;
    socketConnected?: boolean;
  };
  piniaStoreSnapshot: {
    [storeName: string]: any;
  };
  captureTimestamp: string;
}

export interface UserIdentification {
  userId: string;
  email?: string;
  username?: string;
}

export interface ClientReportPayload {
  logs: LogEntry[];
  reportDetails: ReportDetails;
  timestamp: string;
  sessionId?: string;
  diagnosticData?: DiagnosticData;
  userIdentification?: UserIdentification;
}

export interface ClientReportResponse {
  success: boolean;
  message: string;
  reportId?: string;
  isNetworkError?: boolean;
}

export interface ReportTypeOption {
  value: ReportType;
  label: string;
  description: string;
  icon: string;
  color: string;
  tags: string[];
}

export interface ReportSeverityOption {
  value: ReportSeverity;
  label: string;
  description: string;
  color: string;
}

export interface ReportTag {
  id: string;
  label: string;
  color: string;
  category: 'type' | 'component' | 'feature' | 'priority';
}

export interface ReportFilterConfig {
  types: ReportType[];
  severities: ReportSeverity[];
  tags: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// Offline report storage types
export interface OfflineReport {
  id: string;
  reportPayload: ClientReportPayload;
  timestamp: string;
  retryCount: number;
  lastRetryAt?: string;
}

export interface OfflineReportStorage {
  reports: OfflineReport[];
  lastCleanup: string;
}

// Form draft storage types
export interface FormDraft {
  id: string;
  formData: ReportDetails;
  timestamp: string;
  lastModified: string;
}

export interface FormDraftStorage {
  drafts: FormDraft[];
  activeDraftId?: string;
  lastCleanup: string;
}
