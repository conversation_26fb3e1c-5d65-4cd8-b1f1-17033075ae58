<template>
  <div class="not-found-container" :data-theme="themeStore.currentTheme">
    <div class="error-content">
      <!-- Animated Trading Chart Background -->
      <div class="chart-background">
        <svg class="trading-chart" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
          <!-- Chart Grid -->
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="var(--grid-color)" stroke-width="0.5" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          <!-- Broken Trading Line -->
          <path class="chart-line" 
                d="M 50 200 Q 150 180 200 160 Q 250 140 300 120 Q 350 100 400 200 L 450 350" 
                fill="none" 
                stroke="var(--primary-color)" 
                stroke-width="3"
                stroke-dasharray="10,5"/>
          
          <!-- 404 Data Points -->
          <circle cx="200" cy="160" r="6" fill="var(--error-color)" class="data-point">
            <animate attributeName="r" values="6;10;6" dur="2s" repeatCount="indefinite"/>
          </circle>
          <circle cx="300" cy="120" r="6" fill="var(--error-color)" class="data-point">
            <animate attributeName="r" values="6;10;6" dur="2s" begin="0.5s" repeatCount="indefinite"/>
          </circle>
          <circle cx="400" cy="200" r="6" fill="var(--error-color)" class="data-point">
            <animate attributeName="r" values="6;10;6" dur="2s" begin="1s" repeatCount="indefinite"/>
          </circle>
          
          <!-- Floating Currency Symbols -->
          <text x="100" y="100" class="currency-symbol" fill="var(--text-secondary)">$</text>
          <text x="600" y="80" class="currency-symbol" fill="var(--text-secondary)">€</text>
          <text x="700" y="150" class="currency-symbol" fill="var(--text-secondary)">£</text>
          <text x="150" y="350" class="currency-symbol" fill="var(--text-secondary)">¥</text>
        </svg>
      </div>

      <!-- Main Error Display -->
      <div class="error-display">
        <!-- Exchange Rate Display -->
        <div class="exchange-rate-board">
          <div class="rate-header">
            <n-icon size="32" color="var(--error-color)">
              <TrendingDownOutline />
            </n-icon>
            <span class="exchange-title">Exchange Rate</span>
          </div>
          
          <div class="rate-display">
            <div class="currency-pair">
              <span class="base-currency">404</span>
              <span class="divider">/</span>
              <span class="quote-currency">FOUND</span>
            </div>
            <div class="rate-value error-rate">
              <span class="rate-number">0.000</span>
              <div class="rate-status">
                <n-icon size="16" color="var(--error-color)">
                  <CloseCircleOutline />
                </n-icon>
                <span>NOT AVAILABLE</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div class="error-message">
          <h1 class="error-title">Exchange Temporarily Closed</h1>
          <p class="error-subtitle">
            The trading pair you're looking for doesn't exist in our exchange.
            <br>
            Let's get you back to trading profitable opportunities!
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <n-button 
            type="primary" 
            size="large" 
            @click="goHome"
            class="primary-action"
          >
            <template #icon>
              <n-icon><HomeOutline /></n-icon>
            </template>
            Return to Trading Floor
          </n-button>
          
          <n-button 
            size="large" 
            @click="browseOffers"
            class="secondary-action"
          >
            <template #icon>
              <n-icon><SearchOutline /></n-icon>
            </template>
            Browse Live Offers
          </n-button>
          
          <n-button 
            size="large" 
            @click="createOffer"
            class="secondary-action"
          >
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            Create New Offer
          </n-button>
        </div>

        <!-- Help Section -->
        <div class="help-section">
          <n-collapse>
            <n-collapse-item title="Need Help Finding Something?" name="help">
              <div class="help-content">
                <div class="help-item">
                  <n-icon size="20"><DocumentTextOutline /></n-icon>
                  <div>
                    <strong>Browse Offers:</strong> Discover currency exchange opportunities from other users
                  </div>
                </div>
                <div class="help-item">
                  <n-icon size="20"><PersonOutline /></n-icon>
                  <div>
                    <strong>My Profile:</strong> Manage your account settings and verification status
                  </div>
                </div>
                <div class="help-item">
                  <n-icon size="20"><ChatbubbleOutline /></n-icon>
                  <div>
                    <strong>Recent Chats:</strong> Continue conversations with trading partners
                  </div>
                </div>
              </div>
            </n-collapse-item>
          </n-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { 
  NButton, 
  NIcon, 
  NCollapse, 
  NCollapseItem 
} from 'naive-ui'
import {
  HomeOutline,
  SearchOutline,
  AddOutline,
  TrendingDownOutline,
  CloseCircleOutline,
  DocumentTextOutline,
  PersonOutline,
  ChatbubbleOutline
} from '@vicons/ionicons5'

const router = useRouter()
const themeStore = useThemeStore()

const goHome = () => {
  router.push('/')
}

const browseOffers = () => {
  router.push('/browse-offers')
}

const createOffer = () => {
  router.push('/create-offer')
}
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  position: relative;
  overflow: hidden;
  
  /* Theme Variables */
  --primary-color: #18a058;
  --error-color: #d03050;
  --grid-color: #e0e0e6;
  --text-primary: #333;
  --text-secondary: #666;
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --border-color: #e0e0e6;
}

.not-found-container[data-theme="dark"] {
  --primary-color: #63e2b7;
  --error-color: #e88080;
  --grid-color: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --border-color: #3a3a3a;
}

.chart-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: 0;
}

.trading-chart {
  width: 100%;
  height: 100%;
}

.chart-line {
  filter: drop-shadow(0 0 3px var(--primary-color));
}

.currency-symbol {
  font-size: 24px;
  font-weight: bold;
  opacity: 0.3;
  animation: float 3s ease-in-out infinite;
}

.currency-symbol:nth-child(4) { animation-delay: -0.5s; }
.currency-symbol:nth-child(5) { animation-delay: -1s; }
.currency-symbol:nth-child(6) { animation-delay: -1.5s; }
.currency-symbol:nth-child(7) { animation-delay: -2s; }

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.error-content {
  position: relative;
  z-index: 1;
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.exchange-rate-board {
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.
exchange-rate-board[data-theme="dark"] {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.rate-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.exchange-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.rate-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.currency-pair {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 48px;
  font-weight: bold;
  color: var(--text-primary);
}

.base-currency {
  color: var(--error-color);
}

.divider {
  color: var(--text-secondary);
  font-size: 36px;
}

.quote-currency {
  color: var(--text-secondary);
}

.rate-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.rate-number {
  font-size: 32px;
  font-weight: bold;
  color: var(--error-color);
  font-family: 'Monaco', 'Menlo', monospace;
}

.rate-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(208, 48, 80, 0.1);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: var(--error-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.error-message {
  margin-bottom: 40px;
}

.error-title {
  font-size: 36px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.2;
}

.error-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 40px;
}

.primary-action {
  order: 1;
}

.secondary-action {
  order: 2;
}

.help-section {
  margin-top: 40px;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: left;
}

.help-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.help-item strong {
  color: var(--primary-color);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .not-found-container {
    padding: 16px;
  }
  
  .exchange-rate-board {
    padding: 24px 16px;
  }
  
  .currency-pair {
    font-size: 36px;
  }
  
  .divider {
    font-size: 28px;
  }
  
  .rate-number {
    font-size: 24px;
  }
  
  .error-title {
    font-size: 28px;
  }
  
  .error-subtitle {
    font-size: 16px;
  }
  
  .action-buttons {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .currency-pair {
    font-size: 28px;
    flex-direction: column;
    gap: 4px;
  }
  
  .divider {
    font-size: 20px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .help-content {
    gap: 12px;
  }
}

/* Animation for data points */
.data-point {
  filter: drop-shadow(0 0 6px var(--error-color));
}
</style>
