import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { ClientReportPayloadSchema, type ClientReportResponse } from '../types/schemas/debugSchemas';
import { GetReportsQuerySchema, type GetAllReportsResponse, type GetReportByIdResponse } from '../types/schemas/adminDebugSchemas';
import { ClientLogService } from '../services/clientLogService';
import { DebugReportService } from '../services/debugReportService';
import { authMiddleware } from '../middleware/auth';
import { PrismaClient } from '@prisma/client';

// Validation schemas for new endpoints
const UpdateStatusSchema = z.object({
  status: z.enum(['not_reviewed', 'in_progress', 'completed', 'archived', 'duplicate', 'wont_fix']),
  comment: z.string().optional()
});

const AssignReportSchema = z.object({
  assignedTo: z.string().min(1),
  comment: z.string().optional()
});

const AddCommentSchema = z.object({
  comment: z.string().min(1),
  isInternal: z.boolean().optional().default(true)
});

/**
 * Debug routes for client-side logging and reporting with dual-write capability
 */
export default function createDebugRoutes(clientLogService: ClientLogService, prisma?: PrismaClient) {
  const router = new Hono();

  // Initialize database service if Prisma client is provided
  const debugReportService = prisma ? new DebugReportService(prisma) : null;
  const isDualWriteEnabled = !!debugReportService;

  /**
   * POST /report-issue
   * Receive and store client-side debug reports (with dual-write capability)
   */
  router.post(
    '/report-issue',
    zValidator('json', ClientReportPayloadSchema),
    async (c) => {
      try {
        const reportData = c.req.valid('json');

        // Extract user ID from auth context if available
        const userId = c.get('user')?.id;

        let reportId: string;
        let dbSuccess = false;
        let fileSuccess = false;

        // Dual-write: Try database first, then file system
        if (isDualWriteEnabled && debugReportService) {
          try {
            reportId = await debugReportService.createReport(reportData, userId);
            dbSuccess = true;
            console.log(`✅ [DebugRoutes] Saved report ${reportId} to database`);
          } catch (dbError) {
            console.error('❌ [DebugRoutes] Database write failed:', dbError);
            // Continue to file system as fallback
          }
        }

        // File system write (always as backup during migration)
        try {
          const fileReportId = await clientLogService.saveReport(reportData);
          fileSuccess = true;

          // Use database report ID if available, otherwise use file report ID
          if (!reportId) {
            reportId = fileReportId;
          }

          console.log(`✅ [DebugRoutes] Saved report ${fileReportId} to file system`);
        } catch (fileError) {
          console.error('❌ [DebugRoutes] File system write failed:', fileError);

          // If database also failed, this is a complete failure
          if (!dbSuccess) {
            throw fileError;
          }
        }

        // Log dual-write status
        if (isDualWriteEnabled) {
          console.log(`📊 [DebugRoutes] Dual-write status - DB: ${dbSuccess ? '✅' : '❌'}, File: ${fileSuccess ? '✅' : '❌'}`);
        }

        const response: ClientReportResponse = {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId: reportId!,
        };

        return c.json(response, 201);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to process debug report:', error);

        const response: ClientReportResponse = {
          success: false,
          message: 'Failed to process debug report. Please try again or contact support.',
        };

        return c.json(response, 500);
      }
    }
  );
  /**
   * GET /stats (for future admin use)
   * Get comprehensive statistics about debug reports and log storage
   */
  router.get('/stats', async (c) => {
    try {
      const [reportStats, logDirectoryStats] = await Promise.all([
        clientLogService.getReportStats(),
        clientLogService.getLogDirectoryStats()
      ]);

      return c.json({
        success: true,
        data: {
          reports: reportStats,
          storage: {
            ...logDirectoryStats,
            totalSizeMB: Math.round(logDirectoryStats.totalSize / 1024 / 1024 * 100) / 100,
          },
          logDirectory: clientLogService.getLogDirectory(),
        },
      });
    } catch (error: any) {
      console.error('❌ [DebugRoutes] Failed to get report stats:', error);
      return c.json({
        success: false,
        message: 'Failed to retrieve report statistics',
      }, 500);
    }
  });

  /**
   * POST /cleanup (for admin use)
   * Manually trigger log cleanup and rotation
   */
  router.post('/cleanup', async (c) => {
    try {
      await clientLogService.performMaintenance();
      
      return c.json({
        success: true,
        message: 'Log maintenance completed successfully',
      });
    } catch (error: any) {
      console.error('❌ [DebugRoutes] Failed to perform log cleanup:', error);
      return c.json({
        success: false,
        message: 'Failed to perform log maintenance',
      }, 500);
    }
  });

  // ========================================
  // ADMIN DEBUG DASHBOARD ROUTES
  // ========================================

  /**
   * GET /admin/reports
   * Get paginated list of debug reports with filtering and sorting
   */
  router.get(
    '/admin/reports',
    authMiddleware, // Protect with authentication
    zValidator('query', GetReportsQuerySchema),
    async (c) => {
      try {
        const queryParams = c.req.valid('query');
        
        const result = await clientLogService.getAllReports(queryParams);

        const response: GetAllReportsResponse = {
          success: true,
          data: result,
        };

        return c.json(response);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to get reports:', error);

        const response: GetAllReportsResponse = {
          success: false,
          message: 'Failed to retrieve debug reports',
        };

        return c.json(response, 500);
      }
    }
  );

  /**
   * GET /admin/reports/:reportId
   * Get detailed information for a specific report
   */
  router.get(
    '/admin/reports/:reportId',
    authMiddleware, // Protect with authentication
    async (c) => {
      try {
        const reportId = c.req.param('reportId');
        
        if (!reportId) {
          const response: GetReportByIdResponse = {
            success: false,
            data: null,
            message: 'Report ID is required',
          };
          return c.json(response, 400);
        }

        const report = await clientLogService.getReportById(reportId);

        const response: GetReportByIdResponse = {
          success: true,
          data: report,
          message: report ? undefined : 'Report not found',
        };

        return c.json(response, report ? 200 : 404);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to get report by ID:', error);

        const response: GetReportByIdResponse = {
          success: false,
          data: null,
          message: 'Failed to retrieve debug report',
        };

        return c.json(response, 500);
      }
    }
  );

  /**
   * GET /admin/reports/:reportId/export
   * Export a specific report as JSON file download
   */
  router.get(
    '/admin/reports/:reportId/export',
    authMiddleware, // Protect with authentication
    async (c) => {
      try {
        const reportId = c.req.param('reportId');
        
        if (!reportId) {
          return c.json({
            success: false,
            message: 'Report ID is required',
          }, 400);
        }

        const report = await clientLogService.getReportById(reportId);

        if (!report) {
          return c.json({
            success: false,
            message: 'Report not found',
          }, 404);
        }

        // Set headers for file download
        c.header('Content-Type', 'application/json');
        c.header('Content-Disposition', `attachment; filename="debug_report_${reportId}.json"`);

        return c.json(report);

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to export report:', error);

        return c.json({
          success: false,
          message: 'Failed to export debug report',
        }, 500);
      }
    }
  );

  // ========================================
  // NEW STATUS MANAGEMENT ENDPOINTS
  // ========================================

  /**
   * PUT /admin/reports/:reportId/status
   * Update report status with audit trail
   */
  router.put(
    '/admin/reports/:reportId/status',
    authMiddleware,
    zValidator('json', UpdateStatusSchema),
    async (c) => {
      if (!debugReportService) {
        return c.json({
          success: false,
          message: 'Database service not available. Status management requires database migration.'
        }, 503);
      }

      try {
        const reportId = c.req.param('reportId');
        const { status, comment } = c.req.valid('json');
        const userId = c.get('user')?.id;

        if (!reportId) {
          return c.json({
            success: false,
            message: 'Report ID is required'
          }, 400);
        }

        if (!userId) {
          return c.json({
            success: false,
            message: 'User authentication required'
          }, 401);
        }

        await debugReportService.updateReportStatus(reportId, status, userId, comment);

        return c.json({
          success: true,
          message: `Report status updated to ${status}`
        });

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to update report status:', error);
        return c.json({
          success: false,
          message: error.message || 'Failed to update report status'
        }, 500);
      }
    }
  );

  /**
   * PUT /admin/reports/:reportId/assign
   * Assign report to a user
   */
  router.put(
    '/admin/reports/:reportId/assign',
    authMiddleware,
    zValidator('json', AssignReportSchema),
    async (c) => {
      if (!debugReportService) {
        return c.json({
          success: false,
          message: 'Database service not available. Assignment requires database migration.'
        }, 503);
      }

      try {
        const reportId = c.req.param('reportId');
        const { assignedTo, comment } = c.req.valid('json');
        const userId = c.get('user')?.id;

        if (!reportId) {
          return c.json({
            success: false,
            message: 'Report ID is required'
          }, 400);
        }

        if (!userId) {
          return c.json({
            success: false,
            message: 'User authentication required'
          }, 401);
        }

        await debugReportService.assignReport(reportId, assignedTo, userId);

        return c.json({
          success: true,
          message: `Report assigned to user ${assignedTo}`
        });

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to assign report:', error);
        return c.json({
          success: false,
          message: error.message || 'Failed to assign report'
        }, 500);
      }
    }
  );

  /**
   * POST /admin/reports/:reportId/comments
   * Add a comment to a report
   */
  router.post(
    '/admin/reports/:reportId/comments',
    authMiddleware,
    zValidator('json', AddCommentSchema),
    async (c) => {
      if (!debugReportService) {
        return c.json({
          success: false,
          message: 'Database service not available. Comments require database migration.'
        }, 503);
      }

      try {
        const reportId = c.req.param('reportId');
        const { comment, isInternal } = c.req.valid('json');
        const userId = c.get('user')?.id;

        if (!reportId) {
          return c.json({
            success: false,
            message: 'Report ID is required'
          }, 400);
        }

        if (!userId) {
          return c.json({
            success: false,
            message: 'User authentication required'
          }, 401);
        }

        await debugReportService.addComment(reportId, userId, comment, isInternal);

        return c.json({
          success: true,
          message: 'Comment added successfully'
        });

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to add comment:', error);
        return c.json({
          success: false,
          message: error.message || 'Failed to add comment'
        }, 500);
      }
    }
  );

  /**
   * GET /admin/stats
   * Get enhanced dashboard statistics (database-powered)
   */
  router.get(
    '/admin/stats',
    authMiddleware,
    async (c) => {
      try {
        let stats: any = {};

        // Get database stats if available
        if (debugReportService) {
          try {
            stats.database = await debugReportService.getStats();
          } catch (error) {
            console.error('❌ [DebugRoutes] Failed to get database stats:', error);
            stats.database = { error: 'Database stats unavailable' };
          }
        }

        // Get file system stats as backup/comparison
        try {
          const [reportStats, logDirectoryStats] = await Promise.all([
            clientLogService.getReportStats(),
            clientLogService.getLogDirectoryStats()
          ]);

          stats.fileSystem = {
            reports: reportStats,
            storage: {
              ...logDirectoryStats,
              totalSizeMB: Math.round(logDirectoryStats.totalSize / 1024 / 1024 * 100) / 100,
            },
            logDirectory: clientLogService.getLogDirectory(),
          };
        } catch (error) {
          console.error('❌ [DebugRoutes] Failed to get file system stats:', error);
          stats.fileSystem = { error: 'File system stats unavailable' };
        }

        return c.json({
          success: true,
          data: {
            ...stats,
            dualWriteEnabled: isDualWriteEnabled,
            timestamp: new Date().toISOString()
          }
        });

      } catch (error: any) {
        console.error('❌ [DebugRoutes] Failed to get admin stats:', error);
        return c.json({
          success: false,
          message: 'Failed to retrieve admin statistics'
        }, 500);
      }
    }
  );

  return router;
}
