import type { TransactionStatusEnum } from './transaction'; // Or your actual path to TransactionStatusEnum
import type { FrontendNotificationType } from '@/stores/notificationStore';

export const INTEREST_REQUEST_DECLINED = 'INTEREST_REQUEST_DECLINED' as const;
export const INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY = 'INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY' as const;
// Shared Socket.IO event names for frontend

export const OFFER_CREATED = 'OFFER_CREATED' as const;
export const OFFER_UPDATED = 'OFFER_UPDATED' as const;
export const OFFER_STATUS_CHANGED = 'OFFER_STATUS_CHANGED' as const;
export const OFFER_DELETED = 'OFFER_DELETED' as const;
export const INTEREST_RECEIVED = 'INTEREST_RECEIVED' as const;
export const INTEREST_PROCESSED = 'INTEREST_PROCESSED' as const; // For offer creator after they action it (accept/decline)

// Events specifically for the user who showed interest
export const YOUR_INTEREST_ACCEPTED_EVENT = 'YOUR_INTEREST_ACCEPTED' as const; // When their interest is accepted
export const YOUR_INTEREST_DECLINED = 'YOUR_INTEREST_DECLINED' as const; // Added

// Chat specific events
export const CHAT_MESSAGE_SEND = 'CHAT_MESSAGE_SEND' as const;
export const CHAT_MESSAGE_RECEIVE = 'CHAT_MESSAGE_RECEIVE' as const;
export const CHAT_HISTORY_REQUEST = 'CHAT_HISTORY_REQUEST' as const;
export const CHAT_HISTORY_RESPONSE = 'CHAT_HISTORY_RESPONSE' as const;
export const CHAT_USER_JOINED_SESSION = 'CHAT_USER_JOINED_SESSION' as const;
export const CHAT_USER_LEFT_SESSION = 'CHAT_USER_LEFT_SESSION' as const;
export const CHAT_SESSION_INFO = 'CHAT_SESSION_INFO' as const; // For initial session details

// Transaction specific events
export const TRANSACTION_STATUS_UPDATED = 'TRANSACTION_STATUS_UPDATED' as const;

export const SYSTEM_MESSAGE_RECEIVE = 'SYSTEM_MESSAGE_RECEIVE' as const;

// Payer Negotiation events - NEW
export const NEGOTIATION_STATE_UPDATED = 'NEGOTIATION_STATE_UPDATED' as const;
export const PAYMENT_INFO_SUBMITTED = 'PAYMENT_INFO_SUBMITTED' as const;
export const PROPOSAL_MADE = 'PROPOSAL_MADE' as const;
export const NEGOTIATION_FINALIZED = 'NEGOTIATION_FINALIZED' as const;

// Notification events
export const NEW_NOTIFICATION = 'NEW_NOTIFICATION' as const;

// This payload should mirror the Transaction type from @/types/transaction.ts
// as the backend's transactionService.emitTransactionStatusUpdate sends the full transaction object.

// Placeholder Payloads - These should be properly defined based on backend emissions
export interface OfferCreatedPayload { 
  offerId: string; 
  userId?: string;
  fullOfferData?: any; // Can include the full offer data object
  [key: string]: any; 
}
export interface OfferUpdatedPayload { offerId: string; [key: string]: any; }
export interface OfferStatusChangedPayload { offerId: string; status: string; [key: string]: any; }
export interface OfferDeletedPayload { offerId: string; [key: string]: any; }

// Notification payload for NEW_NOTIFICATION events
export interface NewNotificationPayload {
  id: string; // Notification ID
  userId: string; // Recipient
  type: FrontendNotificationType; // NotificationType enum value
  message: string;
  isRead: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  relatedEntityType?: string;
  relatedEntityId?: string;
  actorId?: string;
  actorUsername?: string;
  data?: Record<string, any>; // For additional context
}

export interface SystemMessagePayload {
  messageId: string;
  chatSessionId: string;
  content: string;
  createdAt: string; // ISO string
  isSystemMessage: true;
  // Potentially other fields to give context to the system message
}

export interface InterestReceivedPayload {
  interestId: string;
  interestedUserId: string;
  offerId: string; // ID of the offer the interest is on
  offer: { // Details of the offer
    id: string; // Ensure this is the actual offer ID
    userId: string; // Offer creator's ID
    type: 'BUY' | 'SELL';
    amount: number;
    currencyPair: string;
  };
  interestedUser: { // Details of the user who showed interest
    id: string;
    username: string;
    reputationLevel: number;
  };
  status: 'PENDING'; // Status of the interest
  createdAt: string; // ISO string for when the interest was created
}

export interface InterestProcessedPayload {
  interestId: string;
  offerId: string;
  newStatus: 'ACCEPTED' | 'DECLINED'; // The new status of the interest
  reasonCode?: string; // Optional: reason for decline
  chatSessionId?: string; // Optional: if accepted, the ID of the new chat session
}

// Shared interfaces for socket payloads
export interface OfferDetailsForSocket {
  id: string; // <<< REVERTED to id from offerId
  type: string;
  amount: number;
  currencyPair?: string;
}

export interface UserActorDetailsForSocket {
  userId: string;
  username: string;
  reputationLevel?: number;
}

// For the user whose interest was accepted/declined by offer owner
export interface YourInterestAcceptedPayload {
  interestId: string;
  offerId: string;
  chatSessionId: string;
  offerDetails: OfferDetailsForSocket;
  actorDetails: UserActorDetailsForSocket; // Person who accepted (offer owner)
}

export interface YourInterestDeclinedPayload {
  interestId: string;
  offerId: string;
  offer: OfferDetailsForSocket; // The offer related to the declined interest
  offerCreator: UserActorDetailsForSocket; // The user who declined the interest
  reasonCode?: string;
}

// For the offer owner when they accept/decline an interest
// This is the one that myOffersStore listens to
export interface InterestRequestAcceptedAndChatReadyPayload {
  interestId: string;
  offerId: string; // Top-level
  chatSessionId: string;
  offer: { // Details of the offer
    type: string;
    amount: number;
    currencyPair: string;
  };
  offerCreator: {
    userId: string; // Ensure backend sends userId (or maps 'id' to 'userId')
    username: string;
    reputationLevel?: number; // Optional, if backend might not always send it
  };
  interestedUser: {
    userId: string;
    username: string;
    reputationLevel?: number; // Optional
  };
  message?: string;
}

// Chat event payloads
export interface ChatMessageSendPayload {
  chatSessionId: string;
  messageText: string; // Changed from messageText
}

export interface ChatMessageReceivePayload {
  messageId: string;
  chatSessionId: string;
  sender: { // Changed from senderId, senderUsername
    id: string;
    username: string;
    reputationLevel?: number; // Added to match backend sender structure
  };
  content: string; // Changed back to match backend
  createdAt: string; // Changed from timestamp, to match backend and frontend ChatMessage
  isSystemMessage?: boolean; // Added to match backend
}

export interface ChatHistoryRequestPayload {
  chatSessionId: string;
  limit?: number;
  offset?: number;
}

export interface ChatMessageInHistory {
  messageId: string;
  sender: { // Changed from senderId, senderUsername
    id: string;
    username: string;
    reputationLevel?: number; // Added to match backend sender structure
  };
  content: string; // Changed from messageText
  createdAt: string; // Changed from timestamp, to match backend and frontend ChatMessage
  isSystemMessage?: boolean; // Added to match backend
}
export interface ChatHistoryResponsePayload {
  chatSessionId: string;
  messages: ChatMessageInHistory[];
  hasMore?: boolean;
}

export interface ChatSessionInfoPayload {
  chatSessionId: string;
  otherUserId: string;
  otherUserUsername: string;
  offerId: string;
  // any other relevant info for the chat session
}

export interface ChatUserJoinedSessionPayload {
  chatSessionId: string;
  userId: string;
  username: string;
}

export interface ChatUserLeftSessionPayload {
  chatSessionId: string;
  userId: string;
  username: string;
}

// Transaction specific payloads - REMOVED the old interface, replaced by the type alias above.

export interface TransactionStatusUpdatePayload {
  transactionId: string;
  chatSessionId: string;
  offerId: string | null;
  status: TransactionStatusEnum; // Use your frontend enum
  currencyA: string;
  amountA: number;
  currencyAProviderId: string;
  currencyAProviderUsername: string | null; // ADDED
  currencyB: string;
  amountB: number;
  currencyBProviderId: string;
  currencyBProviderUsername: string | null; // ADDED
  termsAgreementTimestampPayer1: string | null;
  termsAgreementTimestampPayer2: string | null;
  agreedFirstPayerId: string | null;
  firstPayerDesignationTimestamp: string | null;
  paymentExpectedByPayer1: string | null;
  paymentDeclaredAtPayer1: string | null;
  paymentTrackingNumberPayer1: string | null;
  firstPaymentConfirmedByPayer2At: string | null;
  paymentExpectedByPayer2: string | null;
  paymentDeclaredAtPayer2: string | null;
  paymentTrackingNumberPayer2: string | null;
  secondPaymentConfirmedByPayer1At: string | null;
  cancellationReason: string | null;
  cancelledByUserId: string | null;
  disputeReason: string | null;
  disputedByUserId: string | null;
  disputeResolvedAt: string | null;
  disputeResolutionNotes: string | null;
  createdAt: string;
  updatedAt: string;
  // Ensure all fields match the backend payload
}

export interface SystemMessagePayload {
  messageId: string;
  chatSessionId: string;
  content: string;
  createdAt: string; // ISO string
  isSystemMessage: true; // Explicitly true for this payload type
  sender: {
    id: 'SYSTEM';
    username: 'System';
  };
  transactionId?: string; // Optional: context for the system message, e.g., the transaction it relates to
}

// Payer Negotiation event payloads - NEW
export interface PayerNegotiationStatePayload {
  negotiationId: string;
  transactionId: string;
  partyA_Id: string;
  partyB_Id: string;
  partyA_receivingInfoStatus: import('@/types/payerNegotiation').ReceivingInfoStatus;
  partyB_receivingInfoStatus: import('@/types/payerNegotiation').ReceivingInfoStatus;
  partyA_PaymentReceivingInfo: import('@/types/payerNegotiation').PaymentReceivingInfo | null;
  partyB_PaymentReceivingInfo: import('@/types/payerNegotiation').PaymentReceivingInfo | null;
  systemRecommendedPayerId: string | null;
  systemRecommendationReason: string | null;
  systemRecommendationRule: string | null;
  systemRecommendationDetails: any;
  currentProposal_PayerId: string | null;
  currentProposal_ById: string | null;
  currentProposal_Message: string | null;
  partyA_agreedToCurrentProposal: boolean;
  partyB_agreedToCurrentProposal: boolean;
  negotiationStatus: import('@/types/payerNegotiation').NegotiationStatus;
  finalizedPayerId: string | null;
  paymentTimerDueDate: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentInfoSubmittedPayload {
  negotiationId: string;
  userId: string;
  receivingInfoStatus: string;
  negotiationState: PayerNegotiationStatePayload;
}

export interface ProposalMadePayload {
  negotiationId: string;
  proposerId: string;
  proposedPayerId: string;
  message?: string;
}

export interface NegotiationFinalizedPayload {
  negotiationId: string;
  transactionId: string;
  partyA_Id: string;
  partyB_Id: string;
  partyA_receivingInfoStatus: import('@/types/payerNegotiation').ReceivingInfoStatus;
  partyB_receivingInfoStatus: import('@/types/payerNegotiation').ReceivingInfoStatus;
  partyA_PaymentReceivingInfo: import('@/types/payerNegotiation').PaymentReceivingInfo | null;
  partyB_PaymentReceivingInfo: import('@/types/payerNegotiation').PaymentReceivingInfo | null;
  systemRecommendedPayerId: string | null;
  systemRecommendationReason: string | null;
  systemRecommendationRule: string | null;
  systemRecommendationDetails: any;
  currentProposal_PayerId: string | null;
  currentProposal_ById: string | null;
  currentProposal_Message: string | null;
  partyA_agreedToCurrentProposal: boolean;
  partyB_agreedToCurrentProposal: boolean;
  negotiationStatus: import('@/types/payerNegotiation').NegotiationStatus;
  finalizedPayerId: string | null;
  paymentTimerDueDate: string | null;
  createdAt: string;
  updatedAt: string;
}

// A type to map event names to their payload types for type-safe emitting/listening
export interface SocketEventPayloadMap {
  [OFFER_CREATED]: OfferCreatedPayload;
  [OFFER_UPDATED]: OfferUpdatedPayload;
  [OFFER_STATUS_CHANGED]: OfferStatusChangedPayload;
  [OFFER_DELETED]: OfferDeletedPayload;
  [INTEREST_RECEIVED]: InterestReceivedPayload;
  [INTEREST_PROCESSED]: InterestProcessedPayload;
  [YOUR_INTEREST_ACCEPTED_EVENT]: YourInterestAcceptedPayload;
  [YOUR_INTEREST_DECLINED]: YourInterestDeclinedPayload;
  [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: InterestRequestAcceptedAndChatReadyPayload;

  // Chat event payloads
  [CHAT_MESSAGE_SEND]: ChatMessageSendPayload;
  [CHAT_MESSAGE_RECEIVE]: ChatMessageReceivePayload;
  [CHAT_HISTORY_REQUEST]: ChatHistoryRequestPayload;
  [CHAT_HISTORY_RESPONSE]: ChatHistoryResponsePayload;
  [CHAT_SESSION_INFO]: ChatSessionInfoPayload;
  [CHAT_USER_JOINED_SESSION]: ChatUserJoinedSessionPayload;
  [CHAT_USER_LEFT_SESSION]: ChatUserLeftSessionPayload;

  // Transaction events
  [TRANSACTION_STATUS_UPDATED]: TransactionStatusUpdatePayload;
  [SYSTEM_MESSAGE_RECEIVE]: SystemMessagePayload;
  // Payer Negotiation events - NEW
  [NEGOTIATION_STATE_UPDATED]: PayerNegotiationStatePayload;
  [PAYMENT_INFO_SUBMITTED]: PaymentInfoSubmittedPayload;
  [PROPOSAL_MADE]: ProposalMadePayload;
  [NEGOTIATION_FINALIZED]: NegotiationFinalizedPayload;
}
