<template>
  <n-tooltip trigger="hover" placement="bottom-end">
    <template #trigger>
      <div class="connection-icon" :class="connectionClass">
        <n-icon :component="connectionIcon" :class="iconClass" size="20" />
      </div>
    </template>
    <div class="connection-tooltip">
      <div class="tooltip-status">{{ connectionTooltip }}</div>
      <div class="tooltip-transport">Transport: {{ connectionStore.transportType }}</div>
      <n-button
        v-if="connectionStore.connectionQuality === 'disconnected'"
        @click="attemptReconnect"
        text
        type="primary"
        size="tiny"
        class="reconnect-button"
        style="margin-top: 8px;"
      >
        Retry Connection
      </n-button>
    </div>
  </n-tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NIcon, NButton, NTooltip } from 'naive-ui';
import { 
  WifiOutline as WifiIcon,
  CloudOfflineOutline as OfflineIcon,
  SyncOutline as ReconnectingIcon 
} from '@vicons/ionicons5';
import { useConnectionStore } from '@/stores/connection';
import centralizedSocketManager from '@/services/centralizedSocketManager';

const connectionStore = useConnectionStore();

const connectionClass = computed(() => {
  switch (connectionStore.connectionQuality) {
    case 'excellent':
      return 'connection-excellent';
    case 'good':
      return 'connection-warning';
    case 'poor':
      return 'connection-reconnecting';
    case 'disconnected':
      return 'connection-error';
    default:
      return '';
  }
});

const connectionTooltip = computed(() => {
  const status = connectionStore.connectionStatus;
  const lastReason = connectionStore.lastDisconnectReason;

  if (connectionStore.connectionQuality === 'disconnected' && lastReason) {
    if (lastReason === 'offline') {
      return 'Device is offline - Check your internet connection';
    } else if (lastReason.toLowerCase().includes('auth') || lastReason.toLowerCase().includes('token')) {
      return 'Authentication issue - You may need to log in again';
    } else {
      return `${status} - ${lastReason}`;
    }
  }

  return status;
});

const connectionIcon = computed(() => {
  switch (connectionStore.connectionQuality) {
    case 'good':
      return WifiIcon;
    case 'poor':
      return ReconnectingIcon;
    case 'disconnected':
      return OfflineIcon;
    default:
      return WifiIcon;
  }
});

const iconClass = computed(() => {
  return connectionStore.connectionQuality === 'poor' ? 'spinning' : '';
});

async function attemptReconnect() {
  try {
    console.log('[ConnectionStatus] Manual reconnect attempt...');
    await centralizedSocketManager.forceReconnect();
    console.log('[ConnectionStatus] Reconnection successful');
  } catch (error) {
    console.error('[ConnectionStatus] Failed to reconnect:', error);
  }
}
</script>

<style scoped>
.connection-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px; /* Space from notification bell */
}

.connection-icon:hover {
  transform: scale(1.1);
}

.connection-excellent {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.connection-excellent:hover {
  background: rgba(40, 167, 69, 0.2);
}

.connection-warning {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.connection-warning:hover {
  background: rgba(255, 193, 7, 0.2);
}

.connection-reconnecting {
  color: #6c757d;
  background: rgba(108, 117, 125, 0.1);
}

.connection-reconnecting:hover {
  background: rgba(108, 117, 125, 0.2);
}

.connection-error {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.connection-error:hover {
  background: rgba(220, 53, 69, 0.2);
}

.connection-tooltip {
  padding: 8px 12px;
  font-size: 13px;
  line-height: 1.4;
}

.tooltip-status {
  font-weight: 600;
  margin-bottom: 4px;
}

.tooltip-transport {
  color: #666;
  font-size: 12px;
}

.reconnect-button {
  color: inherit !important;
  text-decoration: underline;
}

.spinning {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
