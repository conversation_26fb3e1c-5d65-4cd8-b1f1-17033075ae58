import { Hono } from 'hono';
import { PrismaClient, OfferStatus, InterestStatus as PrismaInterestStatus, OfferType, Prisma, User as PrismaUser, Interest as PrismaInterest, Offer, NotificationType } from '@prisma/client';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware, JwtPayload } from '../middleware/auth';
import { io, getNotificationService } from '../index';
import {
  OFFER_CREATED,
  OFFER_UPDATED,
  OFFER_STATUS_CHANGED,
  INTEREST_RECEIVED
} from '../types/socketEvents';
import { interestService } from '../services/interestService';

const prisma = new PrismaClient();
const offerRouter = new Hono();

// --- Schemas ---
const interestIdParamSchema = z.object({
  interestId: z.string().min(1)
});

const declineInterestBodySchema = z.object({
  reasonCode: z.string().optional()
});

const createOfferSchema = z.object({
  type: z.enum([OfferType.BUY, OfferType.SELL]),
  amount: z.number().positive('Amount must be positive'),
  baseRate: z.number().positive('Base rate must be positive'),
  adjustmentForLowerRep: z.number(),
  adjustmentForHigherRep: z.number(),
});

const updateOfferStatusSchema = z.object({
  status: z.nativeEnum(OfferStatus),
});

// DELETE /api/offers/all-for-test - Delete all offers (for testing)
offerRouter.delete('/all-for-test', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  console.log(`[OfferRoutes] User ${jwtPayload.userId} attempting to delete all test data.`);

  try {
    await prisma.$transaction([
      // 1. Transactions are linked to ChatSessions. Must be deleted before ChatSession.
      prisma.transaction.deleteMany({}),
      // 2. ChatMessages are linked to ChatSessions (onDelete: Cascade from ChatSession should handle, but explicit for full wipe)
      prisma.chatMessage.deleteMany({}),
      // 3. Notifications are linked to Users (onDelete: Cascade). 
      //    They might also have relatedEntityId pointing to offers/interests, so delete early.
      prisma.notification.deleteMany({}),
      // 4. Interests are linked to Offers (onDelete: Cascade) and Users (onDelete: Cascade).
      //    ChatSessions also have an optional link to Interest (onDelete: SetNull).
      //    Deleting Interests before ChatSessions ensures this link is cleared.
      prisma.interest.deleteMany({}),
      // 5. ChatSessions are linked to Offers (onDelete: Cascade) and Users (onDelete: Cascade).
      //    Must be deleted before Offers if not relying solely on cascade.
      prisma.chatSession.deleteMany({}),
      // 6. Finally, delete all Offers.
      prisma.offer.deleteMany({})
    ]);

    console.log(`[OfferRoutes] User ${jwtPayload.userId} successfully deleted all offers and related test data.`);
    return c.json({ message: 'Successfully deleted all offers and related test data.' }, 200);

  } catch (error: any) {
    console.error(`[OfferRoutes API Error] Delete All Test Data by ${jwtPayload.userId}:`, error);
    let errorMessage = 'Failed to delete all test data.';
    let statusCode = 500;
    let prismaCode: string | undefined = undefined;

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      prismaCode = error.code;
      if (error.code === 'P2003') { // Foreign key constraint violation
        errorMessage = `Foreign key constraint violation. Model: ${error.meta?.modelName || 'Unknown'}, Constraint: ${error.meta?.constraint || 'Unknown'}`;
        statusCode = 409; // Conflict
      } else {
        errorMessage = `Prisma error (${error.code}): ${error.message}`;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }
    // Ensure statusCode is a valid Hono StatusCode literal
    const validStatusCode = statusCode as any; // Cast to any to bypass strict literal check if necessary, Hono handles it
    return c.json({ 
      message: errorMessage, 
      prismaCode: prismaCode,
      errorDetails: error.toString()
    }, validStatusCode);
  }
});

// --- Interest Endpoints ---
// Accept Interest
offerRouter.post('/interests/:interestId/accept', authMiddleware, zValidator('param', interestIdParamSchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const { interestId } = c.req.valid('param');
  console.log(`[OfferRoutes] User ${jwtPayload.userId} attempting to accept interest ${interestId}`);
  try {
    const result = await interestService.acceptInterest(interestId, jwtPayload.userId);
    console.log(`[OfferRoutes] Interest ${interestId} accepted successfully. Chat session ${result.chatSession.id} created.`);
    return c.json({ 
      message: 'Interest accepted and chat session created', 
      chatSessionId: result.chatSession.id,
      interestId: result.updatedInterest.id,
      newStatus: result.updatedInterest.status
    });
  } catch (err: any) {
    console.error(`[OfferRoutes API Error] Accept Interest for ${interestId} by ${jwtPayload.userId}:`, err);
    if (err.response && err.response.data) {
      console.error("[OfferRoutes API Error Detail]:", err.response.data);
    }
    if (err.message === 'Interest not found') return c.json({ message: 'Interest not found' }, 404);
    if (err.message === 'Unauthorized to accept this interest') return c.json({ message: 'Unauthorized to accept this interest' }, 403);
    if (err.message === 'Interest already accepted') return c.json({ message: 'Interest already accepted' }, 409);
    if (err.message === 'Invalid offer creator ID provided.') return c.json({ message: 'Invalid offer creator ID provided.' }, 400);
    if (err.message === 'Invalid interested user ID provided.') return c.json({ message: 'Invalid interested user ID provided.' }, 400);
    
    let detailedMessage = 'Internal server error while accepting interest.';
    if (err.message && typeof err.message === 'string') {
        detailedMessage = err.message; 
    }
    return c.json({ message: detailedMessage }, 500);
  }
});

// Decline Interest
offerRouter.post('/interests/:interestId/decline', authMiddleware, zValidator('param', interestIdParamSchema), zValidator('json', declineInterestBodySchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const { interestId } = c.req.valid('param');
  const { reasonCode } = c.req.valid('json');
  try {
    const declinedInterestResult = await interestService.declineInterest(interestId, jwtPayload.userId, reasonCode);

    // Emit INTEREST_PROCESSED to the current user (offer creator) to update their UI
    // The declinedInterestResult contains all necessary information.
    io.to(jwtPayload.userId).emit('INTEREST_PROCESSED', {
      interestId: declinedInterestResult.id, // Corrected: use id from the service result (Interest object)
      offerId: declinedInterestResult.offerId,
      newStatus: 'DECLINED', // Ensure this is the string literal
      reasonCode: declinedInterestResult.declineReasonCode,
    });

    return c.json({
      message: 'Interest declined',
      interestId: declinedInterestResult.id, // Corrected: use id from the service result
      reasonCode: declinedInterestResult.declineReasonCode
    });
  } catch (err: any) {
    if (err.message === 'Interest not found') return c.json({ message: 'Interest not found' }, 404);
    if (err.message === 'Interest is not pending') return c.json({ message: 'Interest is not pending' }, 409);
    if (err.message === 'Forbidden') return c.json({ message: 'Forbidden' }, 403);
    console.error('[API Error] Decline Interest:', err);
    return c.json({ message: 'Internal server error' }, 500);
  }
});

// --- Offer Endpoints ---
// GET /api/offers/browse - Browse active offers from other users
offerRouter.get('/browse', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const viewingUser = await prisma.user.findUnique({ where: { id: jwtPayload.userId } });
  if (!viewingUser) return c.json({ message: 'User not found' }, 404);
  const viewingUserLevel = viewingUser.reputationLevel;  const offers = await prisma.offer.findMany({
    where: { status: 'ACTIVE', userId: { not: jwtPayload.userId } },
    include: {
      user: { select: { username: true, reputationLevel: true } }, // Offer creator
      interests: { // Interests by the viewing user
        where: { interestedUserId: jwtPayload.userId },
        select: {
          id: true,
          status: true,
          chatSession: { // Include the related ChatSession
            select: {
              id: true, // And select its id
              transaction: { // Include transaction info
                select: {
                  id: true,
                  status: true,
                  payerNegotiation: {
                    select: {
                      negotiationStatus: true
                    }
                  }
                }
              }
            }
          }
        },
        take: 1
      },
      // Include ALL chat sessions for this offer to check for any transactions
      chatSessions: {
        select: {
          id: true,
          transaction: {
            select: {
              id: true,
              status: true,
              payerNegotiation: {
                select: {
                  negotiationStatus: true
                }
              }
            }
          }
        }
      }
    },
    orderBy: { createdAt: 'desc' },
  });

  return c.json(offers.map((offer) => {
    const { baseRate, adjustmentForLowerRep, adjustmentForHigherRep, type } = offer;
    const offerCreatorLevel = offer.user!.reputationLevel ?? 3; // user is included
    let applicableRate = baseRate;

    if (viewingUserLevel !== offerCreatorLevel) {
      if (viewingUserLevel > offerCreatorLevel) {
        applicableRate *= (type === 'SELL' ? (1 - adjustmentForHigherRep / 100) : (1 + adjustmentForHigherRep / 100));
      } else { // viewingUserLevel < offerCreatorLevel
        applicableRate *= (type === 'SELL' ? (1 + adjustmentForLowerRep / 100) : (1 - adjustmentForLowerRep / 100));
      }
    }    const currentUserInterest = offer.interests && offer.interests.length > 0 ? offer.interests[0] : null;
    
    // Check for transaction status from current user's interest OR any chat session
    let transactionStatus = currentUserInterest?.chatSession?.transaction?.status || null;
    let negotiationStatus = currentUserInterest?.chatSession?.transaction?.payerNegotiation?.negotiationStatus || null;
    
    // If no transaction from current user's interest, check all chat sessions for any active transaction
    if (!transactionStatus && offer.chatSessions) {
      for (const chatSession of offer.chatSessions) {
        if (chatSession.transaction) {
          transactionStatus = chatSession.transaction.status;
          negotiationStatus = chatSession.transaction.payerNegotiation?.negotiationStatus || null;
          break; // Use the first transaction found
        }
      }
    }
    
    return {
      id: offer.id, type: offer.type, amount: offer.amount, baseRate: offer.baseRate,
      adjustmentForLowerRep: offer.adjustmentForLowerRep, adjustmentForHigherRep: offer.adjustmentForHigherRep,
      status: offer.status, createdAt: offer.createdAt.toISOString(), offerCreatorId: offer.userId,
      offerCreatorUsername: offer.user!.username ?? 'Unknown', // user is included
      offerCreatorReputationLevel: offer.user!.reputationLevel ?? null, // user is included
      calculatedApplicableRate: Math.round(applicableRate),
      currentUserHasShownInterest: !!currentUserInterest,
      currentUserInterestStatus: currentUserInterest ? currentUserInterest.status : null,      chatSessionId: currentUserInterest?.chatSession ? currentUserInterest.chatSession.id : null, // Access chatSession.id
      transactionStatus: transactionStatus,
      negotiationStatus: negotiationStatus,
      currencyPair: offer.currencyPair,
    };
  }));
});

// GET /api/offers/my - Get offers for the logged-in user
offerRouter.get('/my', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const includeInterestsQueryParam = c.req.query('includeInterests');
  const includeInterests = includeInterestsQueryParam === '1';

  type SelectedOfferUserFields = {
    username: string | null;
    reputationLevel: number | null;
  };

  type SelectedInterestedUserFields = {
    id: string;
    username: string | null;
    email: string;
    reputationLevel: number | null;
  };

  type MyInterestWithDetails = {
    id: string;
    interestedUserId: string;
    status: PrismaInterestStatus;
    declineReasonCode: string | null; // Corrected field name
    createdAt: Date;
    chatSession?: { 
      id: string;
      transaction?: {
        status: string;
        payerNegotiation?: {
          negotiationStatus: string;
        }
      }
    } | null; // chatSession relation itself, from which id can be accessed
    interestedUser?: SelectedInterestedUserFields | null;
  };
  
  type OfferForMyRoute = Omit<Offer, 'userId' | 'createdAt' | 'updatedAt'> & {
    userId: string;
    createdAt: Date; 
    updatedAt: Date; 
    user: SelectedOfferUserFields;
    interests?: MyInterestWithDetails[];
  };

  const offers = await prisma.offer.findMany({
    where: { userId: jwtPayload.userId },
    orderBy: { createdAt: Prisma.SortOrder.desc },
    include: {
      user: { select: { username: true, reputationLevel: true } },
      interests: includeInterests ?
        {
          select: { 
            id: true,
            interestedUserId: true,
            status: true,
            declineReasonCode: true, // Corrected: use declineReasonCode
            createdAt: true,            chatSession: { // Select the related chatSession and its id
              select: {
                id: true,
                transaction: {
                  select: {
                    status: true,
                    payerNegotiation: {
                      select: {
                        negotiationStatus: true
                      }
                    }
                  }
                }
              }
            },
            interestedUser: {
              select: { id: true, username: true, email: true, reputationLevel: true }
            }
          },
          orderBy: { createdAt: Prisma.SortOrder.desc },
        } :
        false
    },
  }) as OfferForMyRoute[];

  return c.json(offers.map(o => {
    const userDetails = o.user;

    const mappedInterests = o.interests?.map((i: MyInterestWithDetails) => {
      const interestedUserDetails = i.interestedUser;

      if (includeInterests && !interestedUserDetails) {
        console.warn(`[API Warning] My Offers: Interest ${i.id} for offer ${o.id} was expected to have interestedUser details but did not.`);
      }      return {
        id: i.id,
        interestedUserId: i.interestedUserId,
        status: i.status,
        chatSessionId: i.chatSession?.id || null, // Access id from chatSession object
        transactionStatus: i.chatSession?.transaction?.status || null, // Add transaction status
        negotiationStatus: i.chatSession?.transaction?.payerNegotiation?.negotiationStatus || null, // Add negotiation status
        reasonCode: i.declineReasonCode, // Corrected: use declineReasonCode from Prisma
        createdAt: i.createdAt.toISOString(),
        username: interestedUserDetails?.username || interestedUserDetails?.email?.split('@')[0] || 'Unknown User',
        reputationLevel: interestedUserDetails?.reputationLevel ?? null,
      };
    }) || [];

    return {
      id: o.id,
      type: o.type,
      currencyPair: o.currencyPair,
      amount: o.amount,
      baseRate: o.baseRate,
      adjustmentForLowerRep: o.adjustmentForLowerRep,
      adjustmentForHigherRep: o.adjustmentForHigherRep,
      status: o.status,
      userId: o.userId,
      createdAt: o.createdAt.toISOString(),
      updatedAt: o.updatedAt.toISOString(),
      user: userDetails ? {
        username: userDetails.username,
        reputationLevel: userDetails.reputationLevel
      } : null,
      interests: mappedInterests,
    };
  }));
});

// POST /api/offers - Create a new offer
offerRouter.post('/', authMiddleware, zValidator('json', createOfferSchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const dbUser = await prisma.user.findUnique({ where: { id: jwtPayload.userId } });
  if (!dbUser || !dbUser.phoneVerified) {
    return c.json({ message: 'Phone verification required to create offers.' }, 403);
  }
  const data = c.req.valid('json');
  const newOffer = await prisma.offer.create({
    data: {
      userId: jwtPayload.userId, type: data.type, currencyPair: 'CAD-IRR', // Default
      amount: data.amount, baseRate: data.baseRate,
      adjustmentForLowerRep: data.adjustmentForLowerRep,
      adjustmentForHigherRep: data.adjustmentForHigherRep,
      status: 'ACTIVE',
    },    include: { user: { select: { username: true, reputationLevel: true } } } // Include user for response
  });

  console.log(`[OFFER_CREATION] Offer created with ID: ${newOffer.id}, Status: ${newOffer.status}`);  if (newOffer.status === 'ACTIVE') {
    console.log(`[SOCKET_EMIT] Emitting OFFER_CREATED event for offer: ${newOffer.id}`);
    
    // Create a clean payload without circular references
    const offerPayload = {
      offerId: newOffer.id,
      userId: newOffer.userId,
      fullOfferData: {
        ...newOffer,
        user: newOffer.user ? {
          username: newOffer.user.username,
          reputationLevel: newOffer.user.reputationLevel
        } : undefined,
        interests: [] // New offer won't have interests yet
      }
    };
    
    io.emit(OFFER_CREATED, offerPayload);
    console.log(`[SOCKET_EMIT] OFFER_CREATED event emitted successfully for offer: ${newOffer.id}`);
  } else {
    console.log(`[SOCKET_EMIT] NOT emitting OFFER_CREATED - offer status is: ${newOffer.status}`);
  }
  const userDetails = newOffer.user!; // user is included
  return c.json({
    ...newOffer,
    createdAt: newOffer.createdAt.toISOString(),
    user: { username: userDetails.username, reputationLevel: userDetails.reputationLevel }
  }, 201);
});

// GET /api/offers/browse/:offerId - Get a single browsable offer by ID
offerRouter.get('/browse/:offerId', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const offerId = c.req.param('offerId');
  const viewingUser = await prisma.user.findUnique({ where: { id: jwtPayload.userId } });
  if (!viewingUser) return c.json({ message: 'Viewing user not found' }, 404);

  const offer = await prisma.offer.findUnique({
    where: { id: offerId },
    include: {
      user: { select: { username: true, reputationLevel: true } }, // Offer creator
      interests: { // Interest by the viewing user
        where: { interestedUserId: jwtPayload.userId },
        select: { status: true }, take: 1
      },
    },
  });

  if (!offer || !offer.user) return c.json({ message: 'Offer not found or creator details missing' }, 404);

  const { baseRate, adjustmentForLowerRep, adjustmentForHigherRep, type } = offer;
  const offerCreatorLevel = offer.user.reputationLevel ?? 3;
  let applicableRate = baseRate;
  const viewingUserLevel = viewingUser.reputationLevel;

  if (offer.userId !== jwtPayload.userId && viewingUserLevel !== offerCreatorLevel) { // No adjustment for own offer or same level
      if (viewingUserLevel > offerCreatorLevel) {
        applicableRate *= (type === 'SELL' ? (1 - adjustmentForHigherRep / 100) : (1 + adjustmentForHigherRep / 100));
      } else { // viewingUserLevel < offerCreatorLevel
        applicableRate *= (type === 'SELL' ? (1 + adjustmentForLowerRep / 100) : (1 - adjustmentForLowerRep / 100));
      }
  }
  const currentUserInterest = offer.interests && offer.interests.length > 0 ? offer.interests[0] : null;
  return c.json({
    id: offer.id, type: offer.type, amount: offer.amount, baseRate: offer.baseRate,
    adjustmentForLowerRep: offer.adjustmentForLowerRep, adjustmentForHigherRep: offer.adjustmentForHigherRep,
    status: offer.status, createdAt: offer.createdAt.toISOString(), offerCreatorId: offer.userId,
    offerCreatorUsername: offer.user.username ?? 'Unknown',
    offerCreatorReputationLevel: offer.user.reputationLevel ?? null,
    calculatedApplicableRate: Math.round(applicableRate),
    currentUserHasShownInterest: !!currentUserInterest,
    currentUserInterestStatus: currentUserInterest ? currentUserInterest.status : null,
  });
});

// GET /api/offers/:offerId - Get a single offer by ID (owner only)
offerRouter.get('/:offerId', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const offerId = c.req.param('offerId');  const offer = await prisma.offer.findUnique({
    where: { id: offerId },
    include: { 
      user: { select: { username: true, reputationLevel: true } },
      interests: {
        where: { interestedUserId: jwtPayload.userId },
        select: { id: true, status: true }
      }
    }
  });

  if (!offer || !offer.user) return c.json({ message: 'Offer not found or user details missing' }, 404);
  
  const userDetails = offer.user;
  const userInterest = offer.interests.length > 0 ? offer.interests[0] : null;
  
  return c.json({
    ...offer,
    createdAt: offer.createdAt.toISOString(),
    user: { username: userDetails.username, reputationLevel: userDetails.reputationLevel },
    userInterest: userInterest ? { id: userInterest.id, status: userInterest.status } : null,
    isOwner: offer.userId === jwtPayload.userId
  });
});

// PUT /api/offers/:offerId - Update an offer (owner only)
offerRouter.put('/:offerId', authMiddleware, zValidator('json', createOfferSchema.partial()), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const offerId = c.req.param('offerId');
  const foundOffer = await prisma.offer.findUnique({ where: { id: offerId } });

  if (!foundOffer) return c.json({ message: 'Offer not found' }, 404);
  if (foundOffer.userId !== jwtPayload.userId) return c.json({ message: 'Forbidden' }, 403);

  const data = c.req.valid('json');
  const updatedOffer = await prisma.offer.update({
    where: { id: offerId }, data,
    include: { user: { select: { username: true, reputationLevel: true } } }
  });

  io.emit(OFFER_UPDATED, { offerId: updatedOffer.id });
  const userDetails = updatedOffer.user!; // user is included
  return c.json({
    ...updatedOffer,
    createdAt: updatedOffer.createdAt.toISOString(),
    user: { username: userDetails.username, reputationLevel: userDetails.reputationLevel }
  });
});

// PATCH /api/offers/:offerId/status - Update offer status (owner only)
offerRouter.patch('/:offerId/status', authMiddleware, zValidator('json', updateOfferStatusSchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const offerId = c.req.param('offerId');
  const { status } = c.req.valid('json');

  const foundOffer = await prisma.offer.findUnique({ 
    where: { id: offerId },
    include: { user: { select: { username: true, reputationLevel: true } } } 
  });
  if (!foundOffer) return c.json({ message: 'Offer not found' }, 404);
  if (foundOffer.userId !== jwtPayload.userId) return c.json({ message: 'Forbidden' }, 403);

  if (['COMPLETED', 'CANCELLED'].includes(foundOffer.status) || foundOffer.status === status) {
    return c.json({ message: `Offer is already ${foundOffer.status} or no change requested.` }, foundOffer.status === status ? 200 : 400);
  }

  const updatedOffer = await prisma.offer.update({
    where: { id: offerId }, data: { status },
    include: { user: { select: { username: true, reputationLevel: true } } } 
  });

  io.emit(OFFER_STATUS_CHANGED, { offerId: updatedOffer.id, newStatus: updatedOffer.status });
  
  // const notificationService = getNotificationService();
  // if (notificationService && updatedOffer.user) { 
  //   try {
  //     await notificationService.createNotification({
  //       userId: updatedOffer.userId, 
  //       type: NotificationType.OFFER_STATUS_CHANGED,
  //       message: `The status of your offer for ${updatedOffer.currencyPair} was changed to ${updatedOffer.status}.`,
  //       relatedEntityType: 'OFFER',
  //       relatedEntityId: updatedOffer.id,
  //       actorId: updatedOffer.userId, 
  //       actorUsername: updatedOffer.user.username || 'N/A',
  //     });
  //   } catch (error) {
  //     console.error('[OfferRoutes] Failed to create notification for offer status change:', error);
  //   }
  // }

  const userDetails = updatedOffer.user!;
  return c.json({
    ...updatedOffer,
    createdAt: updatedOffer.createdAt.toISOString(),
    user: { username: userDetails.username, reputationLevel: userDetails.reputationLevel }
  });
});

// POST /api/offers/:offerId/interest - Show interest in an offer
offerRouter.post('/:offerId/interest', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const offerId = c.req.param('offerId');
  const interestedUser = await prisma.user.findUnique({ where: { id: jwtPayload.userId } });

  if (!interestedUser) return c.json({ message: 'User not found' }, 404);
  if (!interestedUser.phoneVerified) return c.json({ message: 'Phone verification required' }, 403);

  const offer = await prisma.offer.findUnique({
    where: { id: offerId },
    include: { user: { select: { id: true, username: true, email: true, reputationLevel: true } } } 
  });

  if (!offer || !offer.user) return c.json({ message: 'Offer not found or creator missing' }, 404);
  if (offer.status !== 'ACTIVE') return c.json({ message: 'Offer not active' }, 400);
  if (offer.userId === jwtPayload.userId) return c.json({ message: 'Cannot show interest in own offer' }, 400);

  const existingInterest = await prisma.interest.findFirst({
    where: { 
      offerId: offerId,
      interestedUserId: jwtPayload.userId 
    }
  });
  if (existingInterest) return c.json({ message: 'Already shown interest' }, 409);

  const interest = await prisma.interest.create({
    data: { 
      offerId,
      interestedUserId: jwtPayload.userId,
      status: PrismaInterestStatus.PENDING
    },
    include: {
      offer: { // Offer on which interest is shown
        include: {
          user: { // Creator of the offer
            select: { id: true, username: true, email: true }
          }
        }
      },
      interestedUser: { // User who showed interest
        select: { id: true, username: true, email: true, reputationLevel: true }
      }
    }
  });

  // Notify the offer creator about the new interest via Socket.IO and create a persistent notification
  if (offer.userId !== jwtPayload.userId && interest.offer && interest.offer.user && interest.interestedUser) {
    const interestedUserUsername = interest.interestedUser.username || interest.interestedUser.email.split('@')[0];
    const offerTitleString = `${interest.offer.type} ${interest.offer.amount} ${interest.offer.currencyPair || 'N/A'}`;
    
    // Emit socket event with enhanced logging
     console.log(`🔔🔔🔔 [BACKEND] Emitting INTEREST_RECEIVED to user ${offer.userId} for offer ${interest.offerId}`);
     console.log(`🔔 [BACKEND] INTEREST_RECEIVED payload:`, {
       interestId: interest.id,
       offerId: interest.offerId,
       offerUserId: offer.userId,
       interestedUserId: interest.interestedUser?.id
     });
     
     const interestReceivedPayload = {
       interestId: interest.id,
       offerId: interest.offerId,
       offer: {
         id: interest.offer.id,
         type: interest.offer.type,
         amount: interest.offer.amount,
         currencyPair: interest.offer.currencyPair,
        // Add other offer fields as needed
      },
      interestedUser: {
        userId: interest.interestedUser.id,
        username: interest.interestedUser.username || interest.interestedUser.email.split('@')[0],
        reputationLevel: interest.interestedUser.reputationLevel
      }
    };
     
     console.log('[SOCKET_EVENT]', {
       event: 'INTEREST_RECEIVED',
       action: 'emit',
       targetUserId: offer.userId,
       interestId: interest.id,
       offerId: interest.offerId,
       interestedUserId: interest.interestedUser?.id
     });
      
    io.to(offer.userId).emit(INTEREST_RECEIVED, interestReceivedPayload);
    
     // Create persistent notification
    const notificationService = getNotificationService();
    if (notificationService) {
      try {
        await notificationService.createNotification({
          userId: interest.offer.userId, // Notification for the offer creator
          type: NotificationType.NEW_INTEREST_ON_YOUR_OFFER,
          message: `User ${interestedUserUsername} showed interest in your offer: "${offerTitleString}".`, // Use the new descriptive message
          relatedEntityType: 'OFFER',
          relatedEntityId: interest.offerId,
          actorId: interest.interestedUserId,
          actorUsername: interestedUserUsername,
          data: JSON.stringify({ // 🔧 Add JSON.stringify here
            interestId: interest.id,
            offerId: interest.offer.id,
            offerTitle: offerTitleString,
            offerType: interest.offer.type, 
            offerAmount: interest.offer.amount,
            currencyPair: interest.offer.currencyPair,
            interestedUserId: interest.interestedUser.id,
            interestedUserUsername: interestedUserUsername,
            interestedUserReputation: interest.interestedUser.reputationLevel,
            createdAt: interest.createdAt.toISOString(),
          })
        });
      } catch (error) {
        console.error('[OfferRoutes] Failed to create notification for new interest:', error);
      }
    }
  } else if (offer.userId === jwtPayload.userId) {
    // This case should ideally be caught earlier by: if (offer.userId === jwtPayload.userId) return c.json(...)
    console.warn('[OfferRoutes] Attempted to show interest in own offer, notification/socket event skipped.');
  } else {
    console.error('[OfferRoutes] Could not emit INTEREST_RECEIVED or create notification due to missing data in interest object:', {
      hasOffer: !!interest.offer,
      hasOfferUser: !!(interest.offer && interest.offer.user),
      hasInterestedUser: !!interest.interestedUser
    });
  }

  return c.json({
    message: 'Interest shown',
    interestId: interest.id,
    offerId: interest.offerId,
    status: interest.status,
    createdAt: interest.createdAt.toISOString(),
  }, 201);
});

export default offerRouter;
