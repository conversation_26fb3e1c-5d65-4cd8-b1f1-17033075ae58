// ⚠️ DEPRECATED: This file is deprecated and will be removed in a future version.
// All socket functionality has been migrated to centralizedSocketManager.ts
// Do not import from this file - use centralizedSocketManager instead.

// src/services/socketService.ts
import { io as ioClient, Socket } from 'socket.io-client';
import { useAuthStore } from '@/stores/auth'; // Corrected path
import { useConnectionStore, type TransportType } from '@/stores/connection';
import { INTEREST_RECEIVED, type InterestReceivedPayload } from '@/types/socketEvents';

// Type guard function to validate transport type
function isValidTransportType(transport: any): transport is TransportType {
  return typeof transport === 'string' && (transport === 'websocket' || transport === 'polling');
}

// Helper function to safely get transport type with fallback
function getSafeTransportType(transport: any, fallback: TransportType = 'websocket'): TransportType {
  return isValidTransportType(transport) ? transport : fallback;
}

export let socket: Socket | null = null; // Export socket

// Browser online/offline detection
let isOnlineListenerAdded = false;

// Health check interval ID for cleanup
let healthCheckIntervalId: number | null = null;

// Define handler functions outside so they can be referenced for removal
const handleOnline = () => {
  console.log('🌐 [SocketService] Browser back online, checking socket connection...');
  if (socket && !socket.connected) {
    console.log('[SocketService] Attempting reconnection due to browser online event');
    socket.connect();
  }
};

const handleOffline = () => {
  console.log('🚫 [SocketService] Browser went offline');
  const connectionStore = useConnectionStore();
  connectionStore.setDisconnected('browser_offline');
};

function setupBrowserOfflineDetection() {
  if (isOnlineListenerAdded) return;
  
  const connectionStore = useConnectionStore();
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  isOnlineListenerAdded = true;
  
  // Initial check
  if (!navigator.onLine) {
    console.log('🚫 [SocketService] Browser is currently offline');
    connectionStore.setDisconnected('browser_offline');
  }
}

function cleanupBrowserOfflineDetection() {
  if (!isOnlineListenerAdded) return;
  
  console.log('[SocketService] Cleaning up browser offline detection listeners');
  window.removeEventListener('online', handleOnline);
  window.removeEventListener('offline', handleOffline);
  
  isOnlineListenerAdded = false;
}

// Global event handler for interest events to ensure they're captured
let interestReceivedHandlers: ((data: InterestReceivedPayload) => void)[] = [];

export function onInterestReceived(handler: (data: InterestReceivedPayload) => void) {
  // Create a handler name for debugging
  const handlerName = handler.name || 'anonymous';
  debugLog(`Registering INTEREST_RECEIVED handler: ${handlerName} (total handlers: ${interestReceivedHandlers.length + 1})`);
  
  interestReceivedHandlers.push(handler);
  
  // Return cleanup function
  return () => {
    debugLog(`Removing INTEREST_RECEIVED handler: ${handlerName} (total handlers before removal: ${interestReceivedHandlers.length})`);
    interestReceivedHandlers = interestReceivedHandlers.filter(h => h !== handler);
    debugLog(`Handlers remaining after removal: ${interestReceivedHandlers.length}`);
  };
}

export function initNotificationHandlers(currentSocket: Socket) { 
  // Clear existing listeners first to avoid duplicates
  currentSocket.off(INTEREST_RECEIVED);
  
  debugLog(`Setting up global INTEREST_RECEIVED handler on socket ${currentSocket.id}`);
  
  // Set up the global interest received handler
  currentSocket.on(INTEREST_RECEIVED, (payload: InterestReceivedPayload) => {
    debugLog(`� RECEIVED INTEREST_RECEIVED EVENT with payload:`, payload);
    
    // Show current number of handlers
    debugLog(`Forwarding to ${interestReceivedHandlers.length} registered handlers`);
    
    // Forward to all registered handlers
    interestReceivedHandlers.forEach((handler, index) => {
      try {
        debugLog(`Calling handler #${index + 1} (${handler.name || 'anonymous'})`);
        handler(payload);
      } catch (err) {
        console.error(`🔥 [SocketService] Error in interest handler #${index + 1}:`, err);
      }
    });
  });
}

// Create a debugging tool for tracking socket events and connections
const SOCKET_DEBUG = import.meta.env.DEV || import.meta.env.VITE_SOCKET_DEBUG === 'true';

function debugLog(...args: any[]) {
  if (SOCKET_DEBUG) {
    console.log('🔌 [SocketDebug]', ...args);
  }
}

// Helper function to log emitted events going through socket
export function attachSocketDebugger(socketInstance: Socket) {
  if (!SOCKET_DEBUG || !socketInstance) return;
  
  // Keep the original emit method
  const originalEmit = socketInstance.emit;
  
  // Override the emit method to add logging
  socketInstance.emit = function(ev: any, ...args: any[]) {
    debugLog(`Event EMIT: ${ev}`, args.length > 0 ? args[0] : '');
    return originalEmit.apply(this, [ev, ...args]);
  };
  
  // Log all incoming events
  const originalOnevent = (socketInstance as any).onevent;
  (socketInstance as any).onevent = function(packet: any) {
    const event = packet.data[0];
    const data = packet.data[1];
    debugLog(`Event RECEIVED: ${event}`, data || '');
    originalOnevent.call(this, packet);
  };
  
  debugLog('Socket debugger attached to socket ID:', socketInstance.id);
}

export function initSocket(): Socket {
  const authStore = useAuthStore();
  const connectionStore = useConnectionStore();
  
  console.log('🔥 [SocketService] initSocket called, socket exists:', !!socket, 'connected:', socket?.connected);
  
  // If socket exists and is connected, return it immediately
  if (socket?.connected) {
    console.log('🔥 [SocketService] Socket already connected, returning existing socket');
    return socket;
  }
  
  // Setup browser offline detection (cleanup if already exists to prevent duplicates)
  if (isOnlineListenerAdded) {
    cleanupBrowserOfflineDetection();
  }
  setupBrowserOfflineDetection();
  
  const token = authStore.token;
  if (!token) {
    console.error('[SocketService] Cannot initialize socket: No authentication token found');
    throw new Error('Cannot initialize socket: No authentication token found');
  }
  
  // Only disconnect and recreate if socket exists but is not connected
  if (socket && !socket.connected) {
    console.log('🔥 [SocketService] Socket exists but not connected, disconnecting to create new one');
    socket.disconnect();
    socket = null;
  } else if (socket?.connected) {
    // Socket is connected, just return it
    console.log('🔥 [SocketService] Socket is connected, returning existing socket');
    return socket;
  }

  // Clear any existing health check interval before creating a new one
  if (healthCheckIntervalId !== null) {
    clearInterval(healthCheckIntervalId);
    healthCheckIntervalId = null;
  }
  // Always create a new socket with the current token
  const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000';
  debugLog('Initializing socket with URL:', backendUrl);
  
  socket = ioClient(backendUrl, {
    auth: { token },
    // Enhanced configuration for better fallback handling
    transports: ['websocket', 'polling'], // Explicit fallback
    timeout: 10000, // 10s connection timeout
    reconnectionAttempts: 5, // Limit retry attempts
    reconnectionDelay: 1000, // Start with 1s delay
    reconnectionDelayMax: 5000, // Max 5s delay
  });
  
  // Attach debugger to the new socket
  attachSocketDebugger(socket);
  
  initNotificationHandlers(socket);
    // Enhanced connection monitoring
  socket.on('connect', () => {
    console.log('✅ [SocketService] Connected to server with ID:', socket?.id);
    connectionStore.setConnected(true);
    const transportName = socket?.io.engine.transport.name;
    connectionStore.setTransportType(getSafeTransportType(transportName, 'websocket'));
    
    // Monitor transport upgrades/downgrades
    socket?.io.engine.on('upgrade', () => {
      const upgradedTransport = socket?.io.engine.transport.name;
      console.log('⬆️ [SocketService] Upgraded to:', upgradedTransport);
      connectionStore.setTransportType(getSafeTransportType(upgradedTransport, 'websocket'));
    });
    
    socket?.io.engine.on('upgradeError', () => {
      const fallbackTransport = socket?.io.engine.transport.name;
      console.log('⬇️ [SocketService] Fallback to:', fallbackTransport);
      connectionStore.setTransportType(getSafeTransportType(fallbackTransport, 'polling'));
    });
  });

  socket.on('disconnect', (reason: Socket.DisconnectReason) => {
    console.log('🔌 [SocketService] Disconnected from server:', reason);
    connectionStore.setDisconnected(reason);
    
    if (reason === 'io server disconnect') {
      // Server initiated disconnect - might be maintenance
      console.log('[SocketService] Server disconnect detected, attempting manual reconnect...');
      socket?.connect(); // Manual reconnect
    }
  });

  socket.on('connect_error', (err: Error) => {
    console.error('❌ [SocketService] Connection error:', err.message);
    connectionStore.setReconnecting();
    
    // Check if browser is offline
    if (!navigator.onLine) {
      console.log('[SocketService] Connection error due to browser offline state');
      connectionStore.setDisconnected('browser_offline');
    }
  });

  // Monitor reconnection attempts
  socket.on('reconnect_attempt', (attemptNumber: number) => {
    console.log(`🔄 [SocketService] Reconnection attempt #${attemptNumber}`);
    connectionStore.setReconnecting();
  });

  socket.on('reconnect', (attemptNumber: number) => {
    console.log(`✅ [SocketService] Reconnected after ${attemptNumber} attempts`);
    connectionStore.setConnected(true);
    connectionStore.resetReconnectAttempts();
  });

  socket.on('reconnect_failed', () => {
    console.error('❌ [SocketService] Failed to reconnect after maximum attempts');
    connectionStore.setDisconnected('reconnect_failed');  });

  // Periodic connection health check
  healthCheckIntervalId = setInterval(() => {
    if (!navigator.onLine && connectionStore.isConnected) {
      console.log('[SocketService] Browser offline detected during health check');
      connectionStore.setDisconnected('browser_offline');
    } else if (navigator.onLine && !connectionStore.isConnected && socket?.connected) {
      console.log('[SocketService] Socket connected but store shows disconnected, syncing...');
      connectionStore.setConnected(true);
    }
  }, 3000); // Check every 3 seconds

  return socket;
}

export function getSocket(): Socket {
  if (!socket) {
    debugLog('getSocket called before initSocket or after disconnect. Attempting init.');
    // Attempt to initialize if not already done
    const newSocket = initSocket();
    
    // Attach debugger to the new socket
    attachSocketDebugger(newSocket);
    return newSocket;
  }
  return socket;
}

export function disconnectSocket() {
  const connectionStore = useConnectionStore();
  
  if (socket) {
    console.log('[SocketService] Disconnecting socket.');
    socket.disconnect();
    socket = null;
    connectionStore.setDisconnected('manual_disconnect');
  }
  
  // Clear the health check interval to prevent memory leaks
  if (healthCheckIntervalId !== null) {
    clearInterval(healthCheckIntervalId);
    healthCheckIntervalId = null;
    console.log('[SocketService] Health check interval cleared.');
  }
  
  // Clean up browser offline detection listeners to prevent memory leaks
  cleanupBrowserOfflineDetection();
}
