<template>
  <div class="offline-test-page">
    <n-card title="Offline Bug Report Testing">
      <div class="space-y-4">
        <n-alert type="info">
          <p>This page is for testing the offline bug report functionality.</p>
          <p>Use the browser's developer tools to simulate network conditions:</p>
          <ul>
            <li>Open DevTools (F12)</li>
            <li>Go to Network tab</li>
            <li>Set throttling to "Offline" to simulate no connection</li>
            <li>Try submitting a bug report - it should be stored offline</li>
            <li>Set throttling back to "Online" - reports should auto-submit</li>
          </ul>
        </n-alert>

        <div class="test-controls">
          <n-space>
            <n-button @click="simulateOffline" type="warning">
              Simulate Offline (navigator.onLine = false)
            </n-button>
            <n-button @click="simulateOnline" type="success">
              Simulate Online (navigator.onLine = true)
            </n-button>
            <n-button @click="clearOfflineReports" type="error">
              Clear Offline Reports
            </n-button>
          </n-space>
        </div>

        <div class="status-info">
          <n-descriptions bordered :column="2">
            <n-descriptions-item label="Browser Online Status">
              <n-tag :type="isOnline ? 'success' : 'error'">
                {{ isOnline ? 'Online' : 'Offline' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Socket Connection">
              <n-tag :type="connectionStore.isConnected ? 'success' : 'error'">
                {{ connectionStore.isConnected ? 'Connected' : 'Disconnected' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Offline Reports Count">
              <n-tag type="info">
                {{ offlineReports.offlineReportCount.value }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Processing Queue">
              <n-tag :type="offlineReports.isProcessingQueue.value ? 'warning' : 'default'">
                {{ offlineReports.isProcessingQueue.value ? 'Processing...' : 'Idle' }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <div class="debug-report-section">
          <h3>Test Bug Report Submission</h3>
          <DebugReportButtonEnhanced />
        </div>

        <div class="offline-reports-list" v-if="offlineReports.hasOfflineReports.value">
          <h3>Offline Reports ({{ offlineReports.offlineReportCount.value }})</h3>
          <n-list>
            <n-list-item v-for="report in offlineReports.offlineReports.value" :key="report.id">
              <div class="report-item">
                <div class="report-header">
                  <strong>{{ report.reportPayload.reportDetails.title }}</strong>
                  <n-tag size="small">{{ report.reportPayload.reportDetails.type }}</n-tag>
                </div>
                <div class="report-meta">
                  <span>Created: {{ formatDate(report.timestamp) }}</span>
                  <span>Retries: {{ report.retryCount }}</span>
                  <span v-if="report.lastRetryAt">Last Retry: {{ formatDate(report.lastRetryAt) }}</span>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </div>

        <div class="test-instructions">
          <n-card title="Test Instructions">
            <ol>
              <li>Click "Simulate Offline" to set navigator.onLine to false</li>
              <li>Open the debug report modal and submit a report</li>
              <li>Verify the report appears in the "Offline Reports" list below</li>
              <li>Click "Simulate Online" to restore connectivity</li>
              <li>Wait a few seconds - the offline reports should automatically submit</li>
              <li>Verify the offline reports list becomes empty</li>
            </ol>
          </n-card>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useConnectionStore } from '@/stores/connection'
import { useOfflineReports } from '@/composables/useOfflineReports'
import DebugReportButtonEnhanced from '@/components/DebugReportButtonEnhanced.vue'

const message = useMessage()
const connectionStore = useConnectionStore()
const offlineReports = useOfflineReports()

const isOnline = ref(navigator.onLine)

// Update online status when it changes
const updateOnlineStatus = () => {
  isOnline.value = navigator.onLine
}

onMounted(() => {
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
})

onUnmounted(() => {
  window.removeEventListener('online', updateOnlineStatus)
  window.removeEventListener('offline', updateOnlineStatus)
})

const simulateOffline = () => {
  // Override navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: false
  })
  isOnline.value = false
  connectionStore.setDisconnected('simulated_offline')
  message.warning('Simulated offline mode - navigator.onLine set to false')
}

const simulateOnline = () => {
  // Restore navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  })
  isOnline.value = true
  connectionStore.setConnected(true)
  message.success('Simulated online mode - navigator.onLine set to true')
}

const clearOfflineReports = () => {
  offlineReports.clearOfflineReports()
  message.info('Offline reports cleared')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>

<style scoped>
.offline-test-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.test-controls {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.status-info {
  margin: 16px 0;
}

.debug-report-section {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.offline-reports-list {
  margin-top: 20px;
}

.report-item {
  width: 100%;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.test-instructions {
  margin-top: 20px;
}

.test-instructions ol {
  padding-left: 20px;
}

.test-instructions li {
  margin-bottom: 8px;
}
</style>
