import { Hono } from 'hono';
import { PrismaClient, PaymentReceivingInfo } from '@prisma/client'; // Added PaymentReceivingInfo
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';
import { sendVerificationEmail } from '../services/email';
import { generateVerificationToken } from '../utils/token';
import { authMiddleware } from '../middleware/auth'; // Import auth middleware
import { sendOtp, verifyOtp } from '../services/twilio'; // Import Twilio service functions
import OtpRateLimiter from '../utils/rateLimiter_new'; // Corrected: Use default import
import { 
  isValidUsername, 
  isUsernameAvailable, 
  generateUniqueUsername, 
  sanitizeUsername,
  suggestAlternativeUsernames 
} from '../utils/usernameUtils';

const prisma = new PrismaClient();
const auth = new Hono();

// --- Schemas ---

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters long'),
  username: z.string()
    .min(3, 'Username must be at least 3 characters long')
    .max(20, 'Username must be at most 20 characters long')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .refine(val => !val.startsWith('_') && !val.startsWith('-'), 'Username cannot start with underscore or hyphen')
    .refine(val => !val.endsWith('_') && !val.endsWith('-'), 'Username cannot end with underscore or hyphen')
    .optional(), // Make username optional for auto-generation
});

const loginSchema = z.object({ // Add login schema
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password cannot be empty'), // Basic check
});

// Use E.164 format for phone numbers, consistent with Twilio service
const phoneSchemaE164 = z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format (E.164 required, e.g., +15551234567)');
const otpSchema = z.string().length(6, 'OTP must be 6 digits').regex(/^\d{6}$/, 'OTP must be numeric');

const sendOtpSchema = z.object({
  phoneNumber: phoneSchemaE164,
});

const verifyOtpSchema = z.object({
  // phoneNumber: phoneSchemaE164, // We'll get the phone number from the user record, not the request body for verification
  otpCode: otpSchema,
});

// --- Environment Variables ---
// IMPORTANT: Store your JWT secret securely, e.g., in a .env file
const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key'; // Replace with env var in production
if (JWT_SECRET === 'your-default-secret-key') {
  console.warn('Warning: Using default JWT secret. Set JWT_SECRET environment variable for production.');
}
const OTP_VALIDITY_MINUTES = 5;

// --- Routes ---

// GET /api/auth/username/check/:username - Check username availability
auth.get('/username/check/:username', async (c) => {
  const username = c.req.param('username');
  
  if (!username) {
    return c.json({ error: 'Username is required' }, 400);
  }

  const sanitized = sanitizeUsername(username);
  
  if (!isValidUsername(sanitized)) {
    return c.json({ 
      available: false, 
      error: 'Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens',
      sanitizedSuggestion: sanitized || null
    }, 400);
  }

  try {
    const available = await isUsernameAvailable(sanitized);
    
    if (!available) {
      const suggestions = await suggestAlternativeUsernames(sanitized);
      return c.json({ 
        available: false, 
        message: 'Username is already taken',
        suggestions 
      });
    }
    
    return c.json({ available: true, username: sanitized });
  } catch (error) {
    console.error('Username check error:', error);
    return c.json({ error: 'Failed to check username availability' }, 500);
  }
});

// GET /api/auth/email/check/:email - Check email availability
auth.get('/email/check/:email', async (c) => {
  const email = c.req.param('email');
  
  if (!email) {
    return c.json({ error: 'Email is required' }, 400);
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return c.json({ 
      available: false, 
      error: 'Invalid email format' 
    }, 400);
  }

  try {
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true }
    });
    
    const available = !existingUser;
    
    return c.json({ 
      available,
      message: available ? 'Email is available' : 'Email is already registered'
    });
  } catch (error) {
    console.error('Email check error:', error);
    return c.json({ error: 'Failed to check email availability' }, 500);
  }
});

// --- User Profile Route (Protected) ---
auth.get('/me', authMiddleware, async (c) => {
  const userPayload = c.get('jwtPayload');

  if (!userPayload || !userPayload.userId) {
    return c.json({ error: 'Authentication required' }, 401);
  }

  try {
    const userWithDetails = await prisma.user.findUnique({
      where: { id: userPayload.userId },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneNumber: true,
        phoneVerified: true,
        createdAt: true,
        username: true,
        reputationScore: true, // Keep this if it's a stored field, otherwise it's calculated below
        reputationLevel: true, // Keep this if it's a stored field
        // Include the default payment receiving info
        paymentReceivingInfo: {
          where: { isDefaultForUser: true },
          take: 1, // Ensures we only get one record if, by some chance, there were multiple
        },
      },
    });

    if (!userWithDetails) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Extract paymentReceivingInfo and the rest of the user data
    const { paymentReceivingInfo, ...user } = userWithDetails;
    const defaultPaymentReceivingInfo: PaymentReceivingInfo | null = paymentReceivingInfo && paymentReceivingInfo.length > 0 ? paymentReceivingInfo[0] : null;


    const createdAt = user.createdAt || new Date('2025-05-01'); // Fallback for older accounts

    const calculatedReputationScore = calculateReputationScore({
      ...user,
      createdAt,
      // Pass phoneVerified and emailVerified explicitly if they might be null/undefined on user object
      phoneVerified: user.phoneVerified || false,
      emailVerified: user.emailVerified || false,
    });
    
    const calculatedReputationLevel = calculateReputationLevel(calculatedReputationScore);
    const derivedUsername = user.username || user.email.split('@')[0];    // Debug log to check values
    console.log(`[/auth/me] User ${user.email}: DB Level=${user.reputationLevel}, Calculated Level=${calculatedReputationLevel}, Score=${calculatedReputationScore}`);

    // Extract reputation fields from user to avoid duplicate properties
    const { reputationScore: dbScore, reputationLevel: dbLevel, ...userWithoutReputation } = user;
    
    return c.json({
      ...userWithoutReputation, // Spread user WITHOUT reputation fields
      reputationScore: calculatedReputationScore, // Use calculated values
      reputationLevel: calculatedReputationLevel,
      createdAt,
      username: derivedUsername,
      defaultPaymentReceivingInfo: defaultPaymentReceivingInfo,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return c.json({ error: 'Failed to fetch user profile' }, 500);
  }
});

// Helper function to calculate reputation score
function calculateReputationScore(user: {
  emailVerified: boolean;
  phoneVerified: boolean;
  createdAt: Date;
}): number {
  let score = 0;
  if (user.emailVerified) {
    score += 10;
  }
  if (user.phoneVerified) {
    score += 15;
  }
  const accountAgeInDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));
  score += Math.min(accountAgeInDays, 30);
  return score;
}

// Helper function to map score to level (1-5) - temporary implementation
// Will be replaced by proper implementation in MUN-018
function calculateReputationLevel(score: number): number {
  if (score < 10) return 1; // Newcomer
  if (score < 25) return 2; // Verified
  if (score < 40) return 3; // Reliable
  if (score < 60) return 4; // Trusted
  return 5; // Elite
}

auth.post('/register', zValidator('json', registerSchema), async (c) => {
  const { email, password, username: providedUsername } = c.req.valid('json');
  const saltRounds = 10; // Recommended salt rounds

  try {
    // Check if user already exists by email (this check is still useful)
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      return c.json({ error: 'Email already in use' }, 409); // 409 Conflict
    }

    const hashedPassword = await bcrypt.hash(password, saltRounds);
    const verificationToken = generateVerificationToken();

    let finalUsername: string;

    if (providedUsername) {
      const sanitized = sanitizeUsername(providedUsername);
      if (!isValidUsername(sanitized)) {
        return c.json({
          error: 'Invalid username format',
          details: 'Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens'
        }, 400);
      }
      finalUsername = sanitized;
    } else {
      finalUsername = await generateUniqueUsername(email); // Auto-generate if not provided
    }

    let newUser;
    try {
      newUser = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          emailVerified: false,
          verificationToken,
          username: finalUsername,
        },
      });
    } catch (e: any) {
      if (e.code === 'P2002' && e.meta?.target?.includes('username')) {
        // This specific error code and meta target indicates a unique constraint violation on username
        const suggestions = await suggestAlternativeUsernames(finalUsername);
        return c.json({
          error: 'Username is already taken. Please choose another or try one of the suggestions.',
          suggestions
        }, 409);
      }
      // For other errors, or if it's not a P2002 on username, re-throw to be caught by the outer catch
      console.error('Database error during user creation:', e);
      return c.json({ error: 'Registration failed due to a database error' }, 500);
    }

    // Send verification email
    await sendVerificationEmail(email, verificationToken);

    // Don't return sensitive data
    const { password: _, verificationToken: __, ...userWithoutSensitive } = newUser;

    return c.json({
      message: 'Registration successful. Please check your email to verify your account.',
      user: userWithoutSensitive,
    }, 201); // 201 Created

  } catch (error) {
    // This outer catch will handle errors not caught by the specific P2002 check,
    // or errors from other operations like hashing password, generating token, sending email.
    console.error('Registration error:', error);
    return c.json({ error: 'Registration failed' }, 500);
  }
});

// --- Login Route ---
auth.post('/login', zValidator('json', loginSchema), async (c) => {
  const { email, password } = c.req.valid('json');

  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return c.json({ error: 'Invalid email or password' }, 401); // Unauthorized
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return c.json({ 
        error: 'Please verify your email before logging in',
        needsVerification: true 
      }, 403); // 403 Forbidden
    }

    // Compare password hash
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return c.json({ error: 'Invalid email or password' }, 401); // Unauthorized
    }

    // Generate JWT
    const payload = { userId: user.id, email: user.email };
    // Use non-blocking sign method if in a high-concurrency environment, but sync is often fine here.
    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' }); // Token expires in 1 hour

    // Don't return the password hash
    const { password: _, verificationToken: __, ...userWithoutSensitive } = user;

    return c.json({
      message: 'Login successful',
      token: token,
      user: userWithoutSensitive,
    });

  } catch (error) {
    console.error('Login error:', error);
    return c.json({ error: 'Login failed' }, 500);
  }
});

// Email Verification Route
auth.get('/verify-email', async (c) => {
    const token = c.req.query('token');

    if (!token) {
        return c.json({ error: 'Verification token is required' }, 400);
    }

    try {
        // Find user by verification token
        const user = await prisma.user.findUnique({
            where: { verificationToken: token },
        });

        if (!user) {
            return c.json({ error: 'Invalid verification token' }, 400);
        }

        // Update user as verified
        await prisma.user.update({
            where: { id: user.id },
            data: {
                emailVerified: true,
                verificationToken: null, // Clear the token after use
            },
        });

        return c.json({ message: 'Email verified successfully' });

    } catch (error) {
        console.error('Email verification error:', error);
        return c.json({ error: 'Email verification failed' }, 500);
    }
});

// Resend Email Verification Route (Protected)
auth.post('/resend-verification-email', authMiddleware, async (c) => {
    const userPayload = c.get('jwtPayload');

    if (!userPayload || !userPayload.userId) {
        return c.json({ error: 'Authentication required' }, 401);
    }

    try {
        // Find the current user
        const user = await prisma.user.findUnique({
            where: { id: userPayload.userId },
            select: { id: true, email: true, emailVerified: true }
        });

        if (!user) {
            return c.json({ error: 'User not found' }, 404);
        }

        if (user.emailVerified) {
            return c.json({ error: 'Email is already verified' }, 400);
        }

        // Generate a new verification token
        const verificationToken = generateVerificationToken();

        // Update user with new verification token
        await prisma.user.update({
            where: { id: user.id },
            data: {
                verificationToken,
            },
        });

        // Send new verification email
        await sendVerificationEmail(user.email, verificationToken);

        return c.json({
            message: 'Verification email sent successfully. Please check your email.',
        });

    } catch (error) {
        console.error('Resend verification email error:', error);
        return c.json({ error: 'Failed to resend verification email' }, 500);
    }
});

// Unauthenticated Resend Email Verification Route
auth.post('/resend-verification-email-public', zValidator('json', z.object({
    email: z.string().email('Invalid email format')
})), async (c) => {
    const { email } = c.req.valid('json');

    try {
        // Find user by email
        const user = await prisma.user.findUnique({
            where: { email },
            select: { id: true, email: true, emailVerified: true }
        });

        // Always return success to prevent email enumeration attacks
        // But only send email if user exists and is not verified
        if (user && !user.emailVerified) {
            // Generate a new verification token
            const verificationToken = generateVerificationToken();

            // Update user with new verification token
            await prisma.user.update({
                where: { id: user.id },
                data: {
                    verificationToken,
                },
            });

            // Send new verification email
            await sendVerificationEmail(user.email, verificationToken);
        }

        // Always return success message regardless of whether user exists or is already verified
        // This prevents email enumeration attacks
        return c.json({
            message: 'If an account with that email exists and is not yet verified, a verification email has been sent.',
        });

    } catch (error) {
        console.error('Public resend verification email error:', error);
        return c.json({ error: 'Failed to process verification email request' }, 500);
    }
});

// --- Phone Verification Routes (Protected) ---

// Apply auth middleware specifically to the phone verification routes
auth.use('/phone/*', authMiddleware);

// Send Phone Verification OTP via Twilio
auth.post('/phone/send-otp', zValidator('json', sendOtpSchema), async (c) => {
  const { phoneNumber } = c.req.valid('json');
  const userPayload = c.get('jwtPayload');

  if (!userPayload || !userPayload.userId) {
    return c.json({ error: 'Authentication required' }, 401);
  }
  const userId = userPayload.userId;

  // Instantiate the rate limiter at the beginning of the handler
  const rateLimiter = OtpRateLimiter.getInstance();

  try {
    // Check rate limits first
    let limitCheck = rateLimiter.checkSendLimit(phoneNumber); // Use 'let'

    if (!limitCheck.allowed) {
      const waitMinutes = Math.ceil((limitCheck.blockedUntil! - Date.now()) / (60 * 1000));
      // *** FIX: Return 429 with rateLimitInfo when blocked ***
      return c.json({
        error: `Too many OTP requests. Please wait ${waitMinutes} minutes before trying again.`,
        rateLimitInfo: {
          remainingAttempts: limitCheck.remainingAttempts,
          blockedUntil: limitCheck.blockedUntil
        }
      }, 429); // <-- Return 429 status
    }

    // 1. Check if the phone number is already verified by *another* user
    const existingVerifiedPhone = await prisma.user.findFirst({
        where: {
            phoneNumber: phoneNumber,
            phoneVerified: true,
            NOT: {
                id: userId,
            },
        },
    });

    if (existingVerifiedPhone) {
        return c.json({ error: 'Phone number already in use by another account.' }, 409);
    }

    // 2. Update the current user's record (ensure phone is stored before sending OTP)
    await prisma.user.update({
        where: { id: userId },
        data: {
            phoneNumber: phoneNumber,
            phoneVerified: false,
            // Reset any old OTP secrets/timestamps if changing number
            // otpSecret: null, // Assuming Twilio handles secrets
            // otpTimestamp: null,
        },
    });


    // 3. Send the OTP and record the attempt
    await sendOtp(phoneNumber); // Assuming sendOtp throws on failure
    rateLimiter.recordSendAttempt(phoneNumber);

    // *** FIX: Get updated rate limit info AFTER recording the attempt ***
    const updatedLimitInfo = rateLimiter.checkSendLimit(phoneNumber);

    // *** FIX: Return updated rateLimitInfo on success ***
    return c.json({
      message: 'OTP sent successfully to your phone number.',
      rateLimitInfo: {
        remainingAttempts: updatedLimitInfo.remainingAttempts,
        blockedUntil: updatedLimitInfo.blockedUntil // Include blockedUntil
      }
    });

  } catch (error: any) {
    console.error('Error processing /phone/send-otp:', error); // Log the actual error origin

    // *** FIX: Use the rateLimiter instance from the outer scope ***
    // Attempt to get current rate limit status even on error
    const currentLimitStatus = rateLimiter.checkSendLimit(phoneNumber); // Use the phone number from the request

    // If the error is specifically a rate limit exceeded error (customize message check if needed)
    if (error.message?.includes('Max send attempts reached') || error.status === 429) {
         return c.json({
             error: error.message || 'Rate limit exceeded.',
             rateLimitInfo: {
                 remainingAttempts: currentLimitStatus.remainingAttempts,
                 blockedUntil: currentLimitStatus.blockedUntil
             }
         }, 429);
    }

    // Otherwise, return a generic 500 but still include current rate limit info if possible
    return c.json({
        error: error.message || 'Failed to send OTP. Please try again later.',
        rateLimitInfo: { // Include current status even on generic errors
            remainingAttempts: currentLimitStatus.remainingAttempts,
            blockedUntil: currentLimitStatus.blockedUntil
        }
     }, 500);
  }
});

// Verify Phone OTP via Twilio
auth.post('/phone/verify-otp', zValidator('json', verifyOtpSchema), async (c) => {
  const { otpCode } = c.req.valid('json');
  const userPayload = c.get('jwtPayload');

  if (!userPayload || !userPayload.userId) {
    return c.json({ error: 'Authentication required' }, 401);
  }
  const userId = userPayload.userId;

  try {
    // 1. Retrieve the user and their stored phone number
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { phoneNumber: true, phoneVerified: true }, // Select only needed fields
    });

    if (!user || !user.phoneNumber) {
      return c.json({ error: 'Phone number not found. Please request a new OTP.' }, 400);
    }

    if (user.phoneVerified) {
      return c.json({ error: 'Phone number is already verified.' }, 400);
    }

    // Check rate limits for verification attempts
    const rateLimiter = OtpRateLimiter.getInstance();
    const limitCheck = rateLimiter.checkVerifyLimit(user.phoneNumber); // AFTER (remove await)

    if (!limitCheck.allowed) {
      const waitMinutes = Math.ceil((limitCheck.blockedUntil! - Date.now()) / (60 * 1000));
      return c.json({
        error: `Too many verification attempts. Please wait ${waitMinutes} minutes before trying again.`,
        rateLimitInfo: {
          remainingAttempts: limitCheck.remainingAttempts,
          blockedUntil: limitCheck.blockedUntil
        }
      }, 429);
    }

    // 2. Verify the OTP
    const isOtpValid = await verifyOtp(user.phoneNumber, otpCode); // Keep await for Twilio call
    rateLimiter.recordVerifyAttempt(user.phoneNumber); // AFTER (remove await)

    if (isOtpValid) {
      // Update user's phone verification status
      await prisma.user.update({
        where: { id: userId },
        data: {
          phoneVerified: true,
        },
      });

      return c.json({ 
        message: 'Phone number verified successfully.',
        phoneVerified: true
      });
    } else {
      // *** FIX: Return updated rateLimitInfo on invalid OTP ***
      // Get the latest limit info *after* recording the failed attempt
      const updatedLimitInfo = rateLimiter.checkVerifyLimit(user.phoneNumber);
      return c.json({
        error: 'Invalid verification code.',
        rateLimitInfo: {
          // Use the updated remaining attempts
          remainingAttempts: updatedLimitInfo.remainingAttempts,
          blockedUntil: updatedLimitInfo.blockedUntil // Also include blockedUntil status
        }
      }, 400);
    }

  } catch (error: any) {
    console.error('Error verifying phone OTP:', error);
    return c.json({ error: error.message || 'Failed to verify OTP. Please try again later.' }, 500);
  }
});

export default auth;
