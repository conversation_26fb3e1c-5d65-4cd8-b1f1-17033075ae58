<template>
  <div class="debug-dashboard">
    <!-- Custom Header -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="dashboard-title">Debug Reports Dashboard</h1>
          <p class="dashboard-subtitle">Manage and review client-side debug reports</p>
        </div>
        <div class="header-actions">
          <button 
            class="btn btn-primary" 
            @click="refreshReports" 
            :disabled="loading"
            :class="{ loading: loading }"
          >
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
            </svg>
            {{ loading ? 'Loading...' : 'Refresh' }}
          </button>
          <button class="btn btn-secondary" @click="exportAllReports" :disabled="!hasReports">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            Export All
          </button>
          <button class="btn btn-ghost" @click="toggleTheme">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,1 12,2Z"/>
            </svg>
          </button>
          <button class="btn btn-ghost" @click="showHelp = true" title="Keyboard Shortcuts (?)">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,1 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
      <!-- Filters Section -->
      <section class="filters-section" :class="{ collapsed: !showFilters }">
        <div class="filters-header" @click="toggleFilters">
          <h3 class="filters-title">Filters & Search</h3>
          <button class="btn btn-ghost btn-sm">
            <svg class="icon chevron" viewBox="0 0 24 24" :class="{ rotated: !showFilters }">
              <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
            </svg>
          </button>
        </div>
        
        <div class="filters-content" v-show="showFilters">
          <div class="filters-grid">
            <!-- Search Input -->
            <div class="filter-group">
              <label class="filter-label">Search Reports</label>
              <div class="search-input-container">
                <svg class="search-icon" viewBox="0 0 24 24">
                  <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
                <input 
                  v-model="searchTerm" 
                  type="text" 
                  class="search-input"
                  placeholder="Search by user ID, error type, or message..."
                  @input="debouncedSearch"
                />
                <button 
                  v-if="searchTerm" 
                  class="clear-search" 
                  @click="clearSearch"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Level Filter -->
            <div class="filter-group">
              <label class="filter-label">Log Level</label>
              <div class="custom-select">
                <select v-model="selectedLevel" @change="applyFilters">
                  <option value="">All Levels</option>
                  <option value="error">Error</option>
                  <option value="warn">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
                <svg class="select-arrow" viewBox="0 0 24 24">
                  <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                </svg>
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="filter-group">
              <label class="filter-label">Date Range</label>
              <div class="date-range">
                <input 
                  v-model="dateRange.start" 
                  type="date" 
                  class="date-input"
                  @change="applyFilters"
                />
                <span class="date-separator">to</span>
                <input 
                  v-model="dateRange.end" 
                  type="date" 
                  class="date-input"
                  @change="applyFilters"
                />
              </div>
            </div>

            <!-- Page Size -->
            <div class="filter-group">
              <label class="filter-label">Page Size</label>
              <div class="custom-select">
                <select v-model="pageSize" @change="changePageSize">
                  <option :value="10">10 per page</option>
                  <option :value="25">25 per page</option>
                  <option :value="50">50 per page</option>
                  <option :value="100">100 per page</option>
                </select>
                <svg class="select-arrow" viewBox="0 0 24 24">
                  <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                </svg>
              </div>
            </div>

            <!-- Clear All Filters -->
            <div class="filter-group">
              <button class="btn btn-ghost btn-full" @click="clearAllFilters">
                <svg class="icon" viewBox="0 0 24 24">
                  <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                </svg>
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Reports Statistics -->
      <section class="stats-section" v-if="hasReports">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ totalReports }}</div>
            <div class="stat-label">Total Reports</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ errorCount }}</div>
            <div class="stat-label">Errors</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ warningCount }}</div>
            <div class="stat-label">Warnings</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recentCount }}</div>
            <div class="stat-label">Last 24h</div>
          </div>
        </div>
      </section>

      <!-- Data Table -->
      <section class="table-section">
        <div class="table-header">
          <h3 class="table-title">Debug Reports</h3>
          <div class="table-actions">
            <span class="results-count" v-if="hasReports">
              Showing {{ startIndex }} - {{ endIndex }} of {{ totalReports }} reports
            </span>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>Loading debug reports...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="!hasReports" class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M16,11H8V13H16V11M16,15H8V17H16V15Z"/>
          </svg>
          <h3>No Reports Found</h3>
          <p>No debug reports match your current filters.</p>
          <button class="btn btn-primary" @click="clearAllFilters">
            Clear Filters
          </button>
        </div>

        <!-- Data Table -->
        <div v-else class="table-container">
          <table class="custom-table">
            <thead>
              <tr>
                <th 
                  v-for="column in tableColumns" 
                  :key="column.key"
                  :class="{ sortable: column.sortable, active: sortBy === column.key }"
                  @click="column.sortable && sortTable(column.key)"
                >
                  <div class="th-content">
                    <span>{{ column.title }}</span>
                    <svg 
                      v-if="column.sortable" 
                      class="sort-icon" 
                      viewBox="0 0 24 24"
                      :class="{ 
                        desc: sortBy === column.key && sortOrder === 'desc',
                        asc: sortBy === column.key && sortOrder === 'asc'
                      }"
                    >
                      <path d="M3,13H15L10.5,7.5L6,13H3M6,17L10.5,22.5L15,17H6Z"/>
                    </svg>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>              <tr 
                v-for="report in paginatedReports" 
                :key="getReportId(report)"
                class="table-row"
                @click="openReportDetails(report)"
              >
                <td class="timestamp-cell">
                  <div class="timestamp">
                    {{ formatDateTime(getReportTimestamp(report)) }}
                  </div>
                </td>
                <td class="level-cell">
                  <span class="level-badge" :class="`level-${getReportLevel(report)}`">
                    {{ getReportLevel(report).toUpperCase() }}
                  </span>
                </td>
                <td class="user-cell">
                  <div class="user-info">
                    <span class="user-id">{{ report.reportId || 'Anonymous' }}</span>
                  </div>
                </td>
                <td class="message-cell">
                  <div class="message-content">
                    <span class="message-text">{{ truncateMessage(getReportMessage(report)) }}</span>
                    <span v-if="getReportMessage(report).length > 100" class="message-more">...</span>
                  </div>
                </td>
                <td class="url-cell">
                  <div class="url-content">
                    {{ getReportUrl(report) ? getUrlPath(getReportUrl(report)) : 'N/A' }}
                  </div>
                </td>
                <td class="actions-cell">
                  <div class="action-buttons">
                    <button 
                      class="btn btn-sm btn-ghost" 
                      @click.stop="openReportDetails(report)"
                    >
                      <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                      </svg>
                    </button>
                    <button 
                      class="btn btn-sm btn-ghost" 
                      @click.stop="downloadSingleReport(report)"
                    >
                      <svg class="icon" viewBox="0 0 24 24">
                        <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="hasReports && totalPages > 1" class="pagination-container">
          <div class="pagination">
            <button 
              class="page-btn" 
              :disabled="currentPage === 1"
              @click="goToPage(1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"/>
              </svg>
            </button>
            <button 
              class="page-btn" 
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"/>
              </svg>
            </button>

            <div class="page-numbers">
              <button 
                v-for="page in visiblePages" 
                :key="page"
                class="page-btn page-number" 
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
              >
                {{ page }}
              </button>
            </div>

            <button 
              class="page-btn" 
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"/>
              </svg>
            </button>
            <button 
              class="page-btn" 
              :disabled="currentPage === totalPages"
              @click="goToPage(totalPages)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"/>
              </svg>
            </button>
          </div>
          <div class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </div>
        </div>
      </section>
    </main>

    <!-- Custom Modal for Report Details -->
    <div v-if="selectedReport" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">Debug Report Details</h2>
          <button class="modal-close" @click="closeModal">
            <svg viewBox="0 0 24 24">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
          </button>
        </div>
        
        <div class="modal-content">
          <div class="report-details">
            <div class="detail-section">              <h4>Basic Information</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>Report ID</label>
                  <span>{{ getReportId(selectedReport) }}</span>
                </div>
                <div class="detail-item">
                  <label>Timestamp</label>
                  <span>{{ formatDateTime(getReportTimestamp(selectedReport)) }}</span>
                </div>
                <div class="detail-item">
                  <label>Level</label>
                  <span class="level-badge" :class="`level-${getReportLevel(selectedReport)}`">
                    {{ getReportLevel(selectedReport).toUpperCase() }}
                  </span>
                </div>
                <div class="detail-item">
                  <label>Report Type</label>
                  <span>{{ selectedReport.reportType || 'N/A' }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>Error Details</h4>
              <div class="detail-item full-width">
                <label>Message</label>
                <div class="code-block">{{ getReportMessage(selectedReport) }}</div>
              </div>
              <div class="detail-item full-width" v-if="selectedReport.reportDescription">
                <label>Description</label>
                <div class="code-block">{{ selectedReport.reportDescription }}</div>
              </div>              <div class="detail-item full-width" v-if="getReportStackTrace(selectedReport)">
                <label>Stack Trace</label>
                <div class="code-block stack-trace">{{ getReportStackTrace(selectedReport) }}</div>
              </div>
            </div>

            <div class="detail-section">
              <h4>Context Information</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>URL</label>
                  <span>{{ getReportUrl(selectedReport) || 'N/A' }}</span>
                </div>
                <div class="detail-item">
                  <label>User Agent</label>
                  <span>{{ selectedReport.userAgent || 'N/A' }}</span>
                </div>
                <div class="detail-item">
                  <label>Session ID</label>
                  <span>{{ selectedReport.sessionId || 'N/A' }}</span>
                </div>
                <div class="detail-item">
                  <label>Log Count</label>
                  <span>{{ selectedReport.logCount || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- Log Entries Section -->
            <div class="detail-section" v-if="selectedReport.logs && selectedReport.logs.length > 0">
              <h4>Log Entries ({{ selectedReport.logs.length }})</h4>
              <div class="logs-container">
                <div 
                  v-for="(log, index) in selectedReport.logs" 
                  :key="index"
                  class="log-entry"
                  :class="`log-level-${log.level.toLowerCase()}`"
                >
                  <div class="log-header">
                    <span class="log-timestamp">{{ formatDateTime(log.timestamp) }}</span>
                    <span class="log-level-badge" :class="`level-${log.level.toLowerCase()}`">
                      {{ log.level }}
                    </span>
                  </div>
                  <div class="log-message">{{ log.message }}</div>
                  <div v-if="log.stackTrace" class="log-stack">
                    <details>
                      <summary>Stack Trace</summary>
                      <pre class="code-block">{{ log.stackTrace }}</pre>
                    </details>
                  </div>
                  <div v-if="log.context" class="log-context">
                    <details>
                      <summary>Context Data</summary>
                      <pre class="code-block">{{ formatJSON(log.context) }}</pre>
                    </details>
                  </div>
                </div>
              </div>
            </div>            <div class="detail-section" v-if="selectedReport.userNotes">
              <h4>User Notes</h4>
              <div class="detail-item full-width">
                <div class="code-block">{{ selectedReport.userNotes }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-primary" @click="downloadSingleReport(selectedReport)">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
            </svg>
            Download Report
          </button>
          <button class="btn btn-secondary" @click="closeModal">
            Close
          </button>
        </div>
      </div>
    </div>

    <!-- Help Modal for Keyboard Shortcuts -->
    <div v-if="showHelp" class="modal-overlay" @click="showHelp = false">
      <div class="modal-container help-modal" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">Keyboard Shortcuts</h2>
          <button class="modal-close" @click="showHelp = false">
            <svg viewBox="0 0 24 24">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
          </button>
        </div>
        
        <div class="modal-content">
          <div class="shortcuts-grid">
            <div class="shortcut-section">
              <h4>Navigation</h4>
              <div class="shortcut-item">
                <kbd>←</kbd><kbd>→</kbd>
                <span>Navigate pages</span>
              </div>
              <div class="shortcut-item">
                <kbd>1</kbd>-<kbd>9</kbd>
                <span>Go to page number</span>
              </div>
              <div class="shortcut-item">
                <kbd>Esc</kbd>
                <span>Close modal or clear search</span>
              </div>
            </div>

            <div class="shortcut-section">
              <h4>Actions</h4>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd>+<kbd>R</kbd>
                <span>Refresh reports</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd>+<kbd>F</kbd>
                <span>Focus search</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd>+<kbd>E</kbd>
                <span>Export all reports</span>
              </div>
            </div>

            <div class="shortcut-section">
              <h4>Tips</h4>
              <div class="shortcut-item">
                <span class="tip-icon">💡</span>
                <span>Click on any row to view details</span>
              </div>
              <div class="shortcut-item">
                <span class="tip-icon">🔍</span>
                <span>Search supports partial matches</span>
              </div>
              <div class="shortcut-item">
                <span class="tip-icon">⚡</span>
                <span>Use filters for better performance</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-primary" @click="showHelp = false">
            Got it!
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useAdminDebugStore } from '@/stores/adminDebugStore'
import { useThemeStore } from '@/stores/theme'
import type { ParsedReport } from '@/types/admin'

const adminStore = useAdminDebugStore()
const themeStore = useThemeStore()

// Reactive data
const showFilters = ref(true)
const searchTerm = ref('')
const selectedLevel = ref('')
const dateRange = ref({
  start: '',
  end: ''
})
const pageSize = ref(25)
const currentPage = ref(1)
const sortBy = ref('timestamp')
const sortOrder = ref<'asc' | 'desc'>('desc')
const selectedReport = ref<ParsedReport | null>(null)
const searchDebounceTimer = ref<number>(0)
const showHelp = ref(false)

// Table columns configuration
const tableColumns = [
  { key: 'timestamp', title: 'Timestamp', sortable: true },
  { key: 'level', title: 'Level', sortable: true },
  { key: 'userId', title: 'User ID', sortable: true },
  { key: 'message', title: 'Message', sortable: false },
  { key: 'url', title: 'URL', sortable: false },
  { key: 'actions', title: 'Actions', sortable: false }
]

// Computed properties
const loading = computed(() => adminStore.loading)
const reports = computed(() => adminStore.reports)
const hasReports = computed(() => reports.value.length > 0)
const totalReports = computed(() => adminStore.totalReports)

const filteredReports = computed(() => {
  let filtered = [...reports.value]

  // Apply search filter
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(report => 
      getReportMessage(report).toLowerCase().includes(search) ||
      getReportId(report).toLowerCase().includes(search) ||
      getReportUrl(report).toLowerCase().includes(search) ||
      (report.reportType && report.reportType.toLowerCase().includes(search))
    )
  }

  // Apply level filter
  if (selectedLevel.value) {
    filtered = filtered.filter(report => getReportLevel(report) === selectedLevel.value)
  }

  // Apply date range filter
  if (dateRange.value.start) {
    const startDate = new Date(dateRange.value.start)
    filtered = filtered.filter(report => new Date(getReportTimestamp(report)) >= startDate)  }
  if (dateRange.value.end) {
    const endDate = new Date(dateRange.value.end + 'T23:59:59')
    filtered = filtered.filter(report => new Date(getReportTimestamp(report)) <= endDate)
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let aVal: any
    let bVal: any

    switch (sortBy.value) {
      case 'timestamp':
        aVal = new Date(getReportTimestamp(a)).getTime()
        bVal = new Date(getReportTimestamp(b)).getTime()
        break
      case 'level':
        aVal = getReportLevel(a)
        bVal = getReportLevel(b)
        break
      case 'message':
        aVal = getReportMessage(a).toLowerCase()
        bVal = getReportMessage(b).toLowerCase()
        break
      default:
        aVal = a[sortBy.value as keyof ParsedReport]
        bVal = b[sortBy.value as keyof ParsedReport]
    }

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase()
      bVal = bVal.toLowerCase()
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })

  return filtered
})

const paginatedReports = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredReports.value.slice(start, end)
})

const totalPages = computed(() => Math.ceil(filteredReports.value.length / pageSize.value))

const startIndex = computed(() => {
  if (!hasReports.value) return 0
  return (currentPage.value - 1) * pageSize.value + 1
})

const endIndex = computed(() => {
  if (!hasReports.value) return 0
  return Math.min(currentPage.value * pageSize.value, filteredReports.value.length)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
})

// Statistics
const errorCount = computed(() => reports.value.filter(r => getReportLevel(r) === 'error').length)
const warningCount = computed(() => reports.value.filter(r => getReportLevel(r) === 'warn').length)
const recentCount = computed(() => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return reports.value.filter(r => new Date(getReportTimestamp(r)) >= yesterday).length
})

// Methods
const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const toggleTheme = () => {
  themeStore.toggleTheme()
}

const refreshReports = async () => {
  await adminStore.fetchReports({
    page: currentPage.value,
    limit: pageSize.value
  })
}

const debouncedSearch = () => {
  clearTimeout(searchDebounceTimer.value)
  searchDebounceTimer.value = setTimeout(() => {
    currentPage.value = 1
    applyFilters()
  }, 300)
}

const clearSearch = () => {
  searchTerm.value = ''
  applyFilters()
}

const applyFilters = () => {
  currentPage.value = 1
}

const clearAllFilters = () => {
  searchTerm.value = ''
  selectedLevel.value = ''
  dateRange.value = { start: '', end: '' }
  currentPage.value = 1
}

const changePageSize = () => {
  currentPage.value = 1
  adminStore.setLimit(pageSize.value)
}

const sortTable = (column: string) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column
    sortOrder.value = 'desc'
  }
}

const goToPage = (page: number) => {
  currentPage.value = page
  adminStore.setCurrentPage(page)
}

const openReportDetails = (report: ParsedReport) => {
  selectedReport.value = report
}

const closeModal = () => {
  selectedReport.value = null
}

const downloadSingleReport = async (report: ParsedReport) => {
  try {
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `debug-report-${report.reportId}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download report:', error)  }
}

const exportAllReports = async () => {
  try {
    await adminStore.downloadReport()
  } catch (error) {
    console.error('Failed to export reports:', error)
  }
}

// Helper functions to extract data from ParsedReport structure
const getReportLevel = (report: ParsedReport): string => {
  // Get the highest priority log level from the logs array
  const levels = report.logs.map(log => log.level);
  if (levels.includes('ERROR')) return 'error';
  if (levels.includes('WARN')) return 'warn';
  if (levels.includes('INFO')) return 'info';
  if (levels.includes('DEBUG')) return 'debug';
  return 'unknown';
}

const getReportMessage = (report: ParsedReport): string => {
  // Use report title/description or first log message
  if (report.reportTitle) return report.reportTitle;
  if (report.reportDescription) return report.reportDescription;
  if (report.logs.length > 0) return report.logs[0].message;
  return 'No message available';
}

const getReportUrl = (report: ParsedReport): string => {
  // Use current URL or first log URL
  if (report.currentUrl) return report.currentUrl;
  if (report.logs.length > 0 && report.logs[0].url) return report.logs[0].url;
  return '';
}

const getReportId = (report: ParsedReport): string => {
  return report.reportId;
}

const getReportTimestamp = (report: ParsedReport): string => {
  return report.timestamp || report.serverReceivedAt;
}

const getReportStackTrace = (report: ParsedReport): string => {
  // Find first log entry with stack trace
  const logWithStack = report.logs.find(log => log.stackTrace);
  return logWithStack?.stackTrace || '';
}

// Utility functions
const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const truncateMessage = (message: string, maxLength = 100) => {
  return message.length > maxLength ? message.substring(0, maxLength) : message
}

const getUrlPath = (url: string) => {
  try {
    return new URL(url).pathname
  } catch {
    return url
  }
}

const formatJSON = (data: any) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return String(data)
  }
}

// Lifecycle
onMounted(() => {
  refreshReports()
  setupKeyboardShortcuts()
})

// Keyboard shortcuts for developer productivity
const setupKeyboardShortcuts = () => {
  const handleKeyDown = (event: KeyboardEvent) => {
    // Only activate shortcuts when no input is focused
    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLSelectElement) {
      return
    }

    // Ctrl/Cmd + R: Refresh
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      event.preventDefault()
      refreshReports()
      return
    }

    // Ctrl/Cmd + F: Focus search
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault()
      const searchInput = document.querySelector('.search-input') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
      return
    }

    // Ctrl/Cmd + E: Export all
    if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
      event.preventDefault()
      if (hasReports.value) {
        exportAllReports()
      }
      return
    }    // Escape: Close modal or clear search
    if (event.key === 'Escape') {
      if (selectedReport.value) {
        closeModal()
      } else if (showHelp.value) {
        showHelp.value = false
      } else if (searchTerm.value) {
        clearSearch()
      }
      return
    }

    // ? key: Show help
    if (event.key === '?') {
      event.preventDefault()
      showHelp.value = true
      return
    }

    // Arrow keys for pagination (when no modal is open)
    if (!selectedReport.value) {
      if (event.key === 'ArrowLeft' && currentPage.value > 1) {
        event.preventDefault()
        goToPage(currentPage.value - 1)
      } else if (event.key === 'ArrowRight' && currentPage.value < totalPages.value) {
        event.preventDefault()
        goToPage(currentPage.value + 1)
      }
    }

    // Number keys for quick navigation
    if (event.key >= '1' && event.key <= '9') {
      const pageNum = parseInt(event.key)
      if (pageNum <= totalPages.value) {
        event.preventDefault()
        goToPage(pageNum)
      }
    }
  }

  document.addEventListener('keydown', handleKeyDown)
  
  // Cleanup
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })
}

// Performance optimizations
const throttledSort = throttle(sortTable, 100)
const throttledPageChange = throttle(goToPage, 100)

// Throttle utility
function throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: number
  let lastExecTime = 0
  return ((...args: any[]) => {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }) as T
}
</script>

<style scoped>
/* Reset and Base Styles */
.debug-dashboard {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* CSS Variables for Theming */
:root {
  --radius: 8px;
  --radius-lg: 12px;
}

/* Light Theme Variables */
[data-theme="light"] .debug-dashboard {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-elevated: #ffffff;
  --bg-hover: #f1f5f9;
  --border-color: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark Theme Variables */
[data-theme="dark"] .debug-dashboard {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-elevated: #334155;
  --bg-hover: #475569;
  --border-color: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* Header Styles */
.dashboard-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.dashboard-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
  line-height: 1.2;
}

.dashboard-subtitle {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--accent-hover);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.btn-sm {
  padding: 0.5rem;
  font-size: 0.75rem;
}

.btn-full {
  width: 100%;
}

.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  color: white;
}

/* Icon Styles */
.icon {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
  flex-shrink: 0;
}

.chevron {
  transition: transform 0.2s ease;
}

.chevron.rotated {
  transform: rotate(-180deg);
}

/* Main Content */
.dashboard-main {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Filters Section */
.filters-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.filters-header {
  padding: 1.25rem 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: between;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.filters-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  flex: 1;
}

.filters-content {
  padding: 1.5rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

/* Input Styles */
.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-muted);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.clear-search {
  position: absolute;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.clear-search svg {
  width: 1rem;
  height: 1rem;
}

/* Custom Select */
.custom-select {
  position: relative;
}

.custom-select select {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
}

.custom-select select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.select-arrow {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  color: var(--text-muted);
  pointer-events: none;
}

/* Date Range */
.date-range {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.date-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.date-separator {
  color: var(--text-muted);
  font-size: 0.875rem;
  flex-shrink: 0;
}

/* Statistics Section */
.stats-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Table Section */
.table-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.table-header {
  padding: 1.25rem 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.results-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Loading and Empty States */
.loading-state, .empty-state {
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color);
  border-top-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin: 0 auto 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0 0 1.5rem;
}

/* Custom Table */
.table-container {
  overflow-x: auto;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.custom-table th {
  background: var(--bg-secondary);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.custom-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.custom-table th.sortable:hover {
  background: var(--bg-hover);
}

.th-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-icon {
  width: 1rem;
  height: 1rem;
  color: var(--text-muted);
  transition: all 0.2s ease;
}

.sort-icon.asc {
  color: var(--accent-primary);
  transform: rotate(180deg);
}

.sort-icon.desc {
  color: var(--accent-primary);
}

.custom-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.table-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: var(--bg-hover);
}

/* Table Cell Styles */
.timestamp-cell {
  width: 180px;
}

.timestamp {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.level-cell {
  width: 100px;
}

.level-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.level-error {
  background: rgb(239 68 68 / 0.1);
  color: var(--error);
}

.level-warn {
  background: rgb(245 158 11 / 0.1);
  color: var(--warning);
}

.level-info {
  background: rgb(59 130 246 / 0.1);
  color: var(--accent-primary);
}

.level-debug {
  background: rgb(16 185 129 / 0.1);
  color: var(--success);
}

.level-unknown {
  background: rgb(107 114 128 / 0.1);
  color: var(--text-muted);
}

.user-cell {
  width: 120px;
}

.user-id {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
}

.message-cell {
  min-width: 300px;
  max-width: 400px;
}

.message-content {
  word-break: break-word;
}

.message-text {
  color: var(--text-primary);
}

.message-more {
  color: var(--text-muted);
  font-style: italic;
}

.url-cell {
  width: 200px;
}

.url-content {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
  word-break: break-all;
}

.actions-cell {
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Pagination */
.pagination-container {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-btn:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--accent-primary);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Log Entries Styles */
.logs-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--background-secondary);
}

.log-entry {
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid var(--border-color);
  background: var(--background-primary);
  transition: all 0.2s ease;
}

.log-entry:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-entry.log-level-error {
  border-left-color: var(--error-color);
  background: linear-gradient(135deg, var(--background-primary) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.log-entry.log-level-warn {
  border-left-color: var(--warning-color);
  background: linear-gradient(135deg, var(--background-primary) 0%, rgba(245, 158, 11, 0.05) 100%);
}

.log-entry.log-level-info {
  border-left-color: var(--info-color);
  background: linear-gradient(135deg, var(--background-primary) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.log-entry.log-level-debug {
  border-left-color: var(--text-tertiary);
  background: linear-gradient(135deg, var(--background-primary) 0%, rgba(107, 114, 128, 0.05) 100%);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.log-timestamp {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-family: var(--font-mono);
}

.log-level-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.log-message {
  font-size: 0.9375rem;
  line-height: 1.5;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  word-wrap: break-word;
}

.log-stack,
.log-context {
  margin-top: 0.75rem;
}

.log-stack details,
.log-context details {
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.log-stack summary,
.log-context summary {
  padding: 0.5rem 0.75rem;
  background: var(--background-secondary);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.log-stack summary:hover,
.log-context summary:hover {
  background: var(--hover-color);
}

.log-stack pre,
.log-context pre {
  margin: 0;
  padding: 0.75rem;
  background: var(--background-code);
  font-size: 0.8125rem;
  line-height: 1.4;
  overflow-x: auto;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.modal-close {
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-close svg {
  width: 1.25rem;
  height: 1.25rem;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Report Details */
.report-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.detail-item span {
  color: var(--text-primary);
}

.code-block {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.stack-trace {
  max-height: 200px;
}

/* Help Modal Styles */
.help-modal {
  max-width: 600px;
}

.shortcuts-grid {
  display: grid;
  gap: 2rem;
}

.shortcut-section h4 {
  margin: 0 0 1rem;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.shortcut-item:last-child {
  border-bottom: none;
}

kbd {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.75rem;
  color: var(--text-primary);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-right: 0.25rem;
}

.tip-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

.shortcut-item span:last-child {
  color: var(--text-secondary);
  flex: 1;
}

/* Performance Optimizations */
.table-container {
  contain: layout style paint;
  will-change: transform;
}

.custom-table {
  contain: layout style;
}

.table-row {
  contain: layout style;
}

/* Virtual Scrolling Support */
.table-body {
  height: 400px;
  overflow-y: auto;
  contain: strict;
}

/* Smooth Animations */
.filters-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.level-badge {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus Management */
.search-input:focus,
.date-input:focus,
.custom-select select:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .header-content {
    max-width: 1400px;
    padding: 1.5rem 3rem;
  }

  .dashboard-main {
    max-width: 1400px;
    padding: 2rem 3rem;
  }

  .filters-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop (969px - 1199px) */
@media (max-width: 1199px) and (min-width: 969px) {
  .header-content {
    padding: 1.5rem 2rem;
  }

  .dashboard-main {
    padding: 2rem;
  }

  .filters-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .filter-group:nth-child(4),
  .filter-group:nth-child(5),
  .filter-group:nth-child(6) {
    grid-column: span 1;
  }
}

/* Tablet (769px - 968px) */
@media (max-width: 968px) and (min-width: 769px) {
  .header-content {
    padding: 1rem 1.5rem;
    gap: 1rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-subtitle {
    font-size: 0.875rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .dashboard-main {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .filter-group:last-child {
    grid-column: span 2;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

/* Mobile (481px - 768px) */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1rem;
  }

  .title-section {
    flex: none;
  }

  .dashboard-title {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }

  .dashboard-subtitle {
    font-size: 0.8rem;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 80px;
  }

  .dashboard-main {
    padding: 1rem;
    gap: 1rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .custom-table {
    min-width: 800px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .pagination {
    justify-content: center;
  }

  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    max-width: calc(100vw - 2rem);
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .header-content {
    padding: 0.75rem;
  }

  .dashboard-title {
    font-size: 1.125rem;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
    min-width: 70px;
  }

  .btn .icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .dashboard-main {
    padding: 0.75rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .custom-select select {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .date-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .page-btn {
    width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }

  .modal-container {
    margin: 0.5rem;
    max-height: calc(100vh - 1rem);
    max-width: calc(100vw - 1rem);
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-content {
    padding: 1rem;
  }

  .modal-footer {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .modal-footer .btn {
    width: 100%;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn:hover {
    transform: none;
  }

  .table-row:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.98);
  }

  .table-row:active {
    background: var(--bg-hover);
  }

  /* Larger touch targets */
  .btn {
    min-height: 44px;
  }

  .page-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .clear-search,
  .modal-close {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Print Styles */
@media print {
  .dashboard-header,
  .filters-section,
  .modal-overlay {
    display: none !important;
  }
  
  .table-container {
    overflow: visible !important;
  }
  
  .custom-table {
    break-inside: avoid;
  }
  
  .table-row {
    break-inside: avoid;
  }
}
</style>
