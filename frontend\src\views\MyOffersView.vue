// filepath: c:\Code\MUNygo\frontend\src\views\MyOffersView.vue
<template>
  <div class="my-offers-container">
    <n-page-header :title="t('offers.myOffers')" />
    
    <!-- Main offers section - responsive -->
    <div class="offers-section">
      <!-- Desktop table view -->
      <div class="desktop-only">
        <n-data-table :columns="columns" :data="myOffersDisplayData" :loading="isLoadingOffers" />
      </div>
      
      <!-- Mobile card view -->
      <div class="mobile-only">
        <div v-if="isLoadingOffers" class="mobile-loading">
          <n-spin size="large" />
        </div>
        <div v-else-if="myOffersDisplayData.length === 0" class="mobile-empty">
          <n-empty :description="t('offers.noOffersCreated')" />
        </div>
        <div v-else class="mobile-offers-grid">
          <n-card 
            v-for="offer in myOffersDisplayData" 
            :key="offer.id" 
            class="mobile-offer-card"
            :title="`${offer.type === 'BUY' ? t('app.buy') : t('app.sell')} ${formatAmount(offer.amount, 'CAD')}`"
            hoverable
          >            <template #header-extra>
              <n-tag 
                :type="getOfferOverallStatus(offer, t)?.type || 'default'"
                size="small"
                round
              >
                {{ getOfferOverallStatus(offer, t)?.text || t('status.active') }}
              </n-tag>
            </template>
            
            <div class="mobile-offer-content">              <div class="offer-detail">
                <span class="label">{{ t('offers.baseRate') }}:</span>
                <span class="value">{{ formatAmountForDisplay(offer.baseRate, 'IRR', true) }}</span>
              </div>
              
              <div v-if="userTier !== 1 && offer.adjustmentForLowerRep != null" class="offer-detail">
                <span class="label">{{ getLowerTierLabel() }} {{ t('offers.adjustment') }}:</span>
                <span class="value">{{ offer.adjustmentForLowerRep.toFixed(2) }}%</span>
              </div>
              
              <div v-if="userTier !== 5 && offer.adjustmentForHigherRep != null" class="offer-detail">
                <span class="label">{{ getHigherTierLabel() }} {{ t('offers.adjustment') }}:</span>
                <span class="value">{{ offer.adjustmentForHigherRep.toFixed(2) }}%</span>
              </div>
              
              <div class="offer-detail">
                <span class="label">{{ t('offers.created') }}:</span>
                <span class="value">{{ formatDate(offer.createdAt) }}</span>
              </div>
            </div>
            
            <template #action>
              <n-space justify="space-between" align="center">
                <n-switch
                  :value="offer.status === 'ACTIVE'"
                  :loading="!!offer._statusLoading.value"
                  :disabled="!!offer._statusLoading.value"
                  @update:value="(checked) => handleStatusToggle(offer, checked)"
                >                  <template #checked>{{ t('status.active') }}</template>
                  <template #unchecked>{{ t('offers.statusInactive') }}</template>
                </n-switch>
                
                <n-button 
                  size="small" 
                  type="primary"
                  @click="router.push({ name: 'EditOffer', params: { offerId: offer.id } })"
                >
                  {{ t('offers.editOffer') }}
                </n-button>
              </n-space>
            </template>
          </n-card>
        </div>
      </div>
    </div>    
    <!-- Interest requests section -->
    <div v-if="hasInterestsToShow" class="interest-requests-container">
      <n-divider>{{ t('offers.interestRequests') }}</n-divider>
      <div class="interests-responsive-grid">
        <div v-for="offer in offersWithInterestsToDisplay" :key="offer.id" class="offer-interest-block">
          <n-card :title="`${t('offers.offer')}: ${offer.type === 'BUY' ? t('app.buy') : t('app.sell')} ${offer.amount} CAD ${t('offers.atRate')} ${offer.baseRate}`" class="offer-card">
            <p class="offer-timestamp">{{ t('offers.created') }}: {{ formatDate(offer.createdAt) }}</p>
            
            <div v-if="offer.interests && offer.interests.length > 0" class="interest-requests-list">
              <n-list bordered>
                <template #header>
                  <n-h4 style="margin: 0;">{{ t('offers.interestsReceived') }}</n-h4>
                </template>
                <n-list-item v-for="interest in offer.interests" :key="interest.id">
                  <div class="interest-item-content">
                    <div class="interest-header">
                      <div class="user-info">
                        <span class="username">{{ interest.username || 'N/A' }}</span>
                        <n-tag size="small" type="info">
                          {{ t('offers.reputation') }}: {{ interest.reputationLevel || 'N/A' }}
                        </n-tag>
                      </div>
                      
                      <div class="status-info">
                        <n-tag 
                          v-if="getInterestStatusInfo(interest)"
                          :type="getInterestStatusInfo(interest)!.type"
                          :style="{ 
                            animation: getInterestStatusInfo(interest)!.text === 'Negotiating' || getInterestStatusInfo(interest)!.text === 'In Progress' ? 'pulse 2s infinite' : 'none'
                          }"
                        >
                          <template #icon v-if="getInterestStatusInfo(interest)!.icon">
                            {{ getInterestStatusInfo(interest)!.icon }}
                          </template>
                          {{ getInterestStatusInfo(interest)!.text }}
                        </n-tag>
                        <n-tag v-else :type="getInterestStatusTagType(interest.status)">{{ interest.status }}</n-tag>
                      </div>
                    </div>
                    
                    <div v-if="interest.status === 'DECLINED' && interest.reasonCode" class="decline-reason">
                      <span class="reason-label">{{ t('offers.declineReason') }}:</span>
                      <span class="reason-text">{{ interest.reasonCode }}</span>
                    </div>
                    
                    <div v-if="interest.status === 'ACCEPTED' && interest.chatSessionId" class="chat-info">
                      <span class="chat-id">{{ t('offers.chatId') }}: {{ interest.chatSessionId.substring(0, 8) }}...</span>
                      <n-button size="small" type="primary" @click="goToChat(interest.chatSessionId)" class="chat-button">
                        {{ t('offers.goToChat') }}
                      </n-button>
                    </div>
                    
                    <div v-if="interest.status === 'PENDING'" class="action-buttons">
                      <n-space>
                        <n-button 
                          type="success" 
                          size="small" 
                          @click="handleAcceptInterest(interest.id)"
                          :loading="processingInterests.has(interest.id)"
                          :disabled="processingInterests.has(interest.id)"
                        >
                          {{ t('offers.acceptInterest') }}
                        </n-button>
                        <n-button type="error" size="small" @click="handleDeclineInterest(interest.id)">
                          {{ t('offers.declineInterest') }}
                        </n-button>
                      </n-space>
                    </div>
                  </div>
                </n-list-item>
              </n-list>
            </div>
            <n-empty v-else :description="t('offers.noInterestsReceived')" />
          </n-card>        </div>
      </div>    </div>
    
    <!-- Empty state for interest requests only - when user has offers but no interests -->
    <n-empty v-else-if="!isLoadingOffers && myOffersDisplayData.length > 0" :description="t('offers.noInterestRequestsCurrently')" class="interest-requests-container" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, h, ref as vueRef } from 'vue';
import type { Ref, ComputedRef } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useMyOffersStore } from '@/stores/myOffersStore';
import { useTranslation } from '@/composables/useTranslation';
import type { MyOffer, InterestRequestFrontend } from '@/types/offer';
import { 
  NPageHeader, 
  NDataTable, 
  NButton, 
  NCard, 
  NDivider, 
  useMessage, 
  NSwitch,
  NList,
  NListItem,
  NSpace,
  NTag,
  NEmpty,
  NH4,
  NSpin
} from 'naive-ui';
import { formatAmount, formatAmountForDisplay } from '@/utils/currencyUtils';
import type { DataTableColumns } from 'naive-ui';
import { updateOfferStatus } from '@/services/offerStatusService';
import { getInterestDynamicStatus, getOfferOverallStatus } from '@/utils/statusHelpers';

const authStore = useAuthStore();
console.log('🔥 [MyOffersView] About to call useMyOffersStore()');
const myOffersStore = useMyOffersStore();
console.log('🔥 [MyOffersView] myOffersStore created:', myOffersStore);
const message = useMessage();
const router = useRouter();
const { t } = useTranslation();

// Shared utility function for status toggle
async function toggleOfferStatus(offer: OfferDisplayRow, checked: boolean) {
  offer._statusLoading.value = true;
  try {
    const newStatus = checked ? 'ACTIVE' : 'INACTIVE';
    await updateOfferStatus(offer.id, newStatus);
    
    // Reflect change in the store for global reactivity
    const offerInStore = myOffersStore.myOffers.find(o => o.id === offer.id);
    if (offerInStore) {
      offerInStore.status = newStatus;
    }
    message.success(t('offers.statusUpdated', { 
      status: newStatus === 'ACTIVE' ? t('offers.statusActive') : t('offers.statusInactive') 
    }));
    return true; // Success
  } catch (err: any) {
    message.error('Failed to update offer status.');
    // Revert UI if backend call failed and status was not updated
    const offerInStore = myOffersStore.myOffers.find(o => o.id === offer.id);
    if (offerInStore) {
      offer.status = offerInStore.status; // Revert to store's state
    }
    return false; // Failure
  } finally {
    offer._statusLoading.value = false;
  }
}

// Reactive properties from the store
const isLoadingOffers = computed(() => myOffersStore.loading);
const storeError = computed(() => myOffersStore.error);

// Define the type for rows in the data table, including the added _statusLoading
// Using Ref<boolean> for _statusLoading
type OfferDisplayRow = MyOffer & { _statusLoading: Ref<boolean> };

// Create a display-specific version of offers that includes the local _statusLoading ref
const myOffersDisplayData: ComputedRef<OfferDisplayRow[]> = computed(() =>
  myOffersStore.myOffers.map(offer => ({
    ...offer, 
    _statusLoading: vueRef(false) 
  }))
);

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}

const offersWithInterestsToDisplay: ComputedRef<OfferDisplayRow[]> = computed(() => 
  myOffersDisplayData.value.filter(offer => offer.interests && offer.interests.length > 0)
);

const hasInterestsToShow = computed(() => offersWithInterestsToDisplay.value.length > 0);

const userTier = computed(() => authStore.user?.reputationLevel ?? 3);

// Track which interests are being processed to prevent double-clicks
const processingInterests = vueRef<Set<string>>(new Set());

function getLowerTierLabel() {
  if (!userTier.value) return 'L1-L2';
  if (userTier.value === 1) return 'No Lower Tiers';
  if (userTier.value === 2) return 'L1';
  if (userTier.value === 3) return 'L1-L2';
  if (userTier.value === 4) return 'L1-L3';
  if (userTier.value === 5) return 'L1-L4';
  return 'Lower';
}
function getHigherTierLabel() {
  if (!userTier.value) return 'L4-L5';
  if (userTier.value === 5) return 'No Higher Tiers';
  if (userTier.value === 4) return 'L5';
  if (userTier.value === 3) return 'L4-L5';
  if (userTier.value === 2) return 'L3-L5';
  if (userTier.value === 1) return 'L2-L5';
  return 'Higher';
}

const columns = computed<DataTableColumns<OfferDisplayRow>>(() => {
  const cols: DataTableColumns<OfferDisplayRow> = [
    { 
      title: t('offers.type'), 
      key: 'type',
      render: (row) => row.type === 'BUY' ? t('app.buy') : t('app.sell')
    },
    { title: t('offers.amountCAD'), key: 'amount', render: (row) => formatAmount(row.amount, 'CAD') },
    { title: t('offers.baseRate'), key: 'baseRate', render: (row) => formatAmountForDisplay(row.baseRate, 'IRR', true) },
  ];

  if (userTier.value !== 1) {
    cols.push({
      title: `${getLowerTierLabel()} ${t('offers.adjustment')} (%)`,
      key: 'adjustmentForLowerRep',
      render(row) {
        return row.adjustmentForLowerRep?.toFixed(2) ?? '-';
      },
    });
  }

  if (userTier.value !== 5) {    cols.push({
      title: `${getHigherTierLabel()} ${t('offers.adjustment')} (%)`,
      key: 'adjustmentForHigherRep',
      render(row) {
        return row.adjustmentForHigherRep?.toFixed(2) ?? '-';
      },
    });  }
  cols.push({    
    title: t('offers.overallStatus'),
    key: 'overallStatus',
    render(row) {
      // Handle inactive status first
      if (row.status === 'INACTIVE') {
        return h(
          NTag,
          {
            type: 'default',
            size: 'small',
            round: true
          },
          {
            default: () => t('offers.statusInactive')
          }
        );
      }

      const statusInfo = getOfferOverallStatus(row, t);
      if (!statusInfo) {
        return h(
          NTag,
          {
            type: 'success',
            size: 'small',
            round: true
          },
          {
            default: () => t('status.active')
          }
        );
      }
      
      return h(
        NTag,
        {
          type: statusInfo.type,
          size: 'small',
          round: true,
          style: statusInfo.text === t('status.negotiating') || statusInfo.text === t('status.inProgress') ? 
            { animation: 'pulse 2s infinite' } : {}
        },
        {
          default: () => `${statusInfo.icon || ''} ${statusInfo.text}`.trim()
        }
      );
    },
  });  cols.push({
    title: t('offers.status'),
    key: 'status',
    render(row) {
      return h(
        NSwitch,
        {
          value: row.status === 'ACTIVE',
          checkedValue: true,
          uncheckedValue: false,
          loading: !!row._statusLoading.value,
          disabled: !!row._statusLoading.value,          onUpdateValue: async (checked: boolean) => {
            await toggleOfferStatus(row, checked);
          },
        },
        {
          default: () => (row.status === 'ACTIVE' ? t('offers.statusActive') : t('offers.statusInactive')),
        }
      );
    },
  });
  cols.push({
    title: t('offers.createdAt'),
    key: 'createdAt',
    render(row) {
      return formatDate(row.createdAt);
    },
  });  cols.push({
    title: t('offers.actions'),
    key: 'actions',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => router.push({ name: 'EditOffer', params: { offerId: row.id } }),
        },
        { default: () => t('offers.edit') }
      );
    },
  });
  return cols;
});

onMounted(async () => {
  if (!authStore.isAuthenticated || !authStore.user?.id) {
    message.error('You must be logged in to view your offers.');
    router.push({ name: 'Login' });
    return;
  }
  try {
    await myOffersStore.fetchMyOffers();
    if (storeError.value) {
      message.error(storeError.value);
    }
  } catch (error) {
    message.error('An unexpected error occurred while loading your offers.');
    console.error('Error fetching my offers on mount:', error);
  }
});

function getInterestStatusTagType(status: InterestRequestFrontend['status']): 'default' | 'success' | 'error' | 'warning' | 'info' {
  switch (status) {
    case 'PENDING': return 'warning';
    case 'ACCEPTED': return 'success';
    case 'DECLINED': return 'error';
    default: return 'default';
  }
}

// Get dynamic status for interest cards
function getInterestStatusInfo(interest: InterestRequestFrontend) {
  return getInterestDynamicStatus(
    interest.status,
    interest.transactionStatus,
    interest.negotiationStatus,
    t
  );
}

// Removed offerId from parameters as it's not used by the store method
async function handleAcceptInterest(interestId: string) {
  // Prevent double-clicks
  if (processingInterests.value.has(interestId)) {
    console.log(`[MyOffersView] Interest ${interestId} is already being processed, ignoring click`);
    return;
  }

  try {
    processingInterests.value.add(interestId);
    await myOffersStore.acceptInterest(interestId);
    // Naive UI success notification is handled by myOffersStore via socket event
  } catch (error: any) {
    console.error('[MyOffersView] Error accepting interest:', error);
    
    // Handle specific error cases
    if (error.response?.status === 409) {
      message.warning('This interest has already been processed. The page will refresh to show the current state.');
      // Refresh the offers to get the current state
      setTimeout(() => {
        myOffersStore.fetchMyOffers();
      }, 1000);
    } else {
      message.error(error.response?.data?.message || error.message || 'Failed to accept interest.');
    }
  } finally {
    processingInterests.value.delete(interestId);
  }
}

// --- Start: Decline Interest Modal Logic ---
// This logic is now handled by the DeclineInterestModal.vue component
// Modified handleDeclineInterest to use store
async function handleDeclineInterest(interestId: string) {
  const offer = myOffersDisplayData.value.find(o => o.interests.some(i => i.id === interestId));
  if (offer) {
    const interest = offer.interests.find(i => i.id === interestId);
    if (interest) {
      myOffersStore.openDeclineInterestModal(interest); // This opens the modal component
    } else {
      message.error('Interest not found for declining.');
    }
  } else {
    message.error('Offer not found for the interest to be declined.');
  }
}
// --- End: Decline Interest Modal Logic ---

// Mobile card status toggle handler
async function handleStatusToggle(offer: OfferDisplayRow, checked: boolean) {
  await toggleOfferStatus(offer, checked);
}

const goToChat = (chatSessionId?: string | null) => {
  if (chatSessionId) {
    router.push({ name: 'ChatSession', params: { chatSessionId: chatSessionId } });
  } else {
    message.error('Chat session ID not found.');
  }
};

</script>

<style scoped>
.my-offers-container {
  padding: 0 1rem;
}

/* Responsive visibility classes */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: block;
  }
  
  .my-offers-container {
    padding: 0 0.5rem;
  }
}

/* Mobile offers grid */
.mobile-offers-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

.mobile-offer-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-offer-card:hover {
  transform: translateY(-2px);
}

.mobile-offer-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.offer-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  border-bottom: 1px solid var(--n-border-color);
}

.offer-detail:last-child {
  border-bottom: none;
}

.offer-detail .label {
  font-weight: 500;
  color: var(--n-text-color-2);
  font-size: 0.9rem;
}

.offer-detail .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.mobile-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
}

.mobile-empty {
  padding: 2rem 0;
}

/* Interest requests responsive styles */
.interests-responsive-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .interests-responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

.interest-requests-container {
  margin-top: 2rem;
}

.offer-interest-block {
  margin-bottom: 1.5rem;
}

.offer-card {
  transition: background-color 0.3s ease;
}

[data-theme="light"] .offer-card {
  background-color: #f9f9f9;
}

[data-theme="dark"] .offer-card {
  background-color: #2a2a2a;
}

.offer-timestamp {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

[data-theme="light"] .offer-timestamp {
  color: #666;
}

[data-theme="dark"] .offer-timestamp {
  color: #999;
}

.interest-requests-list {
  margin-top: 1rem;
}

.n-list-item {
  padding: 12px 0;
}

/* Enhanced interest item styling */
.interest-item-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.interest-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .interest-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.username {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.status-info {
  display: flex;
  align-items: center;
}

.decline-reason {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
  background-color: var(--n-color-error-suppl);
  border-radius: 6px;
  border-left: 3px solid var(--n-color-error);
}

@media (min-width: 640px) {
  .decline-reason {
    flex-direction: row;
    gap: 0.5rem;
  }
}

.reason-label {
  font-weight: 500;
  color: var(--n-text-color-2);
  font-size: 0.9rem;
}

.reason-text {
  font-weight: 600;
  color: var(--n-color-error);
}

.chat-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: var(--n-color-success-suppl);
  border-radius: 6px;
  border-left: 3px solid var(--n-color-success);
}

@media (min-width: 640px) {
  .chat-info {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.chat-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  color: var(--n-text-color-2);
}

.chat-button {
  align-self: flex-start;
}

@media (min-width: 640px) {
  .chat-button {
    align-self: auto;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

@media (max-width: 480px) {
  .action-buttons .n-space {
    width: 100%;
    justify-content: stretch;
  }
  
  .action-buttons .n-button {
    flex: 1;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
