import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Validates if a username meets the requirements
 */
export function isValidUsername(username: string): boolean {
  const MIN_LENGTH = 3;
  const MAX_LENGTH = 20;
  // Allow letters, numbers, underscores, and hyphens
  const VALID_PATTERN = /^[a-zA-Z0-9_-]+$/;
  
  return (
    username.length >= MIN_LENGTH &&
    username.length <= MAX_LENGTH &&
    VALID_PATTERN.test(username) &&
    !username.startsWith('_') &&
    !username.startsWith('-') &&
    !username.endsWith('_') &&
    !username.endsWith('-')
  );
}

/**
 * Checks if a username is available (not taken)
 */
export async function isUsernameAvailable(username: string): Promise<boolean> {
  if (!username || typeof username !== 'string') {
    throw new Error('Username must be a non-empty string');
  }
  
  try {
    // Simple equality check for better SQLite compatibility
    const existingUser = await prisma.user.findFirst({
      where: { 
        username: username
      },
    });
    return !existingUser;
  } catch (error) {
    console.error('Error checking username availability:', error);
    throw new Error('Failed to check username availability');
  }
}

/**
 * Generates a unique username from email
 */
export async function generateUniqueUsername(email: string): Promise<string> {
  const baseUsername = email.split('@')[0].toLowerCase();
  let username = baseUsername;
  let counter = 1;

  // First, clean the base username to meet our validation rules
  username = username.replace(/[^a-zA-Z0-9_-]/g, ''); // Remove invalid chars
  username = username.replace(/^[_-]+|[_-]+$/g, ''); // Remove leading/trailing _ or -
  
  // Ensure minimum length
  if (username.length < 3) {
    username = `user${username}`;
  }
  
  // Ensure maximum length
  if (username.length > 20) {
    username = username.substring(0, 20);
  }

  // Keep trying until we find a unique username
  while (true) {
    if (isValidUsername(username) && await isUsernameAvailable(username)) {
      return username;
    }

    // If username exists or invalid, append a number and try again
    const baseForCounter = baseUsername.length > 17 ? baseUsername.substring(0, 17) : baseUsername;
    username = `${baseForCounter}${counter}`;
    counter++;
    
    // Safety check to prevent infinite loops
    if (counter > 9999) {
      username = `user${Date.now().toString().slice(-6)}`;
      break;
    }
  }

  return username;
}

/**
 * Sanitizes and validates a user-provided username
 */
export function sanitizeUsername(username: string): string {
  return username
    .toLowerCase()
    .trim()
    .replace(/[^a-zA-Z0-9_-]/g, '') // Remove invalid characters
    .replace(/^[_-]+|[_-]+$/g, '') // Remove leading/trailing _ or -
    .substring(0, 20); // Ensure max length
}

/**
 * Checks if multiple usernames are available in a single database query
 */
async function batchCheckUsernameAvailability(usernames: string[]): Promise<{ [username: string]: boolean }> {
  if (!usernames.length) {
    return {};
  }

  try {
    const existingUsers = await prisma.user.findMany({
      where: {
        username: {
          in: usernames
        }
      },
      select: {
        username: true
      }
    });

    const takenUsernames = new Set(existingUsers.map(user => user.username));
    const result: { [username: string]: boolean } = {};
    
    for (const username of usernames) {
      result[username] = !takenUsernames.has(username);
    }
    
    return result;
  } catch (error) {
    console.error('Error in batch username availability check:', error);
    // Return all usernames as unavailable on error to be safe
    const result: { [username: string]: boolean } = {};
    for (const username of usernames) {
      result[username] = false;
    }
    return result;
  }
}

/**
 * Suggests alternative usernames if the provided one is taken
 */
export async function suggestAlternativeUsernames(baseUsername: string): Promise<string[]> {
  try {
    const sanitized = sanitizeUsername(baseUsername);
    
    if (!sanitized) {
      console.warn('Username sanitization resulted in empty string:', baseUsername);
      return [];
    }

    // Generate all candidate usernames first
    const candidates: string[] = [];
    
    // Add numbered variations
    for (let i = 1; i <= 5; i++) {
      const suggestion = `${sanitized}${i}`;
      if (isValidUsername(suggestion)) {
        candidates.push(suggestion);
      }
    }
    
    // Add creative variations
    const variations = [
      `${sanitized}_user`,
      `${sanitized}_${new Date().getFullYear()}`,
      `the_${sanitized}`,
      `${sanitized}_official`
    ];
    
    for (const variation of variations) {
      if (isValidUsername(variation)) {
        candidates.push(variation);
      }
    }

    // Batch check availability for all candidates
    const availabilityMap = await batchCheckUsernameAvailability(candidates);
    
    // Filter available usernames and limit to 5
    const suggestions: string[] = [];
    for (const candidate of candidates) {
      if (availabilityMap[candidate] && suggestions.length < 5) {
        suggestions.push(candidate);
      }
    }
    
    return suggestions;
  } catch (error) {
    console.error('Error in suggestAlternativeUsernames:', error);
    // Return empty array on complete failure
    return [];
  }
}
