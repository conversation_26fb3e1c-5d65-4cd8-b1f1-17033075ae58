import { Hono } from 'hono';
import { Server } from 'socket.io';
import { zValidator } from '@hono/zod-validator';
import { 
  AgreeToTermsSchema,
  DesignateFirstPayerSchema,
  DeclarePaymentSchema,
  ConfirmReceiptSchema,
  CancelTransactionSchema,
  DisputeTransactionSchema,
  getTransactionByChatSessionId,
} from '../services/transactionService';
import { authMiddleware, AuthVariables } from '../middleware/auth'; 
import { TransactionService } from '../services/transactionService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';
import createPayerNegotiationRoutes from './payerNegotiationRoutes';
import { PayerNegotiationService } from '../services/payerNegotiationService';

// Variables available to handlers in this route module, after all middleware in this file has run.
// It expects 'io' and 'notificationService' from the app's context.
// authMiddleware adds AuthVariables (jwtPayload).
// The local middleware adds transactionService.
type TransactionRoutesVariables = AuthVariables & {
  transactionService: TransactionService;
  io: Server; // Expected from app context
  notificationService: NotificationService; // Expected from app context
  chatService: ChatService; // Expected from app context
};

export default function createTransactionRoutes(payerNegotiationService: PayerNegotiationService) {
  const transactionRoutes = new Hono<{ Variables: TransactionRoutesVariables }>();
  // Middleware to inject TransactionService instance
  transactionRoutes.use('*', authMiddleware, async (c, next) => {
    const ioFromContext = c.get('io'); 
    const notificationServiceFromContext = c.get('notificationService'); 
    const chatServiceFromContext = c.get('chatService');

    if (!ioFromContext) {
      console.error('[TransactionRoutes Middleware] Socket.IO instance (io) not found in Hono context. This should have been set by global app middleware.');
      return c.json({ error: 'Server configuration error: Socket.IO not available.' }, 500);
    }
    if (!notificationServiceFromContext) {
      console.error('[TransactionRoutes Middleware] NotificationService instance not found in Hono context. This should have been set by global app middleware.');
      return c.json({ error: 'Server configuration error: NotificationService not available.' }, 500);
    }
    if (!chatServiceFromContext) {
      console.error('[TransactionRoutes Middleware] ChatService instance not found in Hono context. This should have been set by global app middleware.');
      return c.json({ error: 'Server configuration error: ChatService not available.' }, 500);
    }
    
    const transactionServiceInstance = new TransactionService(ioFromContext, notificationServiceFromContext, chatServiceFromContext); 
    c.set('transactionService', transactionServiceInstance);
    await next();
  });

  // GET current transaction details by Transaction ID
  transactionRoutes.get('/:id', async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');

    try {
      const transaction = await transactionService.getTransactionById(id, userId); 
      if (!transaction) {
        return c.json({ error: 'Transaction not found or user not authorized.' }, 404);
      }
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error fetching transaction by id ${id}:`, error);
      return c.json({ error: 'Failed to retrieve transaction details.' }, 500);
    }
  });

  // NEW ROUTE: GET current transaction details by Chat Session ID
  transactionRoutes.get('/chat/:chatSessionId', async (c) => {
    const { chatSessionId } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload

    try {
      // Call the imported standalone function directly
      const transaction = await getTransactionByChatSessionId(chatSessionId, userId); 
      if (!transaction) { 
        return c.json({ error: 'Transaction not found for this chat session or user not authorized.' }, 404);
      }
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error fetching transaction by chatSessionId ${chatSessionId}:`, error);
      // Ensure the original error message from service layer is not lost if it's specific
      const message = error.message || 'An unexpected error occurred while retrieving transaction details.';
      const statusCode = error.statusCode || 500; // If your errors have a statusCode property
      return c.json({ error: message }, statusCode);
    }
  });

  // POST agree to terms
  transactionRoutes.post('/:id/agree-terms', zValidator('json', AgreeToTermsSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');

    try {
      const transaction = await transactionService.agreeToTerms(id, userId); 
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/agree-terms for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to agree to terms.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('not in the correct state') || error.message?.includes('already agreed') ? 400 : 500));
    }
  });

  // POST designate first payer
  transactionRoutes.post('/:id/designate-payer', zValidator('json', DesignateFirstPayerSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');
    const { designatedPayerId } = c.req.valid('json');

    try {
      const transaction = await transactionService.designateFirstPayer(id, userId, designatedPayerId);
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/designate-payer for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to designate first payer.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('not in the correct state') || error.message?.includes('must agree to terms') || error.message?.includes('already been designated') ? 400 : 500));
    }
  });

  // POST declare payment
  transactionRoutes.post('/:id/declare-payment', zValidator('json', DeclarePaymentSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');
    const { trackingNumber } = c.req.valid('json');

    try {
      const transaction = await transactionService.declarePayment(id, userId, trackingNumber);
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/declare-payment for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to declare payment.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('Invalid transaction state') ? 400 : 500));
    }
  });

  // POST confirm receipt of payment
  transactionRoutes.post('/:id/confirm-receipt', zValidator('json', ConfirmReceiptSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');

    try {
      const transaction = await transactionService.confirmReceipt(id, userId);
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/confirm-receipt for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to confirm receipt.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('Invalid transaction state') || error.message?.includes('not declared payment yet') ? 400 : 500));
    }
  });

  // POST cancel transaction
  transactionRoutes.post('/:id/cancel', zValidator('json', CancelTransactionSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');
    const { reason } = c.req.valid('json');
    try {
      const transaction = await transactionService.cancelTransaction(id, userId, reason);
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/cancel for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to cancel transaction.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('terminal state') ? 400 : 500));
    }
  });

  // POST dispute transaction
  transactionRoutes.post('/:id/dispute', zValidator('json', DisputeTransactionSchema), async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload'); // Use jwtPayload
    const transactionService = c.get('transactionService');
    const { reason } = c.req.valid('json');
    try {
      const transaction = await transactionService.disputeTransaction(id, userId, reason);
      return c.json(transaction);
    } catch (error: any) {
      console.error(`[TransactionRoutes] Error in /:id/dispute for transaction ${id}:`, error);
      return c.json({ error: error.message || 'Failed to dispute transaction.' }, error.message?.includes('not found') ? 404 : (error.message?.includes('terminal state') ? 400 : 500));
    }
  });

  // Mount payer negotiation routes with the injected service
  transactionRoutes.route('/:transactionId', createPayerNegotiationRoutes(payerNegotiationService));

  return transactionRoutes;
}
