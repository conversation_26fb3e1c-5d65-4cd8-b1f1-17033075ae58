<template>
  <NModal 
    v-model:show="modalVisible" 
    preset="card"
    style="width: 90%; max-width: 1000px"
    title="Debug Report Details"
    size="huge"
    :bordered="false"
    :segmented="true"
  >
    <template #header-extra>
      <NSpace>
        <NButton 
          v-if="report"
          type="primary" 
          size="small"
          @click="$emit('export', report.reportId)"
        >
          <template #icon>
            <NIcon><DownloadOutline /></NIcon>
          </template>
          Download JSON
        </NButton>
        <NButton 
          v-if="report"
          type="default" 
          size="small"
          @click="copyReportId"
        >
          <template #icon>
            <NIcon><CopyOutline /></NIcon>
          </template>
          Copy ID
        </NButton>
      </NSpace>
    </template>

    <NSpin :show="loading">
      <div v-if="report" class="report-details">
        <!-- Basic Information -->
        <NCard title="Basic Information" class="mb-4">
          <NDescriptions :column="2" label-placement="left" bordered>
            <NDescriptionsItem label="Report ID">
              <NText code>{{ report.reportId }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Session ID">
              <NText code>{{ report.sessionId }}</NText>
            </NDescriptionsItem>            <NDescriptionsItem label="Server Received At">
              {{ formatDate(report.serverReceivedAt) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="Client Timestamp">
              {{ formatDate(report.clientTimestamp) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="Current URL">
              <NText v-if="report.currentUrl" type="success">
                {{ report.currentUrl }}
              </NText>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="User Agent">
              <NTooltip v-if="report.userAgent" trigger="hover">
                <template #trigger>
                  <NText>{{ getUserAgentSummary(report.userAgent) }}</NText>
                </template>
                <div style="max-width: 400px; word-break: break-all;">
                  {{ report.userAgent }}
                </div>
              </NTooltip>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- Report Details -->
        <NCard title="Report Details" class="mb-4">
          <NDescriptions :column="1" label-placement="top" bordered>
            <NDescriptionsItem label="Report Type">
              <NTag v-if="report.reportType" :type="getTypeTagType(report.reportType)">
                {{ getReportTypeLabel(report.reportType) }}
              </NTag>
              <NText v-else depth="3">Not specified</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Severity">
              <NTag v-if="report.reportSeverity" :type="getSeverityTagType(report.reportSeverity)">
                {{ getSeverityLabel(report.reportSeverity) }}
              </NTag>
              <NText v-else depth="3">Not specified</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Title">
              <NText v-if="report.reportTitle">{{ report.reportTitle }}</NText>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Description">
              <div v-if="report.reportDescription" class="formatted-text">
                {{ report.reportDescription }}
              </div>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Steps to Reproduce">
              <div v-if="report.stepsToReproduce" class="formatted-text">
                {{ report.stepsToReproduce }}
              </div>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Expected Behavior">
              <div v-if="report.expectedBehavior" class="formatted-text">
                {{ report.expectedBehavior }}
              </div>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Actual Behavior">
              <div v-if="report.actualBehavior" class="formatted-text">
                {{ report.actualBehavior }}
              </div>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="User Notes">
              <div v-if="report.userNotes" class="formatted-text">
                {{ report.userNotes }}
              </div>
              <NText v-else depth="3">None</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="Tags" v-if="report.hasTags && report.tags?.length">
              <NSpace>
                <NTag v-for="tag in report.tags" :key="tag" type="info" size="small">
                  {{ tag }}
                </NTag>
              </NSpace>
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- Client Logs -->
        <NCard title="Client Logs" class="mb-4">
          <template #header-extra>
            <NSpace>
              <NText depth="3">Total {{ report.logCount }} logs</NText>
              <NSelect 
                v-model:value="selectedLogLevel"
                :options="logLevelFilterOptions"
                size="small"
                style="width: 120px"
                placeholder="Filter level"
                clearable
              />
            </NSpace>
          </template>          <div v-if="filteredLogs.length === 0" class="empty-logs">
            <NText depth="3">No logs to display</NText>
          </div>

          <NCollapse v-else>
            <NCollapseItem 
              v-for="(log, index) in filteredLogs" 
              :key="index"
              :title="getLogTitle(log, index)"
              :name="index"
            >
              <template #header-extra>
                <NTag 
                  :type="getLogLevelTagType(log.level)" 
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ log.level }}
                </NTag>
              </template>

              <div class="log-details">
                <NDescriptions :column="1" size="small" label-placement="left">
                  <NDescriptionsItem label="Message">
                    <NText>{{ log.message }}</NText>
                  </NDescriptionsItem>
                  <NDescriptionsItem label="Timestamp">
                    {{ formatDate(log.timestamp) }}
                  </NDescriptionsItem>
                  <NDescriptionsItem label="URL" v-if="log.url">
                    <NText type="info">{{ log.url }}</NText>
                  </NDescriptionsItem>
                  <NDescriptionsItem label="Stack Trace" v-if="log.stackTrace">
                    <NCode 
                      :code="log.stackTrace" 
                      language="javascript" 
                      show-line-numbers
                      style="max-height: 200px; overflow-y: auto;"
                    />
                  </NDescriptionsItem>
                  <NDescriptionsItem label="Additional Context" v-if="log.context && Object.keys(log.context).length">
                    <NCode 
                      :code="JSON.stringify(log.context, null, 2)" 
                      language="json"
                      style="max-height: 150px; overflow-y: auto;"
                    />
                  </NDescriptionsItem>
                </NDescriptions>
              </div>
            </NCollapseItem>
          </NCollapse>
        </NCard>

        <!-- Diagnostic Data Section -->
        <DiagnosticDataCard :diagnostic-data="report.diagnosticData" />

        <!-- Raw JSON (for debugging) -->
        <NCard title="Raw JSON" v-if="showRawJson">
          <NCode 
            :code="JSON.stringify(report, null, 2)" 
            language="json"
            show-line-numbers
            style="max-height: 400px; overflow-y: auto;"
          />
        </NCard>        <!-- Debug Toggle -->
        <div class="debug-toggle mt-4">
          <NCheckbox v-model:checked="showRawJson">
            Show Raw JSON (for debugging)
          </NCheckbox>
        </div>
      </div>

      <div v-else-if="!loading" class="no-report">
        <NResult 
          status="warning"
          title="Report Not Found"
          description="Unable to load report details"
        />
      </div>
    </NSpin>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  NModal,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NText,
  NTag,
  NSpace,
  NButton,
  NIcon,
  NTooltip,
  NCollapse,
  NCollapseItem,
  NCode,
  NResult,
  NSelect,
  NCheckbox,
  NSpin,
  useMessage
} from 'naive-ui';
import { DownloadOutline, CopyOutline } from '@vicons/ionicons5';
import { format } from 'date-fns';
import type { ParsedReport, LogEntry } from '../../types/admin';
import {
  REPORT_TYPES,
  REPORT_SEVERITIES,
  LOG_LEVELS,
  SEVERITY_COLORS,
  LOG_LEVEL_COLORS
} from '../../types/admin';
import DiagnosticDataCard from './DiagnosticDataCard.vue';

// Props and emits
interface Props {
  show: boolean;
  report: ParsedReport | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  'update:show': [value: boolean];
  'export': [reportId: string];
}>();

// Local state
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const showRawJson = ref(false);
const selectedLogLevel = ref<string | null>(null);
const message = useMessage();

// Computed properties
const logLevelFilterOptions = computed(() => [
  ...Object.entries(LOG_LEVELS).map(([value, label]) => ({
    label,
    value
  }))
]);

const filteredLogs = computed(() => {
  if (!props.report?.logs) return [];
  
  if (!selectedLogLevel.value) {
    return props.report.logs;
  }
  
  return props.report.logs.filter(log => log.level === selectedLogLevel.value);
});

// Helper functions
function formatDate(dateString: string): string {
  try {
    return format(new Date(dateString), 'yyyy/MM/dd HH:mm:ss');
  } catch {
    return dateString;
  }
}

function getUserAgentSummary(userAgent: string): string {
  // Extract browser and OS info from user agent
  const browserMatch = userAgent.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/);
  const osMatch = userAgent.match(/(Windows|Mac|Linux|Android|iOS)/);
  
  const browser = browserMatch ? browserMatch[1] : 'Unknown';
  const os = osMatch ? osMatch[1] : 'Unknown';
  
  return `${browser} on ${os}`;
}

function getReportTypeLabel(type: string): string {
  return REPORT_TYPES[type as keyof typeof REPORT_TYPES] || type;
}

function getSeverityLabel(severity: string): string {
  return REPORT_SEVERITIES[severity as keyof typeof REPORT_SEVERITIES] || severity;
}

function getTypeTagType(type: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
  switch (type) {
    case 'bug': return 'error';
    case 'feature-request': return 'success';
    case 'performance': return 'warning';
    case 'ui-ux': return 'info';
    case 'improvement': return 'primary';
    default: return 'default';
  }
}

function getSeverityTagType(severity: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
  const colorType = SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS];
  return colorType || 'default';
}

function getLogLevelTagType(level: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
  const colorType = LOG_LEVEL_COLORS[level as keyof typeof LOG_LEVEL_COLORS];
  return colorType || 'default';
}

function getLogTitle(log: LogEntry, index: number): string {
  const time = formatDate(log.timestamp);
  const message = log.message.substring(0, 80) + (log.message.length > 80 ? '...' : '');
  return `#${index + 1} [${time}] ${message}`;
}

async function copyReportId() {
  if (!props.report) return;
  
  try {
    await navigator.clipboard.writeText(props.report.reportId);
    message.success('Report ID copied to clipboard');
  } catch {
    message.error('Failed to copy report ID');
  }
}

// Reset state when modal closes
watch(() => props.show, (newShow) => {
  if (!newShow) {
    showRawJson.value = false;
    selectedLogLevel.value = null;
  }
});
</script>

<style scoped>
.report-details {
  max-height: 80vh;
  overflow-y: auto;
}

.formatted-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: var(--n-text-color);
}

.log-details {
  padding: 12px 0;
}

.empty-logs {
  text-align: center;
  padding: 20px;
  color: var(--n-text-color-disabled);
}

.no-report {
  text-align: center;
  padding: 40px 20px;
}

.debug-toggle {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid var(--n-border-color);
}

/* Dark mode improvements */
:deep(.n-card) {
  background: var(--n-card-color);
  border-color: var(--n-border-color);
}

:deep(.n-descriptions) {
  --n-th-color: var(--n-card-color);
  --n-td-color: var(--n-card-color);
}

:deep(.n-code) {
  background: var(--n-code-color);
  border-color: var(--n-border-color);
}

:deep(.n-tag) {
  border: 1px solid var(--n-border-color);
}

/* Better collapse styling */
:deep(.n-collapse-item) {
  border-color: var(--n-border-color);
}

:deep(.n-collapse-item__header) {
  background: var(--n-card-color);
}

@media (max-width: 768px) {
  .report-details {
    max-height: 70vh;
  }
  
  :deep(.n-modal) {
    margin: 16px;
    width: calc(100% - 32px) !important;
  }
}
</style>
