<template>
  <div class="debug-test-view">
    <div class="container">
      <h1>Enhanced Debug Report Features Test Page</h1>
      
      <div class="intro">
        <p>This page allows you to test the new enhanced debug report features:</p>
        <ul>
          <li><strong>Offline Bug Report Storage</strong> - Reports are saved locally when offline</li>
          <li><strong>Auto-save Form Data</strong> - Form data is automatically saved as you type</li>
          <li><strong>Reset Form Button</strong> - Clear all form data with confirmation</li>
        </ul>
      </div>

      <!-- Test Component -->
      <EnhancedDebugReportTest />

      <!-- Instructions -->
      <div class="instructions">
        <h2>Testing Instructions</h2>
        
        <div class="instruction-section">
          <h3>1. Test Offline Functionality</h3>
          <ol>
            <li>Open browser developer tools (F12)</li>
            <li>Go to Network tab and check "Offline" to simulate offline mode</li>
            <li>Click "Add Test Offline Report" button above</li>
            <li>Open the Enhanced Debug Report Button and try to submit a report</li>
            <li>Notice the offline indicator and that reports are stored locally</li>
            <li>Uncheck "Offline" in developer tools to go back online</li>
            <li>Watch as offline reports are automatically processed</li>
          </ol>
        </div>

        <div class="instruction-section">
          <h3>2. Test Auto-save Functionality</h3>
          <ol>
            <li>Click the Enhanced Debug Report Button</li>
            <li>Start filling out the form (title, description, etc.)</li>
            <li>Notice the auto-save notification appears</li>
            <li>Close the modal without submitting</li>
            <li>Reopen the modal and see the draft restoration dialog</li>
            <li>Choose to restore the draft and see your data is preserved</li>
          </ol>
        </div>

        <div class="instruction-section">
          <h3>3. Test Reset Form Functionality</h3>
          <ol>
            <li>Open the Enhanced Debug Report Button</li>
            <li>Fill out some form fields</li>
            <li>Click the "Reset Form" button</li>
            <li>Confirm the reset in the dialog</li>
            <li>Notice all form fields are cleared and drafts are removed</li>
          </ol>
        </div>

        <div class="instruction-section">
          <h3>4. Test Integration</h3>
          <ol>
            <li>Try various combinations of the above features</li>
            <li>Check the statistics section to see stored data</li>
            <li>Use "Clear All Test Data" to reset everything</li>
            <li>Monitor the browser console for any errors</li>
          </ol>
        </div>
      </div>

      <!-- Browser Storage Info -->
      <div class="storage-info">
        <h2>Browser Storage Information</h2>
        <p>The enhanced features use localStorage to store data:</p>
        <ul>
          <li><code>munygo-offline-reports</code> - Stores offline reports</li>
          <li><code>munygo-form-drafts</code> - Stores form drafts</li>
        </ul>
        <p>You can inspect these in Developer Tools → Application → Local Storage</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EnhancedDebugReportTest from '@/components/EnhancedDebugReportTest.vue';
</script>

<style scoped>
.debug-test-view {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.5rem;
}

h2 {
  color: #444;
  margin: 30px 0 15px 0;
  font-size: 1.8rem;
}

h3 {
  color: #555;
  margin: 20px 0 10px 0;
  font-size: 1.3rem;
}

.intro {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.intro p {
  margin-bottom: 15px;
  font-size: 1.1rem;
  line-height: 1.6;
}

.intro ul {
  margin-left: 20px;
}

.intro li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.instructions {
  background: white;
  padding: 25px;
  border-radius: 8px;
  margin-top: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.instruction-section {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.instruction-section ol {
  margin-left: 20px;
  margin-top: 10px;
}

.instruction-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.storage-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-top: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.storage-info ul {
  margin-left: 20px;
}

.storage-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.storage-info code {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #d73a49;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .intro,
  .instructions,
  .storage-info {
    padding: 15px;
  }
  
  .instruction-section {
    padding: 12px;
  }
}
</style>
