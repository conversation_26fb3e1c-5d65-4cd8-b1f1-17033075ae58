import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { Hono } from 'hono';
import { PrismaClient, OfferStatus, OfferType } from '@prisma/client';
import offerRoutes from '../routes/offer';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();
const app = new Hono();
app.route('/offers', offerRoutes);

// Helper to create a test user and offer
async function createTestUserAndOffer() {
  const user = await prisma.user.create({
    data: {
      email: `testuser_${Date.now()}@example.com`,
      password: 'hashedpassword',
      emailVerified: true,
      phoneVerified: true,
      username: 'testuser',
    },
  });
  const offer = await prisma.offer.create({
    data: {
      userId: user.id,
      type: OfferType.BUY,
      amount: 100,
      baseRate: 50000,
      adjustmentForLowerRep: 0,
      adjustmentForHigherRep: 0,
      status: OfferStatus.ACTIVE,
    },
  });
  return { user, offer };
}

describe('PATCH /offers/:offerId/status', () => {
  let user: any;
  let offer: any;
  let token: string;

  beforeAll(async () => {
    ({ user, offer } = await createTestUserAndOffer());
    token = jwt.sign({ userId: user.id, email: user.email }, process.env.JWT_SECRET || 'your-default-secret-key', { expiresIn: '1h' });
  });

  afterAll(async () => {
    await prisma.offer.deleteMany({ where: { userId: user.id } });
    await prisma.user.delete({ where: { id: user.id } });
    await prisma.$disconnect();
  });

  it('should toggle offer status to INACTIVE', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.status).toBe('INACTIVE');
  });

  it('should toggle offer status back to ACTIVE', async () => {
    // Set to INACTIVE first
    await prisma.offer.update({ where: { id: offer.id }, data: { status: 'INACTIVE' } });
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'ACTIVE' }),
    });
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.status).toBe('ACTIVE');
  });

  it('should return 404 for non-existent offer', async () => {
    const res = await app.request(`/offers/nonexistentid/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    expect(res.status).toBe(404);
  });

  it('should return 403 if user does not own the offer', async () => {
    // Create another user and offer
    const otherUser = await prisma.user.create({
      data: {
        email: `otheruser_${Date.now()}@example.com`,
        password: 'hashedpassword',
        emailVerified: true,
        phoneVerified: true,
        username: 'otheruser',
      },
    });
    const otherOffer = await prisma.offer.create({
      data: {
        userId: otherUser.id,
        type: OfferType.BUY,
        amount: 100,
        baseRate: 50000,
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0,
        status: OfferStatus.ACTIVE,
      },
    });
    const res = await app.request(`/offers/${otherOffer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    expect(res.status).toBe(403);
    // Cleanup
    await prisma.offer.delete({ where: { id: otherOffer.id } });
    await prisma.user.delete({ where: { id: otherUser.id } });
  });

  it('should return 400 for invalid status value', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INVALID_STATUS' }),
    });
    expect(res.status).toBe(400);
  });
});
