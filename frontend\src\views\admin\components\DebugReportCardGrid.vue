<template>
  <div class="card-grid">
    <div
      v-for="report in reports"
      :key="report.reportId"
      class="report-card"
      @click="$emit('view-report', report)"
    >
      <div class="card-header">
        <span class="type-badge">{{ formatType(report.reportType) }}</span>
        <span 
          class="severity-badge" 
          :style="{ backgroundColor: getSeverityColor(report.reportSeverity) }"
        >
          {{ formatSeverity(report.reportSeverity) }}
        </span>
      </div>
      <div class="card-content">
        <p class="report-id">ID: {{ report.reportId?.substring(0, 12) }}...</p>
        <p class="user-info">User: {{ formatUser(report) }}</p>
        <p class="description">{{ getDescription(report) }}</p>
        <p class="timestamp">Received: {{ formatDate(report.serverReceivedAt) }}</p>
      </div>
      <div class="card-actions">
        <button @click.stop="$emit('view-report', report)" class="btn btn-sm btn-outline">
          View Details
        </button>
        <button 
          @click.stop="report.reportId && $emit('download-report', report.reportId)" 
          class="btn btn-sm btn-outline" 
          :disabled="!report.reportId"
        >
          Download
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns';

interface ParsedReport {
  reportId?: string;
  reportType?: string;
  reportSeverity?: string;
  serverReceivedAt?: string;
  reportDescription?: string;
  // User identification fields
  userId?: string;
  userEmail?: string;
  username?: string;
}

const SEVERITY_COLORS = {
  low: '#10b981',
  medium: '#f59e0b', 
  high: '#ef4444',
  critical: '#dc2626'
};

defineOptions({
  name: 'DebugReportCardGrid'
});

defineProps<{
  reports: ParsedReport[];
}>();

defineEmits<{
  'view-report': [report: ParsedReport];
  'download-report': [id: string];
}>();

function formatType(type?: string): string {
  if (!type) return 'N/A';
  return type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

function formatSeverity(severity?: string): string {
  if (!severity) return 'N/A';
  return severity.charAt(0).toUpperCase() + severity.slice(1);
}

function formatDate(dateString?: string): string {
  return dateString ? format(new Date(dateString), 'MMM dd, yyyy HH:mm') : 'N/A';
}

function getSeverityColor(severity?: string): string {
  return severity ? (SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666') : '#666';
}

function formatUser(report: ParsedReport): string {
  if (report.username) {
    return report.username;
  }
  if (report.userEmail) {
    return report.userEmail;
  }
  if (report.userId) {
    return `User ${report.userId.substring(0, 8)}...`;
  }
  return 'Anonymous';
}

function getDescription(report: ParsedReport): string {
  const desc = report.reportDescription;
  if (!desc) return 'No description available';
  return desc.length > 100 ? desc.substring(0, 100) + '...' : desc;
}
</script>

<style scoped>
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

.report-card {
  background-color: var(--bg-surface, #fff);
  border: 1px solid var(--border-base, #e0e0e0);
  border-radius: var(--radius-lg, 8px);
  padding: 1rem;
  box-shadow: var(--shadow-sm, 0 1px 3px rgba(0,0,0,0.1));
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.report-card:hover {
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0,0,0,0.1));
  transform: translateY(-2px);
  border-color: var(--primary-200, #bfdbfe);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.type-badge, 
.severity-badge {
  font-size: var(--font-size-xs, 0.75rem);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full, 9999px);
  font-weight: var(--font-weight-semibold, 600);
  white-space: nowrap;
}

.type-badge {
  background-color: var(--primary-100, #dbeafe);
  color: var(--primary-700, #1d4ed8);
}

.severity-badge {
  color: white;
}

.card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-content p {
  margin: 0.5rem 0;
  font-size: var(--font-size-sm, 0.875rem);
}

.report-id {
  font-family: var(--font-family-mono, monospace);
  color: var(--text-tertiary, #6c757d);
  font-size: var(--font-size-xs, 0.75rem);
}

.user-info {
  color: var(--text-secondary, #666);
  font-size: var(--font-size-xs, 0.75rem);
  font-weight: var(--font-weight-medium, 500);
}

.description {
  color: var(--text-secondary, #666);
  min-height: 40px;
  flex-grow: 1;
  line-height: 1.4;
}

.timestamp {
  color: var(--text-tertiary, #6c757d);
  font-size: var(--font-size-xs, 0.75rem);
  margin-top: auto;
}

.card-actions {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-base, #e0e0e0);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  transition: all 0.2s ease-in-out;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs, 0.75rem);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-500, #3b82f6);
  border-color: var(--primary-500, #3b82f6);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-50, #eff6ff);
}

.btn-outline:disabled {
  color: var(--text-quaternary, #94a3b8);
  border-color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
}
</style>