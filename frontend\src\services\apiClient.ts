import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import router from '@/router';
import { i18n } from '@/i18n';

// Create an Axios instance that includes the auth token
const apiClient = axios.create({
  baseURL: '/api', // Assuming your API base is /api
});

// Attach token to requests
apiClient.interceptors.request.use(config => {
  const authStore = useAuthStore();
  const token = authStore.token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, error => {
  return Promise.reject(error);
});

// Global response interceptor for 401 Invalid token
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (
      error.response &&
      error.response.status === 401 &&
      typeof error.response.data?.error === 'string' &&
      error.response.data.error.toLowerCase().includes('invalid token')
    ) {      const authStore = useAuthStore();
      authStore.logout();
      // Redirect to login with a friendly message
      const sessionExpiredMessage = `${(i18n.global as any).t('errors.sessionExpired')}. ${(i18n.global as any).t('errors.pleaseLoginAgain')}`;
      router.push({ name: 'login', query: { message: sessionExpiredMessage } });
    }
    return Promise.reject(error);
  }
);

export default apiClient;
