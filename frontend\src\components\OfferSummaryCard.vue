<script setup lang="ts">
import { computed, ref } from 'vue'
import { NCard, NText, NTag, NButton, NModal, NSpace } from 'naive-ui'
import type { BrowseOffer } from '@/types/offer'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { useLanguageStore } from '@/stores/language'
import { useTranslation } from '@/composables/useTranslation'
import { getInterestDynamicStatus } from '@/utils/statusHelpers'
import { formatAmountForDisplay } from '@/utils/currencyUtils'

const props = defineProps<{
  offer: BrowseOffer
}>()

const authStore = useAuthStore()
const router = useRouter()
const languageStore = useLanguageStore()
const { t } = useTranslation()

const isOwnOffer = computed(() => {
  return authStore.user?.id === props.offer.offerCreatorId
})

const rateDifference = computed(() => {
  if (props.offer.calculatedApplicableRate != null && props.offer.baseRate != null) {
    const applicableRate = Number(props.offer.calculatedApplicableRate)
    const baseRate = Number(props.offer.baseRate)
    if (!isNaN(applicableRate) && !isNaN(baseRate)) {
      return applicableRate - baseRate
    }
  }
  return 0
})

const isBonus = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value > 0;
  } else {
    return rateDifference.value < 0;
  }
});

const isPenalty = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value < 0;
  } else {
    return rateDifference.value > 0;
  }
});

const offerCreatorRepTagType = computed<'success' | 'warning' | 'info' | 'default'> (() => {
  const level = props.offer.offerCreatorReputationLevel;
  if (level === null || level === undefined) return 'default';
  if (level >= 4) return 'success'; // Trusted, Elite
  if (level <= 2) return 'warning'; // Newcomer, Verified
  return 'info'; // Reliable
});

const rateIndicatorType = computed<'success' | 'error' | 'default'>(() => {
  if (isOwnOffer.value) return 'default';
  if (isBonus.value) return 'success';
  if (isPenalty.value) return 'error';
  return 'default';
});

const dynamicStatus = computed(() => {
  // If user hasn't shown interest, no tag
  if (!props.offer.currentUserHasShownInterest) {
    return null;
  }
  // Use the centralized status logic
  const status = getInterestDynamicStatus(
    props.offer.currentUserInterestStatus || null,
    props.offer.transactionStatus,
    props.offer.negotiationStatus,
    t
  );
  return status;
});

const canShowInterest = computed(() => {
  return !isOwnOffer.value && !props.offer.currentUserHasShownInterest;
});

const canGoToChat = computed(() => {
  return props.offer.currentUserInterestStatus === 'ACCEPTED' && !!props.offer.chatSessionId;
});

const showConfirmModal = ref(false);

const emit = defineEmits<{
  showInterest: [offerId: string]
  click: [offer: BrowseOffer]
}>()

function showInterest(event: Event) {
  event.stopPropagation(); // Prevent event bubbling to card click
  showConfirmModal.value = true;
}

function handleCardClick() {
  emit('click', props.offer);
}

function confirmShowInterest() {
  showConfirmModal.value = false;
  emit('showInterest', props.offer.id);
}

function cancelShowInterest() {
  showConfirmModal.value = false;
}

function goToChat(event: Event) {
  event.stopPropagation(); // Prevent event bubbling to card click
  if (props.offer.chatSessionId) {
    router.push({ name: 'ChatSession', params: { chatSessionId: props.offer.chatSessionId } });
  } else {
    console.error('Attempted to go to chat without a chatSessionId on offer:', props.offer.id);
    // Optionally, show a user-facing error message via a message provider if available
  }
}

</script>

<template>  <NCard 
    class="offer-summary-card" 
    hoverable 
    :bordered="true"
    :content-style="{ padding: '10px', position: 'relative' }" 
    :header-style="{ padding: '8px 10px', paddingBottom: '4px' }"
    @click="handleCardClick"
  >
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center;">        <NText strong style="font-size: 0.95em; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; padding-right: 5px;">
          {{ offer.type === 'BUY' ? t('offers.buying') : t('offers.selling') }} {{ Number(offer.amount).toLocaleString() }} CAD
        </NText><div style="display: flex; align-items: center; gap: 5px;">          <!-- Dynamic status tag -->
          <NTag 
            v-if="dynamicStatus" 
            :type="dynamicStatus.type" 
            size="tiny"
            round
            class="status-tag"
            :style="{ 
              animation: dynamicStatus.text === 'Negotiating' || dynamicStatus.text === 'In Progress' ? 'pulse 2s infinite' : 'none'
            }"
          >
            <template #icon v-if="dynamicStatus.text === 'Complete'">
              ✅
            </template>
            <template #icon v-else-if="dynamicStatus.text === 'In Progress'">
              🔄
            </template>
            <template #icon v-else-if="dynamicStatus.text === 'Negotiating'">
              💬
            </template>
            {{ dynamicStatus.text }}
          </NTag>
          <!-- Go to Chat Button REMOVED from here -->
        </div>
      </div>
    </template>
    <div class="card-content">      <div class="rate-section">
        <NText :depth="2" style="font-size: 0.85em;">
          {{ t('offers.baseRate') }}: {{ formatAmountForDisplay(Number(offer.baseRate), 'IRR', true) }} IRR/CAD
        </NText>
        <NText :depth="2" style="font-size: 0.85em;">
          {{ t('offers.yourRate') }}: {{ formatAmountForDisplay(Number(offer.calculatedApplicableRate), 'IRR', true) }} IRR/CAD
          <NTag v-if="!isOwnOffer && (isBonus || isPenalty)" :type="rateIndicatorType" size="tiny" round style="margin-left: 4px;">
            {{ isBonus ? t('offers.bonus') : (isPenalty ? t('offers.penalty') : '') }}
          </NTag>
        </NText>
      </div><div class="creator-section">
        <NText :depth="3" style="font-size: 0.8em;">
          {{ t('offers.by') }}: {{ offer.offerCreatorUsername || t('offers.unknownUser') }}
          <NTag round size="tiny" :type="offerCreatorRepTagType" class="reputation-tag" style="margin-left: 4px;">
            {{ t('offers.level') }} {{ offer.offerCreatorReputationLevel ?? 'N/A' }}
          </NTag>
        </NText>
      </div></div>    <!-- Show Interest Button -->
    <NButton
      v-if="canShowInterest"
      type="primary"
      size="tiny"
      round
      @click="showInterest"
      class="show-interest-button"
      style="position: absolute; bottom: 10px; left: 10px;"
    >
      {{ t('offers.showInterest') }}
    </NButton>
    <!-- Go to Chat Button -->
    <NButton
      v-if="canGoToChat"
      type="primary"
      size="tiny"
      round
      @click="goToChat"
      class="go-to-chat-button"
      :style="{ 
        position: 'absolute',
        bottom: '10px',
        [languageStore.isRTL ? 'left' : 'right']: '10px'
      }"    >
      {{ t('offers.goToChat') }}
    </NButton>
  </NCard>
  <!-- Confirmation Modal for Show Interest -->
  <NModal v-model:show="showConfirmModal" preset="dialog">
    <template #header>
      <NText strong>{{ t('offers.showInterest') }}</NText>
    </template>    <NSpace vertical>
      <NText>
        {{ offer.type === 'BUY' ? t('offers.buying') : t('offers.selling') }} {{ Number(offer.amount).toLocaleString() }} CAD
      </NText>      <NText :depth="2" style="font-size: 0.9em;">
        {{ t('offers.yourRate') }}: {{ formatAmountForDisplay(Number(offer.calculatedApplicableRate), 'IRR', true) }} IRR/CAD
      </NText>
      <NText>{{ t('offers.confirmShowInterest') }}</NText>
    </NSpace>
    <template #action>
      <NSpace>
        <NButton @click="cancelShowInterest">{{ t('app.cancel') }}</NButton>
        <NButton type="primary" @click="confirmShowInterest">{{ t('app.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped>
.offer-summary-card {
  cursor: pointer;
  transition: box-shadow 0.3s ease-in-out;
  /* Ensure card has a positioning context for absolute children if not set in :content-style */
  /* position: relative; */ 
}

.offer-summary-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Reduced gap for a more compact look */
}

.rate-section, .creator-section {
  display: flex;
  flex-direction: column;
  gap: 2px; /* Reduced gap within sections */
}

/* .reputation-tag { */
  /* Styles for reputation-tag can be centralized here if needed */
  /* For example: vertical-align: middle; */
/* } */

.show-interest-button {
  position: absolute;
  bottom: 10px;
  left: 10px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Add any other specific styles */
</style>
