import { ref, computed, watch, readonly } from 'vue';
import { useConnectionStore } from '@/stores/connection';
import { useClientLogger } from '@/composables/useClientLogger';
import type {
  OfflineReport,
  OfflineReportStorage,
  ClientReportPayload,
  ClientReportResponse
} from '@/types/logging';
import { useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';

const STORAGE_KEY = 'munygo-offline-reports';
const MAX_OFFLINE_REPORTS = 50;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_MS = 5000; // 5 seconds
const CLEANUP_INTERVAL_DAYS = 7;

// Global state for offline reports
const offlineReports = ref<OfflineReport[]>([]);
const isProcessingQueue = ref(false);
const lastProcessedAt = ref<string | null>(null);
const isInitialized = ref(false);

/**
 * Composable for managing offline bug report storage and submission
 */
export function useOfflineReports() {
  const { t } = useI18n();
  const message = useMessage();
  const connectionStore = useConnectionStore();
  const logger = useClientLogger();

  // Computed properties
  const hasOfflineReports = computed(() => offlineReports.value.length > 0);
  const offlineReportCount = computed(() => offlineReports.value.length);
  const isOnline = computed(() => connectionStore.isConnected);

  /**
   * Load offline reports from localStorage
   */
  const loadOfflineReports = (): void => {
    if (isInitialized.value) return;

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const storage: OfflineReportStorage = JSON.parse(stored);
        offlineReports.value = storage.reports || [];

        // Clean up old reports
        cleanupOldReports();

        logger.logInfo('Offline reports loaded', {
          count: offlineReports.value.length,
          lastCleanup: storage.lastCleanup
        });
      }
      isInitialized.value = true;
    } catch (error) {
      console.error('Failed to load offline reports:', error);
      offlineReports.value = [];
      isInitialized.value = true;
    }
  };

  /**
   * Save offline reports to localStorage
   */
  const saveOfflineReports = (): void => {
    try {
      const storage: OfflineReportStorage = {
        reports: offlineReports.value,
        lastCleanup: new Date().toISOString()
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(storage));
    } catch (error) {
      console.error('Failed to save offline reports:', error);
      logger.logError('Failed to save offline reports to localStorage', error);
    }
  };

  /**
   * Add a report to offline storage
   */
  const addOfflineReport = (reportPayload: ClientReportPayload): string => {
    const reportId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const offlineReport: OfflineReport = {
      id: reportId,
      reportPayload,
      timestamp: new Date().toISOString(),
      retryCount: 0
    };

    offlineReports.value.unshift(offlineReport);
    
    // Limit the number of stored reports
    if (offlineReports.value.length > MAX_OFFLINE_REPORTS) {
      offlineReports.value = offlineReports.value.slice(0, MAX_OFFLINE_REPORTS);
    }
    
    saveOfflineReports();
    
    logger.logInfo('Report added to offline storage', {
      reportId,
      totalOfflineReports: offlineReports.value.length
    });

    return reportId;
  };

  /**
   * Remove a report from offline storage
   */
  const removeOfflineReport = (reportId: string): void => {
    const index = offlineReports.value.findIndex(report => report.id === reportId);
    if (index !== -1) {
      offlineReports.value.splice(index, 1);
      saveOfflineReports();
      
      logger.logInfo('Report removed from offline storage', {
        reportId,
        remainingReports: offlineReports.value.length
      });
    }
  };

  /**
   * Clean up old offline reports
   */
  const cleanupOldReports = (): void => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_INTERVAL_DAYS);
    
    const initialCount = offlineReports.value.length;
    offlineReports.value = offlineReports.value.filter(report => {
      const reportDate = new Date(report.timestamp);
      return reportDate > cutoffDate;
    });
    
    const removedCount = initialCount - offlineReports.value.length;
    if (removedCount > 0) {
      saveOfflineReports();
      logger.logInfo('Cleaned up old offline reports', {
        removedCount,
        remainingCount: offlineReports.value.length
      });
    }
  };

  /**
   * Attempt to submit a single offline report
   */
  const submitOfflineReport = async (report: OfflineReport): Promise<{ success: boolean; response?: ClientReportResponse }> => {
    try {
      const response = await logger.sendLogsToServer(report.reportPayload.reportDetails);

      if (response.success) {
        logger.logInfo('Offline report submitted successfully', {
          reportId: report.id,
          serverReportId: response.reportId
        });
        return { success: true, response };
      } else {
        logger.logWarn('Offline report submission failed', {
          reportId: report.id,
          error: response.message,
          isNetworkError: response.isNetworkError
        });
        return { success: false, response };
      }
    } catch (error) {
      logger.logError('Error submitting offline report', error, {
        reportId: report.id,
        retryCount: report.retryCount
      });
      return { success: false };
    }
  };

  /**
   * Process the offline reports queue
   */
  const processOfflineReports = async (): Promise<void> => {
    if (isProcessingQueue.value || !isOnline.value || offlineReports.value.length === 0) {
      return;
    }

    isProcessingQueue.value = true;
    lastProcessedAt.value = new Date().toISOString();
    
    logger.logInfo('Processing offline reports queue', {
      queueSize: offlineReports.value.length
    });

    const reportsToProcess = [...offlineReports.value];
    let successCount = 0;
    let failureCount = 0;

    for (const report of reportsToProcess) {
      if (!isOnline.value) {
        logger.logInfo('Connection lost during offline report processing');
        break;
      }

      const result = await submitOfflineReport(report);

      if (result.success) {
        removeOfflineReport(report.id);
        successCount++;
      } else {
        // Check if we should retry or give up
        const shouldRetry = result.response?.isNetworkError ?? true; // Default to retry if no response

        if (shouldRetry) {
          // Update retry count only for retryable errors (network issues)
          report.retryCount++;
          report.lastRetryAt = new Date().toISOString();

          // Remove if max retries exceeded
          if (report.retryCount >= MAX_RETRY_ATTEMPTS) {
            removeOfflineReport(report.id);
            failureCount++;
            logger.logWarn('Offline report exceeded max retries, removing', {
              reportId: report.id,
              retryCount: report.retryCount,
              lastError: result.response?.message || 'Unknown error'
            });
          } else {
            saveOfflineReports();
          }
        } else {
          // Non-retryable error (e.g., validation error) - remove immediately
          removeOfflineReport(report.id);
          failureCount++;
          logger.logWarn('Offline report removed due to non-retryable error', {
            reportId: report.id,
            error: result.response?.message || 'Unknown error'
          });
        }
      }

      // Small delay between submissions to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    isProcessingQueue.value = false;

    // Show user feedback
    if (successCount > 0) {
      message.success(t('debug.offlineReportsSubmitted', { count: successCount }));
    }
    
    if (failureCount > 0) {
      message.warning(t('debug.offlineReportsFailed', { count: failureCount }));
    }

    logger.logInfo('Offline reports processing completed', {
      successCount,
      failureCount,
      remainingReports: offlineReports.value.length
    });
  };

  /**
   * Clear all offline reports
   */
  const clearOfflineReports = (): void => {
    offlineReports.value = [];
    saveOfflineReports();
    logger.logInfo('All offline reports cleared');
  };

  /**
   * Get offline report statistics
   */
  const getOfflineReportStats = () => {
    return {
      total: offlineReports.value.length,
      byRetryCount: offlineReports.value.reduce((acc, report) => {
        acc[report.retryCount] = (acc[report.retryCount] || 0) + 1;
        return acc;
      }, {} as Record<number, number>),
      oldestReport: offlineReports.value.length > 0 
        ? offlineReports.value[offlineReports.value.length - 1].timestamp 
        : null,
      newestReport: offlineReports.value.length > 0 
        ? offlineReports.value[0].timestamp 
        : null
    };
  };

  // Watch for connection changes and process queue when online
  watch(isOnline, (newValue) => {
    if (newValue && hasOfflineReports.value) {
      logger.logInfo('Connection restored, processing offline reports');
      // Add a small delay to ensure connection is stable
      setTimeout(() => {
        processOfflineReports();
      }, RETRY_DELAY_MS);
    }
  });

  // Ensure initialization when accessing computed properties
  const ensureInitialized = () => {
    if (typeof window !== 'undefined' && !isInitialized.value) {
      loadOfflineReports();
    }
  };

  // Override computed properties to ensure initialization
  const hasOfflineReportsComputed = computed(() => {
    ensureInitialized();
    return offlineReports.value.length > 0;
  });

  const offlineReportCountComputed = computed(() => {
    ensureInitialized();
    return offlineReports.value.length;
  });

  return {
    // State
    offlineReports: readonly(offlineReports),
    hasOfflineReports: hasOfflineReportsComputed,
    offlineReportCount: offlineReportCountComputed,
    isProcessingQueue: readonly(isProcessingQueue),
    lastProcessedAt: readonly(lastProcessedAt),
    isOnline,

    // Methods
    addOfflineReport,
    removeOfflineReport,
    processOfflineReports,
    clearOfflineReports,
    getOfflineReportStats,
    cleanupOldReports
  };
}
