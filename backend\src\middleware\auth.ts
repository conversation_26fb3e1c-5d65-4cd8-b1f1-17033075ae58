import { createMiddleware } from 'hono/factory';
import { verify } from 'hono/jwt';
import { Context } from 'hono';

const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key';

// Define and export interfaces
export interface JwtPayload {
  userId: string;
  email: string;
  exp?: number;
  iat?: number;
   [key: string]: string | number | boolean | null | undefined;
}

// Define a type for the context variables set by the middleware
export interface AuthVariables {
  jwtPayload: JwtPayload;
}

export const authMiddleware = createMiddleware<{ Variables: AuthVariables }>(async (c, next) => {
  const authHeader = c.req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ error: 'Unauthorized: Missing or invalid token' }, 401);
  }

  const token = authHeader.substring(7); // Remove 'Bearer '

  try {
    const decodedPayload = await verify(token, JWT_SECRET);
    // Convert to our custom JwtPayload type with type checking
    const payload = decodedPayload as unknown as JwtPayload;
    
    // Check if payload contains required fields
    if (!payload.userId || !payload.email) {
        return c.json({ error: 'Unauthorized: Invalid token payload' }, 401);
    }
    
    // Check expiration if needed (verify might handle it depending on library version)
    if (payload.exp && payload.exp * 1000 < Date.now()) {
        return c.json({ error: 'Unauthorized: Token expired' }, 401);
    }
    c.set('jwtPayload', payload); // Store the payload in context for later use
    await next();
  } catch (error) {
    console.error('JWT verification error:', error);
    return c.json({ error: 'Unauthorized: Invalid token' }, 401);
  }
});
