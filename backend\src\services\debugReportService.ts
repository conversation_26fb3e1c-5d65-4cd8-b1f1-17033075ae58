import { PrismaClient, DebugReportType, DebugReportSeverity, DebugReportStatus } from '@prisma/client';
import type { ClientReportPayload } from '../types/schemas/debugSchemas';

/**
 * Interface for filtering and pagination options
 */
export interface GetReportsOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterByType?: string;
  filterBySeverity?: string;
  filterByStatus?: string;
  filterByAssignee?: string;
  filterByDateStart?: string;
  filterByDateEnd?: string;
  searchQuery?: string;
}

/**
 * Interface for paginated response
 */
export interface GetReportsResponse {
  reports: any[];
  total: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Service for handling debug reports with PostgreSQL database
 */
export class DebugReportService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Create a new debug report in the database
   */
  async createReport(reportData: ClientReportPayload, userId?: string): Promise<string> {
    const reportId = `DBG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const report = await this.prisma.debugReport.create({
        data: {
          reportId,
          userId,
          type: this.mapReportType(reportData.reportDetails.type),
          severity: this.mapSeverity(reportData.reportDetails.severity),
          title: reportData.reportDetails.title,
          description: reportData.reportDetails.description,
          stepsToReproduce: reportData.reportDetails.stepsToReproduce,
          expectedBehavior: reportData.reportDetails.expectedBehavior,
          actualBehavior: reportData.reportDetails.actualBehavior,
          additionalNotes: reportData.reportDetails.additionalNotes,
          sessionId: reportData.sessionId,
          currentUrl: reportData.reportDetails.userContext?.currentPage,
          userAgent: reportData.reportDetails.userContext?.userAgent,
          viewportWidth: reportData.reportDetails.userContext?.viewport?.width,
          viewportHeight: reportData.reportDetails.userContext?.viewport?.height,
          diagnosticData: reportData.diagnosticData as any,
          logs: reportData.logs as any,
          userActions: reportData.reportDetails.userContext?.userActions as any,
          clientTimestamp: new Date(reportData.timestamp),
          tags: {
            create: reportData.reportDetails.reportTags?.map(tag => ({ tag })) || []
          }
        },
        include: {
          tags: true,
          user: {
            select: { id: true, email: true, username: true }
          }
        }
      });

      console.log(`✅ [DebugReportService] Created report ${reportId} in database`);
      return report.reportId;
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to create report:', error);
      throw error;
    }
  }

  /**
   * Update report status with audit trail
   */
  async updateReportStatus(
    reportId: string, 
    newStatus: string, 
    changedBy: string, 
    comment?: string
  ): Promise<void> {
    try {
      const report = await this.prisma.debugReport.findUnique({
        where: { reportId },
        select: { id: true, status: true }
      });

      if (!report) {
        throw new Error('Report not found');
      }

      await this.prisma.$transaction([
        this.prisma.debugReport.update({
          where: { reportId },
          data: { 
            status: this.mapStatus(newStatus),
            updatedAt: new Date()
          }
        }),
        this.prisma.debugReportStatusHistory.create({
          data: {
            reportId: report.id,
            oldStatus: report.status,
            newStatus: this.mapStatus(newStatus),
            changedBy,
            comment
          }
        })
      ]);

      console.log(`✅ [DebugReportService] Updated report ${reportId} status to ${newStatus}`);
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to update report status:', error);
      throw error;
    }
  }

  /**
   * Assign report to a user
   */
  async assignReport(reportId: string, assignedTo: string, assignedBy: string): Promise<void> {
    try {
      const report = await this.prisma.debugReport.findUnique({
        where: { reportId },
        select: { id: true }
      });

      if (!report) {
        throw new Error('Report not found');
      }

      await this.prisma.$transaction([
        this.prisma.debugReport.update({
          where: { reportId },
          data: { 
            assignedToId: assignedTo,
            assignedAt: new Date(),
            status: DebugReportStatus.IN_PROGRESS, // Auto-set to in progress when assigned
            updatedAt: new Date()
          }
        }),
        this.prisma.debugReportStatusHistory.create({
          data: {
            reportId: report.id,
            oldStatus: null,
            newStatus: DebugReportStatus.IN_PROGRESS,
            changedBy: assignedBy,
            comment: `Assigned to user ${assignedTo}`
          }
        })
      ]);

      console.log(`✅ [DebugReportService] Assigned report ${reportId} to ${assignedTo}`);
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to assign report:', error);
      throw error;
    }
  }

  /**
   * Get reports with filtering, sorting, and pagination
   */
  async getReportsWithFilters(options: GetReportsOptions = {}): Promise<GetReportsResponse> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      filterByType,
      filterBySeverity,
      filterByStatus,
      filterByAssignee,
      filterByDateStart,
      filterByDateEnd,
      searchQuery
    } = options;

    try {
      const where: any = {};

      // Apply filters
      if (filterByType) where.type = this.mapReportType(filterByType);
      if (filterBySeverity) where.severity = this.mapSeverity(filterBySeverity);
      if (filterByStatus) where.status = this.mapStatus(filterByStatus);
      if (filterByAssignee) where.assignedToId = filterByAssignee;
      
      if (filterByDateStart || filterByDateEnd) {
        where.createdAt = {};
        if (filterByDateStart) where.createdAt.gte = new Date(filterByDateStart);
        if (filterByDateEnd) where.createdAt.lte = new Date(filterByDateEnd);
      }

      if (searchQuery) {
        where.OR = [
          { title: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } },
          { reportId: { contains: searchQuery, mode: 'insensitive' } }
        ];
      }

      const [reports, total] = await Promise.all([
        this.prisma.debugReport.findMany({
          where,
          include: {
            user: { select: { id: true, email: true, username: true } },
            assignedTo: { select: { id: true, email: true, username: true } },
            tags: true,
            _count: { select: { comments: true } }
          },
          orderBy: { [sortBy]: sortOrder },
          skip: (page - 1) * limit,
          take: limit
        }),
        this.prisma.debugReport.count({ where })
      ]);

      return {
        reports: reports.map(this.formatReportForAPI),
        total,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      };
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to get reports:', error);
      throw error;
    }
  }

  /**
   * Get a single report by ID
   */
  async getReportById(reportId: string): Promise<any | null> {
    try {
      const report = await this.prisma.debugReport.findUnique({
        where: { reportId },
        include: {
          user: { select: { id: true, email: true, username: true } },
          assignedTo: { select: { id: true, email: true, username: true } },
          tags: true,
          statusHistory: {
            include: {
              changer: { select: { id: true, email: true, username: true } }
            },
            orderBy: { createdAt: 'desc' }
          },
          comments: {
            include: {
              user: { select: { id: true, email: true, username: true } }
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      return report ? this.formatReportForAPI(report) : null;
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to get report by ID:', error);
      throw error;
    }
  }

  /**
   * Add a comment to a report
   */
  async addComment(reportId: string, userId: string, comment: string, isInternal = true): Promise<void> {
    try {
      const report = await this.prisma.debugReport.findUnique({
        where: { reportId },
        select: { id: true }
      });

      if (!report) {
        throw new Error('Report not found');
      }

      await this.prisma.debugReportComment.create({
        data: {
          reportId: report.id,
          userId,
          comment,
          isInternal
        }
      });

      console.log(`✅ [DebugReportService] Added comment to report ${reportId}`);
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to add comment:', error);
      throw error;
    }
  }

  /**
   * Get dashboard statistics
   */
  async getStats(): Promise<any> {
    try {
      const [
        totalReports,
        notReviewedCount,
        inProgressCount,
        completedCount,
        recentReports
      ] = await Promise.all([
        this.prisma.debugReport.count(),
        this.prisma.debugReport.count({ where: { status: DebugReportStatus.NOT_REVIEWED } }),
        this.prisma.debugReport.count({ where: { status: DebugReportStatus.IN_PROGRESS } }),
        this.prisma.debugReport.count({ where: { status: DebugReportStatus.COMPLETED } }),
        this.prisma.debugReport.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        })
      ]);

      return {
        totalReports,
        statusDistribution: {
          notReviewed: notReviewedCount,
          inProgress: inProgressCount,
          completed: completedCount
        },
        recentReports
      };
    } catch (error) {
      console.error('❌ [DebugReportService] Failed to get stats:', error);
      throw error;
    }
  }

  // Helper methods for mapping between API and database enums
  private mapReportType(type: string): DebugReportType {
    const mapping: Record<string, DebugReportType> = {
      'bug': DebugReportType.BUG,
      'feature-request': DebugReportType.FEATURE_REQUEST,
      'performance': DebugReportType.PERFORMANCE,
      'ui-ux': DebugReportType.UI_UX,
      'improvement': DebugReportType.IMPROVEMENT,
      'question': DebugReportType.QUESTION,
      'other': DebugReportType.OTHER
    };
    return mapping[type] || DebugReportType.OTHER;
  }

  private mapSeverity(severity: string): DebugReportSeverity {
    const mapping: Record<string, DebugReportSeverity> = {
      'low': DebugReportSeverity.LOW,
      'medium': DebugReportSeverity.MEDIUM,
      'high': DebugReportSeverity.HIGH,
      'critical': DebugReportSeverity.CRITICAL
    };
    return mapping[severity] || DebugReportSeverity.MEDIUM;
  }

  private mapStatus(status: string): DebugReportStatus {
    const mapping: Record<string, DebugReportStatus> = {
      'not_reviewed': DebugReportStatus.NOT_REVIEWED,
      'in_progress': DebugReportStatus.IN_PROGRESS,
      'completed': DebugReportStatus.COMPLETED,
      'archived': DebugReportStatus.ARCHIVED,
      'duplicate': DebugReportStatus.DUPLICATE,
      'wont_fix': DebugReportStatus.WONT_FIX
    };
    return mapping[status] || DebugReportStatus.NOT_REVIEWED;
  }

  private formatReportForAPI(report: any): any {
    return {
      id: report.id,
      reportId: report.reportId,
      type: report.type.toLowerCase().replace('_', '-'),
      severity: report.severity.toLowerCase(),
      status: report.status.toLowerCase().replace('_', '-'),
      priority: report.priority,
      title: report.title,
      description: report.description,
      stepsToReproduce: report.stepsToReproduce,
      expectedBehavior: report.expectedBehavior,
      actualBehavior: report.actualBehavior,
      additionalNotes: report.additionalNotes,
      user: report.user,
      assignedTo: report.assignedTo,
      assignedAt: report.assignedAt,
      tags: report.tags?.map((t: any) => t.tag) || [],
      commentCount: report._count?.comments || 0,
      statusHistory: report.statusHistory,
      comments: report.comments,
      sessionId: report.sessionId,
      currentUrl: report.currentUrl,
      userAgent: report.userAgent,
      viewportWidth: report.viewportWidth,
      viewportHeight: report.viewportHeight,
      diagnosticData: report.diagnosticData,
      logs: report.logs,
      userActions: report.userActions,
      clientTimestamp: report.clientTimestamp,
      serverReceivedAt: report.serverReceivedAt,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt
    };
  }
}
