
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('@prisma/client/runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailVerified: 'emailVerified',
  verificationToken: 'verificationToken',
  phoneNumber: 'phoneNumber',
  phoneVerified: 'phoneVerified',
  otpSecret: 'otpSecret',
  otpTimestamp: 'otpTimestamp',
  username: 'username',
  reputationScore: 'reputationScore',
  reputationLevel: 'reputationLevel'
};

exports.Prisma.OfferScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  currencyPair: 'currencyPair',
  amount: 'amount',
  baseRate: 'baseRate',
  adjustmentForLowerRep: 'adjustmentForLowerRep',
  adjustmentForHigherRep: 'adjustmentForHigherRep',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InterestScalarFieldEnum = {
  id: 'id',
  offerId: 'offerId',
  interestedUserId: 'interestedUserId',
  status: 'status',
  declineReasonCode: 'declineReasonCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatSessionScalarFieldEnum = {
  id: 'id',
  offerId: 'offerId',
  userOneId: 'userOneId',
  userTwoId: 'userTwoId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  interestId: 'interestId'
};

exports.Prisma.ChatMessageScalarFieldEnum = {
  id: 'id',
  chatSessionId: 'chatSessionId',
  senderId: 'senderId',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isSystemMessage: 'isSystemMessage',
  transactionId: 'transactionId'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  message: 'message',
  isRead: 'isRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  relatedEntityType: 'relatedEntityType',
  relatedEntityId: 'relatedEntityId',
  actorId: 'actorId',
  actorUsername: 'actorUsername',
  data: 'data'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  offerId: 'offerId',
  chatSessionId: 'chatSessionId',
  currencyA: 'currencyA',
  amountA: 'amountA',
  currencyAProviderId: 'currencyAProviderId',
  currencyB: 'currencyB',
  amountB: 'amountB',
  currencyBProviderId: 'currencyBProviderId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  termsAgreementTimestampPayer1: 'termsAgreementTimestampPayer1',
  termsAgreementTimestampPayer2: 'termsAgreementTimestampPayer2',
  agreedFirstPayerId: 'agreedFirstPayerId',
  firstPayerDesignationTimestamp: 'firstPayerDesignationTimestamp',
  paymentExpectedByPayer1: 'paymentExpectedByPayer1',
  paymentDeclaredAtPayer1: 'paymentDeclaredAtPayer1',
  paymentTrackingNumberPayer1: 'paymentTrackingNumberPayer1',
  firstPaymentConfirmedByPayer2At: 'firstPaymentConfirmedByPayer2At',
  paymentExpectedByPayer2: 'paymentExpectedByPayer2',
  paymentDeclaredAtPayer2: 'paymentDeclaredAtPayer2',
  paymentTrackingNumberPayer2: 'paymentTrackingNumberPayer2',
  secondPaymentConfirmedByPayer1At: 'secondPaymentConfirmedByPayer1At',
  cancellationReason: 'cancellationReason',
  cancelledByUserId: 'cancelledByUserId',
  disputeReason: 'disputeReason',
  disputedByUserId: 'disputedByUserId',
  disputeResolvedAt: 'disputeResolvedAt',
  disputeResolutionNotes: 'disputeResolutionNotes'
};

exports.Prisma.PayerNegotiationScalarFieldEnum = {
  negotiationId: 'negotiationId',
  transactionId: 'transactionId',
  partyA_Id: 'partyA_Id',
  partyB_Id: 'partyB_Id',
  partyA_receivingInfoStatus: 'partyA_receivingInfoStatus',
  partyB_receivingInfoStatus: 'partyB_receivingInfoStatus',
  partyA_PaymentReceivingInfoId: 'partyA_PaymentReceivingInfoId',
  partyB_PaymentReceivingInfoId: 'partyB_PaymentReceivingInfoId',
  systemRecommendedPayerId: 'systemRecommendedPayerId',
  systemRecommendationRule: 'systemRecommendationRule',
  systemRecommendationReason: 'systemRecommendationReason',
  systemRecommendationDetails: 'systemRecommendationDetails',
  currentProposal_PayerId: 'currentProposal_PayerId',
  currentProposal_ById: 'currentProposal_ById',
  currentProposal_Message: 'currentProposal_Message',
  partyA_agreedToCurrentProposal: 'partyA_agreedToCurrentProposal',
  partyB_agreedToCurrentProposal: 'partyB_agreedToCurrentProposal',
  negotiationStatus: 'negotiationStatus',
  finalizedPayerId: 'finalizedPayerId',
  paymentTimerDueDate: 'paymentTimerDueDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentReceivingInfoScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  bankName: 'bankName',
  accountNumber: 'accountNumber',
  accountHolderName: 'accountHolderName',
  isDefaultForUser: 'isDefaultForUser',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DebugReportScalarFieldEnum = {
  id: 'id',
  reportId: 'reportId',
  userId: 'userId',
  type: 'type',
  severity: 'severity',
  status: 'status',
  priority: 'priority',
  title: 'title',
  description: 'description',
  stepsToReproduce: 'stepsToReproduce',
  expectedBehavior: 'expectedBehavior',
  actualBehavior: 'actualBehavior',
  additionalNotes: 'additionalNotes',
  assignedToId: 'assignedToId',
  assignedAt: 'assignedAt',
  sessionId: 'sessionId',
  currentUrl: 'currentUrl',
  userAgent: 'userAgent',
  viewportWidth: 'viewportWidth',
  viewportHeight: 'viewportHeight',
  diagnosticData: 'diagnosticData',
  logs: 'logs',
  userActions: 'userActions',
  clientTimestamp: 'clientTimestamp',
  serverReceivedAt: 'serverReceivedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DebugReportTagScalarFieldEnum = {
  id: 'id',
  reportId: 'reportId',
  tag: 'tag',
  createdAt: 'createdAt'
};

exports.Prisma.DebugReportStatusHistoryScalarFieldEnum = {
  id: 'id',
  reportId: 'reportId',
  oldStatus: 'oldStatus',
  newStatus: 'newStatus',
  changedBy: 'changedBy',
  comment: 'comment',
  createdAt: 'createdAt'
};

exports.Prisma.DebugReportCommentScalarFieldEnum = {
  id: 'id',
  reportId: 'reportId',
  userId: 'userId',
  comment: 'comment',
  isInternal: 'isInternal',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.OfferType = exports.$Enums.OfferType = {
  BUY: 'BUY',
  SELL: 'SELL'
};

exports.OfferStatus = exports.$Enums.OfferStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DEACTIVATED: 'DEACTIVATED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  AWAITING_FIRST_PAYER_DESIGNATION: 'AWAITING_FIRST_PAYER_DESIGNATION',
  AWAITING_FIRST_PAYER_PAYMENT: 'AWAITING_FIRST_PAYER_PAYMENT',
  AWAITING_SECOND_PAYER_CONFIRMATION: 'AWAITING_SECOND_PAYER_CONFIRMATION',
  AWAITING_SECOND_PAYER_PAYMENT: 'AWAITING_SECOND_PAYER_PAYMENT',
  AWAITING_FIRST_PAYER_CONFIRMATION: 'AWAITING_FIRST_PAYER_CONFIRMATION',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  DISPUTED: 'DISPUTED'
};

exports.ReceivingInfoStatus = exports.$Enums.ReceivingInfoStatus = {
  PENDING_INPUT: 'PENDING_INPUT',
  PROVIDED: 'PROVIDED',
  CONFIRMED_FROM_PROFILE: 'CONFIRMED_FROM_PROFILE'
};

exports.NegotiationStatus = exports.$Enums.NegotiationStatus = {
  PENDING_RECEIVING_INFO: 'PENDING_RECEIVING_INFO',
  AWAITING_PARTY_A_RECEIVING_INFO: 'AWAITING_PARTY_A_RECEIVING_INFO',
  AWAITING_PARTY_B_RECEIVING_INFO: 'AWAITING_PARTY_B_RECEIVING_INFO',
  READY_TO_NEGOTIATE: 'READY_TO_NEGOTIATE',
  PENDING_RESPONSE: 'PENDING_RESPONSE',
  FINALIZED: 'FINALIZED',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED'
};

exports.InterestStatus = exports.$Enums.InterestStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  DECLINED: 'DECLINED'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  NEW_INTEREST_ON_YOUR_OFFER: 'NEW_INTEREST_ON_YOUR_OFFER',
  YOUR_INTEREST_ACCEPTED: 'YOUR_INTEREST_ACCEPTED',
  YOUR_INTEREST_DECLINED: 'YOUR_INTEREST_DECLINED',
  CHAT_MESSAGE_RECEIVED: 'CHAT_MESSAGE_RECEIVED',
  OFFER_STATUS_UPDATED_BY_OWNER: 'OFFER_STATUS_UPDATED_BY_OWNER',
  OFFER_STATUS_CHANGED: 'OFFER_STATUS_CHANGED',
  TRANSACTION_STARTED: 'TRANSACTION_STARTED',
  TRANSACTION_ACTION_REQUIRED: 'TRANSACTION_ACTION_REQUIRED',
  TRANSACTION_UPDATE: 'TRANSACTION_UPDATE',
  TRANSACTION_PAYMENT_DECLARED: 'TRANSACTION_PAYMENT_DECLARED',
  TRANSACTION_PAYMENT_CONFIRMED: 'TRANSACTION_PAYMENT_CONFIRMED',
  TRANSACTION_COMPLETED: 'TRANSACTION_COMPLETED',
  TRANSACTION_CANCELLED: 'TRANSACTION_CANCELLED',
  TRANSACTION_DISPUTED: 'TRANSACTION_DISPUTED',
  TRANSACTION_AUTO_CANCELLED_TIMER: 'TRANSACTION_AUTO_CANCELLED_TIMER'
};

exports.DebugReportType = exports.$Enums.DebugReportType = {
  BUG: 'BUG',
  FEATURE_REQUEST: 'FEATURE_REQUEST',
  PERFORMANCE: 'PERFORMANCE',
  UI_UX: 'UI_UX',
  IMPROVEMENT: 'IMPROVEMENT',
  QUESTION: 'QUESTION',
  OTHER: 'OTHER'
};

exports.DebugReportSeverity = exports.$Enums.DebugReportSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.DebugReportStatus = exports.$Enums.DebugReportStatus = {
  NOT_REVIEWED: 'NOT_REVIEWED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  ARCHIVED: 'ARCHIVED',
  DUPLICATE: 'DUPLICATE',
  WONT_FIX: 'WONT_FIX'
};

exports.Prisma.ModelName = {
  User: 'User',
  Offer: 'Offer',
  Interest: 'Interest',
  ChatSession: 'ChatSession',
  ChatMessage: 'ChatMessage',
  Notification: 'Notification',
  Transaction: 'Transaction',
  PayerNegotiation: 'PayerNegotiation',
  PaymentReceivingInfo: 'PaymentReceivingInfo',
  DebugReport: 'DebugReport',
  DebugReportTag: 'DebugReportTag',
  DebugReportStatusHistory: 'DebugReportStatusHistory',
  DebugReportComment: 'DebugReportComment'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Code\\MUNygo\\backend\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\Code\\MUNygo\\backend\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "6.8.2",
  "engineVersion": "2060c79ba17c6bb9f5823312b6f6b7f4a845738e",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider = \"prisma-client-js\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\n// Basic user model\nmodel User {\n  id        String   @id @default(cuid())\n  email     String   @unique\n  password  String\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Email Verification Fields\n  emailVerified     Boolean? @default(false)\n  verificationToken String?  @unique // Store the verification token\n\n  // Phone Verification Fields\n  phoneNumber   String?   @unique\n  phoneVerified Boolean?  @default(false)\n  otpSecret     String? // Secret used to generate time-based OTPs\n  otpTimestamp  DateTime? // Timestamp when the OTP was generated/sent\n\n  // Reputation Fields\n  username        String? // Optional username, defaults to email-based if null\n  reputationScore Int     @default(0) // Raw numerical score\n  reputationLevel Int     @default(1) // Level 1-5 based on score ranges\n\n  // Relations\n  offers    Offer[] // Relation to offers created by the user\n  interests Interest[] @relation(\"InterestedUsers\")\n\n  // ChatSession relations\n  chatSessionsAsCreator            ChatSession[]  @relation(\"ChatSessionCreator\")\n  chatSessionsAsInterested         ChatSession[]  @relation(\"ChatSessionInterested\")\n  notifications                    Notification[] // Relation to notifications for the user\n  sentChatMessages                 ChatMessage[]  @relation(\"SentMessages\") // This is the other side\n  transactionsAsCurrencyAProvider  Transaction[]  @relation(\"CurrencyAProvider\")\n  transactionsAsCurrencyBProvider  Transaction[]  @relation(\"CurrencyBProvider\")\n  transactionsWithAgreedFirstPayer Transaction[]  @relation(\"AgreedFirstPayer\")\n  transactionsCancelledBy          Transaction[]  @relation(\"CancelledByUser\") // Back-relation for cancelled transactions\n  transactionsDisputedBy           Transaction[]  @relation(\"DisputedByUser\") // Back-relation for disputed transactions\n\n  // New relations\n  partyANegotiations   PayerNegotiation[]     @relation(\"PartyANegotiations\")\n  partyBNegotiations   PayerNegotiation[]     @relation(\"PartyBNegotiations\")\n  paymentReceivingInfo PaymentReceivingInfo[] @relation(\"UserPaymentReceivingInfo\") // Corrected: Added relation name\n\n  // Debug report relations\n  debugReports         DebugReport[]              @relation(\"UserDebugReports\")\n  assignedDebugReports DebugReport[]              @relation(\"AssignedReports\")\n  debugReportComments  DebugReportComment[]       @relation(\"DebugReportComments\")\n  debugStatusChanges   DebugReportStatusHistory[] @relation(\"DebugStatusChanger\")\n}\n\n// Enums for Offer Type and Status\nenum OfferType {\n  BUY\n  SELL\n}\n\nenum OfferStatus {\n  ACTIVE\n  INACTIVE\n  DEACTIVATED // Assuming this was a typo and should be distinct from INACTIVE or a specific state\n  COMPLETED\n  CANCELLED\n}\n\n// Enum for Transaction Status\nenum TransactionStatus {\n  AWAITING_FIRST_PAYER_DESIGNATION // Initial status: Parties need to designate who pays first.\n  AWAITING_FIRST_PAYER_PAYMENT\n  AWAITING_SECOND_PAYER_CONFIRMATION\n  AWAITING_SECOND_PAYER_PAYMENT\n  AWAITING_FIRST_PAYER_CONFIRMATION\n  COMPLETED\n  CANCELLED\n  DISPUTED\n}\n\n// Enum for Receiving Info Status\nenum ReceivingInfoStatus {\n  PENDING_INPUT\n  PROVIDED\n  CONFIRMED_FROM_PROFILE\n  // Add other statuses if needed, e.g., REJECTED\n}\n\n// Enum for Negotiation Status\nenum NegotiationStatus {\n  PENDING_RECEIVING_INFO // Initial state, awaiting one or both parties' payment info.\n  AWAITING_PARTY_A_RECEIVING_INFO // Party B provided, Party A pending.\n  AWAITING_PARTY_B_RECEIVING_INFO // Party A provided, Party B pending.\n  READY_TO_NEGOTIATE // Both parties provided info. System recommendation is generated at this point.\n  // This status is transient and should immediately move to PENDING_RESPONSE.\n  PENDING_RESPONSE // A proposal (system or user) is active, awaiting response.\n  FINALIZED // Agreement reached on the first payer.\n  EXPIRED // Negotiation timed out.\n  CANCELLED // Negotiation was cancelled by a user or system.\n}\n\nmodel Offer {\n  id                     String      @id @default(cuid())\n  userId                 String\n  user                   User        @relation(fields: [userId], references: [id])\n  type                   OfferType\n  currencyPair           String      @default(\"CAD-IRR\")\n  amount                 Float\n  baseRate               Float\n  adjustmentForLowerRep  Float\n  adjustmentForHigherRep Float\n  status                 OfferStatus @default(ACTIVE)\n  createdAt              DateTime    @default(now())\n  updatedAt              DateTime    @updatedAt\n  interests              Interest[]\n\n  // ChatSession relation\n  chatSessions ChatSession[]\n  transaction  Transaction? // Back-relation for the transaction linked to this offer\n}\n\n// Enum for Interest Status\nenum InterestStatus {\n  PENDING\n  ACCEPTED\n  DECLINED\n}\n\nmodel Interest {\n  id                String         @id @default(cuid())\n  offerId           String\n  offer             Offer          @relation(fields: [offerId], references: [id], onDelete: Cascade)\n  interestedUserId  String\n  interestedUser    User           @relation(\"InterestedUsers\", fields: [interestedUserId], references: [id], onDelete: Cascade)\n  status            InterestStatus @default(PENDING)\n  declineReasonCode String?\n  createdAt         DateTime       @default(now())\n  updatedAt         DateTime       @updatedAt\n  chatSession       ChatSession?   @relation(\"ChatToInterest\") // Back relation for one-to-one\n\n  @@unique([offerId, interestedUserId]) // Added composite unique constraint\n  @@index([offerId])\n  @@index([interestedUserId])\n}\n\nmodel ChatSession {\n  id        String   @id @default(cuid())\n  offerId   String\n  offer     Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)\n  userOneId String // Typically the offer creator\n  userOne   User     @relation(\"ChatSessionCreator\", fields: [userOneId], references: [id], onDelete: Cascade)\n  userTwoId String // Typically the user who showed interest\n  userTwo   User     @relation(\"ChatSessionInterested\", fields: [userTwoId], references: [id], onDelete: Cascade)\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  interestId  String?       @unique\n  interest    Interest?     @relation(\"ChatToInterest\", fields: [interestId], references: [id], onDelete: SetNull)\n  messages    ChatMessage[] // Relation to chat messages\n  transaction Transaction? // Relation to the transaction associated with this chat session\n\n  @@index([userOneId])\n  @@index([userTwoId])\n  @@index([offerId])\n}\n\nmodel ChatMessage {\n  id              String      @id @default(cuid())\n  chatSessionId   String\n  chatSession     ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)\n  senderId        String? // Optional: null for system messages\n  sender          User?       @relation(\"SentMessages\", fields: [senderId], references: [id], onDelete: SetNull)\n  content         String\n  createdAt       DateTime    @default(now())\n  updatedAt       DateTime    @updatedAt\n  isSystemMessage Boolean     @default(false) // This field is crucial\n  transactionId   String? // Link to transaction for context if needed\n\n  @@index([chatSessionId])\n  @@index([senderId])\n  @@index([transactionId])\n}\n\n// Enum for Notification Types\nenum NotificationType {\n  NEW_INTEREST_ON_YOUR_OFFER\n  YOUR_INTEREST_ACCEPTED\n  YOUR_INTEREST_DECLINED\n  CHAT_MESSAGE_RECEIVED // Example for future use\n  OFFER_STATUS_UPDATED_BY_OWNER // e.g. offer creator cancelled their own offer\n  OFFER_STATUS_CHANGED // Added for general offer status changes by owner\n  TRANSACTION_STARTED\n  TRANSACTION_ACTION_REQUIRED\n  TRANSACTION_UPDATE\n  TRANSACTION_PAYMENT_DECLARED // Added\n  TRANSACTION_PAYMENT_CONFIRMED // Added\n  TRANSACTION_COMPLETED\n  TRANSACTION_CANCELLED\n  TRANSACTION_DISPUTED\n  TRANSACTION_AUTO_CANCELLED_TIMER // Added\n  // Add other types as needed\n}\n\n// Model for storing Notifications\nmodel Notification {\n  id        String           @id @default(uuid())\n  userId    String // Recipient of the notification\n  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)\n  type      NotificationType\n  message   String // Display message for the notification\n  isRead    Boolean          @default(false)\n  createdAt DateTime         @default(now())\n  updatedAt DateTime         @updatedAt\n\n  // Optional fields to link to related entities\n  relatedEntityType String? // e.g., \"OFFER\", \"INTEREST\", \"CHAT_SESSION\"\n  relatedEntityId   String? // ID of the entity the notification refers to\n\n  actorId       String? // ID of the user who triggered the event (if applicable)\n  // actor              User?            @relation(\"NotificationActor\", fields: [actorId], references: [id], onDelete:SetNull) // Optional: if you need to link to actor User model\n  actorUsername String? // Username of the actor for quick display\n\n  data String? // For storing additional context, e.g., offer title, decline reason details\n\n  @@index([userId])\n  @@index([userId, isRead, createdAt]) // To quickly fetch and sort unread notifications for a user\n}\n\nmodel Transaction {\n  id                  String            @id @default(cuid())\n  offerId             String?           @unique // Link to the original offer if applicable, made unique for 1-to-1\n  chatSessionId       String            @unique // Each transaction must belong to a chat session\n  currencyA           String\n  amountA             Float\n  currencyAProviderId String // User ID of the provider for currency A\n  currencyB           String\n  amountB             Float\n  currencyBProviderId String // User ID of the provider for currency B\n  status              TransactionStatus @default(AWAITING_FIRST_PAYER_DESIGNATION) // Default to designation phase\n  createdAt           DateTime          @default(now())\n  updatedAt           DateTime          @updatedAt\n\n  // Timestamps for terms agreement (consider if these are still needed in the same way)\n  termsAgreementTimestampPayer1 DateTime? // Timestamp when provider of currency A agreed\n  termsAgreementTimestampPayer2 DateTime? // Timestamp when provider of currency B agreed\n\n  // First payer designation\n  agreedFirstPayerId             String? // User ID of the party agreed to pay first\n  firstPayerDesignationTimestamp DateTime? // Timestamp when the first payer was agreed upon\n\n  // Payment tracking for the first payer (who is `agreedFirstPayerId`)\n  paymentExpectedByPayer1     DateTime? // Deadline for the first payment\n  paymentDeclaredAtPayer1     DateTime? // Timestamp when the first payer declared payment\n  paymentTrackingNumberPayer1 String? // Optional tracking number for the first payment\n\n  // Confirmation of the first payment (by the other party)\n  firstPaymentConfirmedByPayer2At DateTime? // Timestamp when the other party confirmed receipt of the first payment\n\n  // Payment tracking for the second payer (who is NOT `agreedFirstPayerId`)\n  paymentExpectedByPayer2     DateTime? // Deadline for the second payment\n  paymentDeclaredAtPayer2     DateTime? // Timestamp when the second payer declared payment\n  paymentTrackingNumberPayer2 String? // Optional tracking number for the second payment\n\n  // Confirmation of the second payment (by the `agreedFirstPayerId`)\n  secondPaymentConfirmedByPayer1At DateTime? // Timestamp when the first payer confirmed receipt of the second payment\n\n  // Cancellation and Dispute\n  cancellationReason     String?\n  cancelledByUserId      String? // User ID of the party who initiated cancellation\n  disputeReason          String?\n  disputedByUserId       String? // User ID of the party who initiated dispute\n  disputeResolvedAt      DateTime?\n  disputeResolutionNotes String?\n\n  // Relations\n  offer             Offer?            @relation(fields: [offerId], references: [id])\n  chatSession       ChatSession       @relation(fields: [chatSessionId], references: [id])\n  currencyAProvider User              @relation(\"CurrencyAProvider\", fields: [currencyAProviderId], references: [id])\n  currencyBProvider User              @relation(\"CurrencyBProvider\", fields: [currencyBProviderId], references: [id])\n  agreedFirstPayer  User?             @relation(\"AgreedFirstPayer\", fields: [agreedFirstPayerId], references: [id], onDelete: SetNull)\n  cancelledByUser   User?             @relation(\"CancelledByUser\", fields: [cancelledByUserId], references: [id], onDelete: SetNull)\n  disputedByUser    User?             @relation(\"DisputedByUser\", fields: [disputedByUserId], references: [id], onDelete: SetNull)\n  payerNegotiation  PayerNegotiation?\n\n  @@index([currencyAProviderId])\n  @@index([currencyBProviderId])\n  @@index([agreedFirstPayerId])\n  @@index([status])\n  @@index([cancelledByUserId])\n  @@index([disputedByUserId])\n}\n\nmodel PayerNegotiation {\n  negotiationId String      @id @default(cuid())\n  transactionId String      @unique // Each transaction has one negotiation\n  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)\n  partyA_Id     String\n  partyA        User        @relation(\"PartyANegotiations\", fields: [partyA_Id], references: [id], onDelete: Cascade)\n  partyB_Id     String\n  partyB        User        @relation(\"PartyBNegotiations\", fields: [partyB_Id], references: [id], onDelete: Cascade)\n\n  partyA_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)\n  partyB_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)\n\n  // Store the ID of the PaymentReceivingInfo used by each party for this negotiation\n  partyA_PaymentReceivingInfoId String?\n  partyA_PaymentReceivingInfo   PaymentReceivingInfo? @relation(\"PartyAReceivingInfo\", fields: [partyA_PaymentReceivingInfoId], references: [id], onDelete: SetNull)\n  partyB_PaymentReceivingInfoId String?\n  partyB_PaymentReceivingInfo   PaymentReceivingInfo? @relation(\"PartyBReceivingInfo\", fields: [partyB_PaymentReceivingInfoId], references: [id], onDelete: SetNull)\n\n  systemRecommendedPayerId    String?\n  systemRecommendationRule    String? // e.g., \"REPUTATION\", \"CURRENCY\"\n  systemRecommendationReason  String? // Textual reason\n  systemRecommendationDetails String? // Store specific details like reputation scores\n\n  currentProposal_PayerId String? // Who is proposed to pay first\n  currentProposal_ById    String? // Who made the current proposal ('system' or a userId)\n  currentProposal_Message String? // Optional message with the proposal\n\n  partyA_agreedToCurrentProposal Boolean @default(false)\n  partyB_agreedToCurrentProposal Boolean @default(false)\n\n  negotiationStatus   NegotiationStatus @default(PENDING_RECEIVING_INFO) // This default will now be valid\n  finalizedPayerId    String?\n  paymentTimerDueDate DateTime? // Timer for the finalized payer to make their payment\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([partyA_Id])\n  @@index([partyB_Id])\n  @@index([transactionId])\n}\n\n// Define the PaymentReceivingInfo model\nmodel PaymentReceivingInfo {\n  id                String   @id @default(cuid())\n  userId            String\n  user              User     @relation(\"UserPaymentReceivingInfo\", fields: [userId], references: [id])\n  bankName          String\n  accountNumber     String // Consider encrypting this field\n  accountHolderName String\n  isDefaultForUser  Boolean  @default(false)\n  createdAt         DateTime @default(now())\n  updatedAt         DateTime @updatedAt\n\n  // Add these back-relations\n  negotiationsAsPartyA PayerNegotiation[] @relation(\"PartyAReceivingInfo\")\n  negotiationsAsPartyB PayerNegotiation[] @relation(\"PartyBReceivingInfo\")\n\n  @@index([userId])\n}\n\n// Debug Report System Models\nmodel DebugReport {\n  id       String  @id @default(uuid())\n  reportId String  @unique @map(\"report_id\")\n  userId   String? @map(\"user_id\")\n  user     User?   @relation(\"UserDebugReports\", fields: [userId], references: [id])\n\n  // Report details\n  type             DebugReportType\n  severity         DebugReportSeverity\n  status           DebugReportStatus   @default(NOT_REVIEWED)\n  priority         Int                 @default(3) // 1-5 scale\n  title            String              @db.VarChar(200)\n  description      String              @db.Text\n  stepsToReproduce String?             @map(\"steps_to_reproduce\") @db.Text\n  expectedBehavior String?             @map(\"expected_behavior\") @db.Text\n  actualBehavior   String?             @map(\"actual_behavior\") @db.Text\n  additionalNotes  String?             @map(\"additional_notes\") @db.Text\n\n  // Assignment and workflow\n  assignedToId String?   @map(\"assigned_to\")\n  assignedTo   User?     @relation(\"AssignedReports\", fields: [assignedToId], references: [id])\n  assignedAt   DateTime? @map(\"assigned_at\")\n\n  // Context data\n  sessionId      String? @map(\"session_id\")\n  currentUrl     String? @map(\"current_url\") @db.Text\n  userAgent      String? @map(\"user_agent\") @db.Text\n  viewportWidth  Int?    @map(\"viewport_width\")\n  viewportHeight Int?    @map(\"viewport_height\")\n\n  // JSON data\n  diagnosticData Json? @map(\"diagnostic_data\")\n  logs           Json?\n  userActions    Json? @map(\"user_actions\")\n\n  // Timestamps\n  clientTimestamp  DateTime @map(\"client_timestamp\")\n  serverReceivedAt DateTime @default(now()) @map(\"server_received_at\")\n  createdAt        DateTime @default(now()) @map(\"created_at\")\n  updatedAt        DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  tags          DebugReportTag[]\n  statusHistory DebugReportStatusHistory[]\n  comments      DebugReportComment[]\n\n  @@index([status])\n  @@index([type])\n  @@index([severity])\n  @@index([assignedToId])\n  @@index([userId])\n  @@index([createdAt])\n  @@map(\"debug_reports\")\n}\n\nmodel DebugReportTag {\n  id        String      @id @default(uuid())\n  reportId  String      @map(\"report_id\")\n  report    DebugReport @relation(fields: [reportId], references: [id], onDelete: Cascade)\n  tag       String      @db.VarChar(50)\n  createdAt DateTime    @default(now()) @map(\"created_at\")\n\n  @@unique([reportId, tag])\n  @@index([tag])\n  @@map(\"debug_report_tags\")\n}\n\nmodel DebugReportStatusHistory {\n  id        String             @id @default(uuid())\n  reportId  String             @map(\"report_id\")\n  report    DebugReport        @relation(fields: [reportId], references: [id], onDelete: Cascade)\n  oldStatus DebugReportStatus? @map(\"old_status\")\n  newStatus DebugReportStatus  @map(\"new_status\")\n  changedBy String?            @map(\"changed_by\")\n  changer   User?              @relation(\"DebugStatusChanger\", fields: [changedBy], references: [id])\n  comment   String?            @db.Text\n  createdAt DateTime           @default(now()) @map(\"created_at\")\n\n  @@index([reportId])\n  @@map(\"debug_report_status_history\")\n}\n\nmodel DebugReportComment {\n  id         String      @id @default(uuid())\n  reportId   String      @map(\"report_id\")\n  report     DebugReport @relation(fields: [reportId], references: [id], onDelete: Cascade)\n  userId     String      @map(\"user_id\")\n  user       User        @relation(\"DebugReportComments\", fields: [userId], references: [id])\n  comment    String      @db.Text\n  isInternal Boolean     @default(true) @map(\"is_internal\")\n  createdAt  DateTime    @default(now()) @map(\"created_at\")\n\n  @@index([reportId])\n  @@map(\"debug_report_comments\")\n}\n\n// Debug Report Enums\nenum DebugReportType {\n  BUG\n  FEATURE_REQUEST\n  PERFORMANCE\n  UI_UX\n  IMPROVEMENT\n  QUESTION\n  OTHER\n\n  @@map(\"debug_report_type\")\n}\n\nenum DebugReportSeverity {\n  LOW\n  MEDIUM\n  HIGH\n  CRITICAL\n\n  @@map(\"debug_report_severity\")\n}\n\nenum DebugReportStatus {\n  NOT_REVIEWED\n  IN_PROGRESS\n  COMPLETED\n  ARCHIVED\n  DUPLICATE\n  WONT_FIX\n\n  @@map(\"debug_report_status\")\n}\n",
  "inlineSchemaHash": "caf1739226335213f8d0a712a285297ecee5bd4782f3483d7a5c036a1c1cfb4b",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "node_modules/.prisma/client",
    ".prisma/client",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"password\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"emailVerified\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"verificationToken\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"phoneNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"phoneVerified\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"otpSecret\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"otpTimestamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reputationScore\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reputationLevel\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offers\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Offer\",\"nativeType\":null,\"relationName\":\"OfferToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"interests\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Interest\",\"nativeType\":null,\"relationName\":\"InterestedUsers\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSessionsAsCreator\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatSessionCreator\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSessionsAsInterested\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatSessionInterested\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notifications\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Notification\",\"nativeType\":null,\"relationName\":\"NotificationToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sentChatMessages\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatMessage\",\"nativeType\":null,\"relationName\":\"SentMessages\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionsAsCurrencyAProvider\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"CurrencyAProvider\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionsAsCurrencyBProvider\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"CurrencyBProvider\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionsWithAgreedFirstPayer\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"AgreedFirstPayer\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionsCancelledBy\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"CancelledByUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionsDisputedBy\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"DisputedByUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyANegotiations\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PayerNegotiation\",\"nativeType\":null,\"relationName\":\"PartyANegotiations\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyBNegotiations\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PayerNegotiation\",\"nativeType\":null,\"relationName\":\"PartyBNegotiations\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentReceivingInfo\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PaymentReceivingInfo\",\"nativeType\":null,\"relationName\":\"UserPaymentReceivingInfo\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"debugReports\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReport\",\"nativeType\":null,\"relationName\":\"UserDebugReports\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedDebugReports\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReport\",\"nativeType\":null,\"relationName\":\"AssignedReports\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"debugReportComments\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportComment\",\"nativeType\":null,\"relationName\":\"DebugReportComments\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"debugStatusChanges\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportStatusHistory\",\"nativeType\":null,\"relationName\":\"DebugStatusChanger\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Offer\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"OfferToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OfferType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyPair\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":\"CAD-IRR\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"baseRate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"adjustmentForLowerRep\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"adjustmentForHigherRep\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"OfferStatus\",\"nativeType\":null,\"default\":\"ACTIVE\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"interests\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Interest\",\"nativeType\":null,\"relationName\":\"InterestToOffer\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSessions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatSessionToOffer\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transaction\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"OfferToTransaction\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Interest\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Offer\",\"nativeType\":null,\"relationName\":\"InterestToOffer\",\"relationFromFields\":[\"offerId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"interestedUserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"interestedUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"InterestedUsers\",\"relationFromFields\":[\"interestedUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"InterestStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"declineReasonCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"chatSession\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatToInterest\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"offerId\",\"interestedUserId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"offerId\",\"interestedUserId\"]}],\"isGenerated\":false},\"ChatSession\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Offer\",\"nativeType\":null,\"relationName\":\"ChatSessionToOffer\",\"relationFromFields\":[\"offerId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userOneId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userOne\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ChatSessionCreator\",\"relationFromFields\":[\"userOneId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userTwoId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userTwo\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ChatSessionInterested\",\"relationFromFields\":[\"userTwoId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"interestId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"interest\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Interest\",\"nativeType\":null,\"relationName\":\"ChatToInterest\",\"relationFromFields\":[\"interestId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"messages\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatMessage\",\"nativeType\":null,\"relationName\":\"ChatMessageToChatSession\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transaction\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"ChatSessionToTransaction\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ChatMessage\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSessionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSession\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatMessageToChatSession\",\"relationFromFields\":[\"chatSessionId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"senderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sender\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"SentMessages\",\"relationFromFields\":[\"senderId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"content\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"isSystemMessage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Notification\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"NotificationToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"NotificationType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"message\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isRead\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"relatedEntityType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"relatedEntityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actorId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actorUsername\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"data\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Transaction\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSessionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyA\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amountA\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyAProviderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyB\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amountB\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyBProviderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"TransactionStatus\",\"nativeType\":null,\"default\":\"AWAITING_FIRST_PAYER_DESIGNATION\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"termsAgreementTimestampPayer1\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"termsAgreementTimestampPayer2\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"agreedFirstPayerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"firstPayerDesignationTimestamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentExpectedByPayer1\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentDeclaredAtPayer1\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentTrackingNumberPayer1\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"firstPaymentConfirmedByPayer2At\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentExpectedByPayer2\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentDeclaredAtPayer2\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentTrackingNumberPayer2\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"secondPaymentConfirmedByPayer1At\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cancellationReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cancelledByUserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"disputeReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"disputedByUserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"disputeResolvedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"disputeResolutionNotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"offer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Offer\",\"nativeType\":null,\"relationName\":\"OfferToTransaction\",\"relationFromFields\":[\"offerId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"chatSession\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChatSession\",\"nativeType\":null,\"relationName\":\"ChatSessionToTransaction\",\"relationFromFields\":[\"chatSessionId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyAProvider\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"CurrencyAProvider\",\"relationFromFields\":[\"currencyAProviderId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currencyBProvider\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"CurrencyBProvider\",\"relationFromFields\":[\"currencyBProviderId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"agreedFirstPayer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"AgreedFirstPayer\",\"relationFromFields\":[\"agreedFirstPayerId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cancelledByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"CancelledByUser\",\"relationFromFields\":[\"cancelledByUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"disputedByUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"DisputedByUser\",\"relationFromFields\":[\"disputedByUserId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"payerNegotiation\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PayerNegotiation\",\"nativeType\":null,\"relationName\":\"PayerNegotiationToTransaction\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"PayerNegotiation\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"negotiationId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transaction\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"nativeType\":null,\"relationName\":\"PayerNegotiationToTransaction\",\"relationFromFields\":[\"transactionId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA_Id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"PartyANegotiations\",\"relationFromFields\":[\"partyA_Id\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB_Id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"PartyBNegotiations\",\"relationFromFields\":[\"partyB_Id\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA_receivingInfoStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ReceivingInfoStatus\",\"nativeType\":null,\"default\":\"PENDING_INPUT\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB_receivingInfoStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ReceivingInfoStatus\",\"nativeType\":null,\"default\":\"PENDING_INPUT\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA_PaymentReceivingInfoId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA_PaymentReceivingInfo\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PaymentReceivingInfo\",\"nativeType\":null,\"relationName\":\"PartyAReceivingInfo\",\"relationFromFields\":[\"partyA_PaymentReceivingInfoId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB_PaymentReceivingInfoId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB_PaymentReceivingInfo\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PaymentReceivingInfo\",\"nativeType\":null,\"relationName\":\"PartyBReceivingInfo\",\"relationFromFields\":[\"partyB_PaymentReceivingInfoId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"systemRecommendedPayerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"systemRecommendationRule\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"systemRecommendationReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"systemRecommendationDetails\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentProposal_PayerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentProposal_ById\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentProposal_Message\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyA_agreedToCurrentProposal\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"partyB_agreedToCurrentProposal\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"negotiationStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"NegotiationStatus\",\"nativeType\":null,\"default\":\"PENDING_RECEIVING_INFO\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"finalizedPayerId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentTimerDueDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"PaymentReceivingInfo\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserPaymentReceivingInfo\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bankName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"accountNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"accountHolderName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isDefaultForUser\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"negotiationsAsPartyA\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PayerNegotiation\",\"nativeType\":null,\"relationName\":\"PartyAReceivingInfo\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"negotiationsAsPartyB\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PayerNegotiation\",\"nativeType\":null,\"relationName\":\"PartyBReceivingInfo\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"DebugReport\":{\"dbName\":\"debug_reports\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reportId\",\"dbName\":\"report_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserDebugReports\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"severity\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportSeverity\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DebugReportStatus\",\"nativeType\":null,\"default\":\"NOT_REVIEWED\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priority\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":3,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"VarChar\",[\"200\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"stepsToReproduce\",\"dbName\":\"steps_to_reproduce\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"expectedBehavior\",\"dbName\":\"expected_behavior\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actualBehavior\",\"dbName\":\"actual_behavior\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"additionalNotes\",\"dbName\":\"additional_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedToId\",\"dbName\":\"assigned_to\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedTo\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"AssignedReports\",\"relationFromFields\":[\"assignedToId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedAt\",\"dbName\":\"assigned_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionId\",\"dbName\":\"session_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentUrl\",\"dbName\":\"current_url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAgent\",\"dbName\":\"user_agent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"viewportWidth\",\"dbName\":\"viewport_width\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"viewportHeight\",\"dbName\":\"viewport_height\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"diagnosticData\",\"dbName\":\"diagnostic_data\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"logs\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userActions\",\"dbName\":\"user_actions\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"clientTimestamp\",\"dbName\":\"client_timestamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serverReceivedAt\",\"dbName\":\"server_received_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"tags\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportTag\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportTag\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"statusHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportStatusHistory\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportStatusHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"comments\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportComment\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportComment\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"DebugReportTag\":{\"dbName\":\"debug_report_tags\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reportId\",\"dbName\":\"report_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"report\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReport\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportTag\",\"relationFromFields\":[\"reportId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tag\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"VarChar\",[\"50\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"reportId\",\"tag\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"reportId\",\"tag\"]}],\"isGenerated\":false},\"DebugReportStatusHistory\":{\"dbName\":\"debug_report_status_history\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reportId\",\"dbName\":\"report_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"report\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReport\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportStatusHistory\",\"relationFromFields\":[\"reportId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"oldStatus\",\"dbName\":\"old_status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newStatus\",\"dbName\":\"new_status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReportStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changedBy\",\"dbName\":\"changed_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changer\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"DebugStatusChanger\",\"relationFromFields\":[\"changedBy\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"comment\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"DebugReportComment\":{\"dbName\":\"debug_report_comments\",\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reportId\",\"dbName\":\"report_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"report\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DebugReport\",\"nativeType\":null,\"relationName\":\"DebugReportToDebugReportComment\",\"relationFromFields\":[\"reportId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"dbName\":\"user_id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"DebugReportComments\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"comment\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isInternal\",\"dbName\":\"is_internal\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"OfferType\":{\"values\":[{\"name\":\"BUY\",\"dbName\":null},{\"name\":\"SELL\",\"dbName\":null}],\"dbName\":null},\"OfferStatus\":{\"values\":[{\"name\":\"ACTIVE\",\"dbName\":null},{\"name\":\"INACTIVE\",\"dbName\":null},{\"name\":\"DEACTIVATED\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null},{\"name\":\"CANCELLED\",\"dbName\":null}],\"dbName\":null},\"TransactionStatus\":{\"values\":[{\"name\":\"AWAITING_FIRST_PAYER_DESIGNATION\",\"dbName\":null},{\"name\":\"AWAITING_FIRST_PAYER_PAYMENT\",\"dbName\":null},{\"name\":\"AWAITING_SECOND_PAYER_CONFIRMATION\",\"dbName\":null},{\"name\":\"AWAITING_SECOND_PAYER_PAYMENT\",\"dbName\":null},{\"name\":\"AWAITING_FIRST_PAYER_CONFIRMATION\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null},{\"name\":\"CANCELLED\",\"dbName\":null},{\"name\":\"DISPUTED\",\"dbName\":null}],\"dbName\":null},\"ReceivingInfoStatus\":{\"values\":[{\"name\":\"PENDING_INPUT\",\"dbName\":null},{\"name\":\"PROVIDED\",\"dbName\":null},{\"name\":\"CONFIRMED_FROM_PROFILE\",\"dbName\":null}],\"dbName\":null},\"NegotiationStatus\":{\"values\":[{\"name\":\"PENDING_RECEIVING_INFO\",\"dbName\":null},{\"name\":\"AWAITING_PARTY_A_RECEIVING_INFO\",\"dbName\":null},{\"name\":\"AWAITING_PARTY_B_RECEIVING_INFO\",\"dbName\":null},{\"name\":\"READY_TO_NEGOTIATE\",\"dbName\":null},{\"name\":\"PENDING_RESPONSE\",\"dbName\":null},{\"name\":\"FINALIZED\",\"dbName\":null},{\"name\":\"EXPIRED\",\"dbName\":null},{\"name\":\"CANCELLED\",\"dbName\":null}],\"dbName\":null},\"InterestStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"ACCEPTED\",\"dbName\":null},{\"name\":\"DECLINED\",\"dbName\":null}],\"dbName\":null},\"NotificationType\":{\"values\":[{\"name\":\"NEW_INTEREST_ON_YOUR_OFFER\",\"dbName\":null},{\"name\":\"YOUR_INTEREST_ACCEPTED\",\"dbName\":null},{\"name\":\"YOUR_INTEREST_DECLINED\",\"dbName\":null},{\"name\":\"CHAT_MESSAGE_RECEIVED\",\"dbName\":null},{\"name\":\"OFFER_STATUS_UPDATED_BY_OWNER\",\"dbName\":null},{\"name\":\"OFFER_STATUS_CHANGED\",\"dbName\":null},{\"name\":\"TRANSACTION_STARTED\",\"dbName\":null},{\"name\":\"TRANSACTION_ACTION_REQUIRED\",\"dbName\":null},{\"name\":\"TRANSACTION_UPDATE\",\"dbName\":null},{\"name\":\"TRANSACTION_PAYMENT_DECLARED\",\"dbName\":null},{\"name\":\"TRANSACTION_PAYMENT_CONFIRMED\",\"dbName\":null},{\"name\":\"TRANSACTION_COMPLETED\",\"dbName\":null},{\"name\":\"TRANSACTION_CANCELLED\",\"dbName\":null},{\"name\":\"TRANSACTION_DISPUTED\",\"dbName\":null},{\"name\":\"TRANSACTION_AUTO_CANCELLED_TIMER\",\"dbName\":null}],\"dbName\":null},\"DebugReportType\":{\"values\":[{\"name\":\"BUG\",\"dbName\":null},{\"name\":\"FEATURE_REQUEST\",\"dbName\":null},{\"name\":\"PERFORMANCE\",\"dbName\":null},{\"name\":\"UI_UX\",\"dbName\":null},{\"name\":\"IMPROVEMENT\",\"dbName\":null},{\"name\":\"QUESTION\",\"dbName\":null},{\"name\":\"OTHER\",\"dbName\":null}],\"dbName\":\"debug_report_type\"},\"DebugReportSeverity\":{\"values\":[{\"name\":\"LOW\",\"dbName\":null},{\"name\":\"MEDIUM\",\"dbName\":null},{\"name\":\"HIGH\",\"dbName\":null},{\"name\":\"CRITICAL\",\"dbName\":null}],\"dbName\":\"debug_report_severity\"},\"DebugReportStatus\":{\"values\":[{\"name\":\"NOT_REVIEWED\",\"dbName\":null},{\"name\":\"IN_PROGRESS\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null},{\"name\":\"ARCHIVED\",\"dbName\":null},{\"name\":\"DUPLICATE\",\"dbName\":null},{\"name\":\"WONT_FIX\",\"dbName\":null}],\"dbName\":\"debug_report_status\"}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined


const { warnEnvConflicts } = require('@prisma/client/runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "node_modules/.prisma/client/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "node_modules/.prisma/client/schema.prisma")
