<template>
  <nav class="pagination-controls" v-if="totalPages > 1">
    <div class="pagination-info">
      <span>Page {{ currentPage }} of {{ totalPages }}</span>
      <select :value="pageSize" @change="changeSize($event)" class="page-size-select">
        <option value="10">10/page</option>
        <option value="20">20/page</option>
        <option value="50">50/page</option>
        <option value="100">100/page</option>
      </select>
    </div>
    <div class="page-buttons">
      <button @click="goTo(1)" :disabled="currentPage === 1" class="btn">First</button>
      <button @click="goTo(currentPage - 1)" :disabled="currentPage === 1" class="btn">Prev</button>
      <template v-for="page in visiblePageNumbers" :key="page">
        <button
          v-if="typeof page === 'number'"
          @click="goTo(page)"
          :class="{ active: page === currentPage }"
          class="btn page-number"
        >
          {{ page }}
        </button>
        <span v-else class="ellipsis">...</span>
      </template>
      <button @click="goTo(currentPage + 1)" :disabled="currentPage === totalPages" class="btn">Next</button>
      <button @click="goTo(totalPages)" :disabled="currentPage === totalPages" class="btn">Last</button>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'DebugReportPagination'
});

const props = defineProps<{
  currentPage: number;
  totalPages: number;
  pageSize: number;
}>();

const emit = defineEmits<{
  'change-page': [page: number];
  'change-page-size': [size: number];
}>();

function goTo(page: number) {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('change-page', page);
  }
}

function changeSize(event: Event) {
  const target = event.target as HTMLSelectElement;
  emit('change-page-size', parseInt(target.value, 10));
}

const visiblePageNumbers = computed(() => {
  const pages: (number | string)[] = [];
  const maxVisible = 5;
  
  if (props.totalPages <= maxVisible + 2) {
    for (let i = 1; i <= props.totalPages; i++) pages.push(i);
    return pages;
  }

  pages.push(1);

  let start = Math.max(2, props.currentPage - Math.floor((maxVisible - 2) / 2));
  let end = Math.min(props.totalPages - 1, props.currentPage + Math.ceil((maxVisible - 2) / 2));
  
  if (props.currentPage < maxVisible - 1) {
    end = maxVisible - 1;
  }
  if (props.currentPage > props.totalPages - (maxVisible - 2)) {
    start = props.totalPages - (maxVisible - 2);
  }

  if (start > 2) pages.push('...');

  for (let i = start; i <= end; i++) {
    if (i > 1 && i < props.totalPages) {
      pages.push(i);
    }
  }

  if (end < props.totalPages - 1) pages.push('...');
  
  pages.push(props.totalPages);
  return pages;
});
</script>

<style scoped>
.pagination-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-base, #e0e0e0);
  font-size: var(--font-size-sm, 0.875rem);
  gap: 1rem;
}

@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.page-size-select {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 4px);
  border: 1px solid var(--border-base, #e0e0e0);
  background-color: var(--bg-surface, #fff);
  font-size: var(--font-size-sm, 0.875rem);
}

.page-buttons {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  flex-wrap: wrap;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-buttons {
    justify-content: center;
  }
}

.btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-base, #e0e0e0);
  background-color: var(--bg-surface, #fff);
  color: var(--text-primary, #333);
  border-radius: var(--radius-md, 6px);
  cursor: pointer;
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn:hover:not(:disabled) {
  background-color: var(--bg-surface-hover, #f8f9fa);
  border-color: var(--primary-300, #93c5fd);
}

.btn:focus {
  outline: 2px solid var(--primary-500, #3b82f6);
  outline-offset: 2px;
}

.btn:disabled {
  color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
  background-color: var(--bg-surface-hover, #f8f9fa);
}

.btn.active {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
  font-weight: var(--font-weight-bold, 700);
}

.page-number {
  min-width: 36px;
  justify-content: center;
}

.ellipsis {
  padding: 0.375rem 0.5rem;
  color: var(--text-tertiary, #6c757d);
}
</style>