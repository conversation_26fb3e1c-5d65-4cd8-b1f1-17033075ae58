<template>
  <div class="create-offer-container offerform-bg">
    <n-spin :show="isLoading">
      <!-- Explanation Card -->
      <n-card class="explanation-card">
        <template #header>
          <button class="card-title explanation-toggle" @click="isExplanationOpen = !isExplanationOpen"
            :aria-expanded="isExplanationOpen" aria-controls="explanation-content" tabindex="0"
            @keydown.enter.prevent="isExplanationOpen = !isExplanationOpen"
            @keydown.space.prevent="isExplanationOpen = !isExplanationOpen" type="button">            <n-icon :component="InformationCircleOutline" size="24"
              style="vertical-align: middle; margin-right: 8px;" />
            {{ t('offerForm.understandingTieredPricing') }}
            <n-icon :component="ChevronForward" class="chevron" :class="{ open: isExplanationOpen }" size="20"
              aria-hidden="true" style="margin-left: 16px; transition: transform 0.3s;" />
          </button>
        </template>
        <transition name="fade-slide" mode="out-in">
          <div v-show="isExplanationOpen" id="explanation-content" class="explanation-content" role="region"
            aria-labelledby="explanation-toggle" key="explanation">            <n-space vertical>
              <p v-html="t('offerForm.baseRateExplanation', { tier: getCurrentTierLabel() })"></p>
              <p v-html="t('offerForm.adjustmentExplanation')"></p>
              <n-grid cols="1" md:cols="2" x-gap="20" y-gap="16" class="tier-grid" style="margin-bottom: 12px;">
                <n-gi v-if="userTier !== 1">
                  <div class="tier-box lower-rep">
                    <n-h3 style="margin-top:0"><n-icon :component="WarningOutline" class="tier-icon" /> {{
                      getLowerTierLabel()
                      }}</n-h3>                    <div class="tier-content">
                      <p v-html="t('offerForm.lowerTierWorseRates')"></p>
                      <p v-html="explanationL1L2DetailText"></p>
                    </div>
                  </div>
                </n-gi>
                <n-gi v-if="userTier !== 5">
                  <div class="tier-box higher-rep">
                    <n-h3 style="margin-top:0"><n-icon :component="CheckmarkCircleOutline" class="tier-icon" /> {{
                      getHigherTierLabel() }}</n-h3>                    <div class="tier-content">
                      <p v-html="t('offerForm.higherTierBetterRates')"></p>
                      <p v-html="explanationL4L5DetailText"></p>
                    </div>
                  </div>
                </n-gi>
              </n-grid>
            </n-space>
          </div>
        </transition>
      </n-card>

      <n-form ref="formRef" :model="form" :rules="rules" label-placement="top" style="width: 100%; max-width: 700px;">        <n-card :title="t('offerForm.offerBasics')" style="margin-bottom: 20px;">          <n-form-item :label="t('offerForm.offerType')" path="type">
            <n-radio-group v-model:value="form.type" class="offer-type-radio-group">
              <n-radio-button value="SELL">{{ t('offerForm.sellCAD') }}</n-radio-button>
              <n-radio-button value="BUY">{{ t('offerForm.buyCAD') }}</n-radio-button>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="t('offerForm.currencyPair')">
            <n-input value="CAD-IRR" disabled />
          </n-form-item>
          <n-form-item :label="t('offerForm.amountCAD')" path="amount">
            <n-input-number v-model:value="form.amount" :min="0.01" :precision="2" style="width: 100%;" />
          </n-form-item>
          <n-form-item :label="t('offerForm.baseExchangeRate')" path="baseRate">
            <n-input-number v-model:value="form.baseRate" :min="0.01" :precision="0"
              :placeholder="t('offerForm.baseRatePlaceholder', { tier: getCurrentTierLabel() })" style="width: 100%;" />
            <template #feedback>
              <div style="min-height: 1.2em;">
                {{ t('offerForm.baseRateFeedback', { tier: getCurrentTierLabel() }) }}
              </div>
            </template>
          </n-form-item>
        </n-card>

        <n-h2 style="font-size: 1.2em; margin-bottom: 10px; margin-top: 30px;">{{ t('offerForm.tieredAdjustments') }}</n-h2>

        <!-- Card for Lower Reputation Users (L1 & L2) -->
        <n-card v-if="userTier !== 1" class="adjustment-card" :bordered="true">
          <template #header>            <n-h3 style="margin:0; display: flex; align-items: center;">
              <n-icon :component="WarningOutline" size="22" style="margin-right: 8px; color: var(--n-warning-color);" />
              {{ t('offerForm.forLowerReputationUsers', { tier: getLowerTierLabel() }) }}
            </n-h3>
          </template>          <n-form-item :label="t('offerForm.definePenaltyPercentage')" path="adjustmentForLowerRep">            <n-input-number v-model:value="form.adjustmentForLowerRep" :precision="2" :min="0" :max="100" :step="0.25"
              :placeholder="t('offerForm.percentagePlaceholder')" suffix="%" class="percentage-input" />
            <template #feedback>
              <div class="helper-text" style="min-height: 1.2em;" v-html="lowerRepHelperText"></div>
            </template>
          </n-form-item>

          <div class="calculated-impact">            <n-h4 style="font-size: 1em; margin-top:0; margin-bottom: 10px;">              <n-icon :component="TrendingDownOutline" size="18"
                style="vertical-align: text-bottom; margin-right: 5px;" />
              {{ t('offerForm.calculatedImpact') }}
            </n-h4>            <template v-if="showCalculatedImpact"><n-space vertical size="small">
                <n-text v-html="t('offerForm.yourBaseRate', { tier: getCurrentTierLabel(), rate: form.baseRate?.toFixed(0) })"></n-text>
                <div>
                  <n-text v-html="t('offerForm.adjustedRate', { tier: getLowerTierLabel(), rate: calcAdjustedRate('lower') })"></n-text>
                  <span :class="lowerRepRateDirectionClass" style="margin-left: 5px; font-weight: bold;">
                    <n-icon :component="lowerRepRateIcon" size="16" style="vertical-align: middle;" />
                    {{ lowerRepRateDirectionText }}
                  </span>
                </div>
                <n-tag :type="form.adjustmentForLowerRep > 0 ? 'error' : 'default'" size="small"
                  style="margin-top: 5px;">
                  {{ lowerRepTagText }}
                </n-tag>
              </n-space>            </template>
            <template v-else>
              <n-empty :description="t('offerForm.selectOfferTypeAndRate')" size="small" />
            </template>
          </div>
        </n-card>

        <!-- Card for Higher Reputation Users (L4 & L5) -->
        <n-card v-if="userTier !== 5" class="adjustment-card" :bordered="true" style="margin-top: 20px;">
          <template #header>            <n-h3 style="margin:0; display: flex; align-items: center;">
              <n-icon :component="CheckmarkCircleOutline" size="22"
                style="margin-right: 8px; color: var(--n-success-color);" />
              {{ t('offerForm.forHigherReputationUsers', { tier: getHigherTierLabel() }) }}
            </n-h3>
          </template>          <n-form-item :label="t('offerForm.defineBonusPercentage')" path="adjustmentForHigherRep">            <n-input-number v-model:value="form.adjustmentForHigherRep" :precision="2" :min="0" :max="100" :step="0.25"
              :placeholder="t('offerForm.percentagePlaceholder')" suffix="%" class="percentage-input" />
            <template #feedback>
              <div class="helper-text" style="min-height: 1.2em;" v-html="higherRepHelperText"></div>
            </template>
          </n-form-item>

          <div class="calculated-impact">            <n-h4 style="font-size: 1em; margin-top:0; margin-bottom: 10px;">              <n-icon :component="TrendingUpOutline" size="18"
                style="vertical-align: text-bottom; margin-right: 5px;" />
              {{ t('offerForm.calculatedImpact') }}
            </n-h4>            <template v-if="showCalculatedImpact"><n-space vertical size="small">
                <n-text v-html="t('offerForm.yourBaseRate', { tier: getCurrentTierLabel(), rate: form.baseRate?.toFixed(0) })"></n-text>
                <div>
                  <n-text v-html="t('offerForm.adjustedRate', { tier: getHigherTierLabel(), rate: calcAdjustedRate('higher') })"></n-text>
                  <span :class="higherRepRateDirectionClass" style="margin-left: 5px; font-weight: bold;">
                    <n-icon :component="higherRepRateIcon" size="16" style="vertical-align: middle;" />
                    {{ higherRepRateDirectionText }}
                  </span>
                </div>
                <n-tag :type="form.adjustmentForHigherRep > 0 ? 'success' : 'default'" size="small"
                  style="margin-top: 5px;">
                  {{ higherRepTagText }}
                </n-tag>
              </n-space>
            </template>            <template v-else>
              <n-empty :description="t('offerForm.selectOfferTypeAndRate')" size="small" />
            </template>
          </div>
        </n-card>
        <n-form-item style="margin-top: 30px;">
          <n-button type="primary" :loading="isSubmitting" @click="handleSubmit" block>{{ submitLabel }}</n-button>
        </n-form-item>
      </n-form>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { NForm, NFormItem, NInput, NInputNumber, NButton, NRadioGroup, NRadioButton, NPageHeader, NSpace, NCard, NGrid, NGi, NIcon, NTag, useMessage, NEmpty, NH2, NH3, NH4, NText, NSpin } from 'naive-ui';
import { z } from 'zod';
import {
  CheckmarkCircleOutline, WarningOutline, InformationCircleOutline, ArrowUpOutline, ArrowDownOutline, TrendingUpOutline, TrendingDownOutline, ChevronForward
} from '@vicons/ionicons5';
import { useTranslation } from '@/composables/useTranslation';

// Translation composable
const { t } = useTranslation();

// Collapsible explanation state
const isExplanationOpen = ref(true);

const props = defineProps<{
  initialValues?: {
    type: 'BUY' | 'SELL' | null,
    amount: number | null,
    baseRate: number | null,
    adjustmentForLowerRep: number,
    adjustmentForHigherRep: number,
  },
  mode: 'create' | 'edit',
  loading?: boolean,
  userTier?: number // 1-5, optional, for dynamic tier labeling
}>();
const emit = defineEmits(['submit']);

const formRef = ref();
const isSubmitting = ref(false);
const isLoading = computed(() => props.loading ?? false);

const form = ref({
  type: props.initialValues?.type ?? null,
  amount: props.initialValues?.amount ?? null,
  baseRate: props.initialValues?.baseRate ?? null,
  adjustmentForLowerRep: props.initialValues?.adjustmentForLowerRep ?? 0,
  adjustmentForHigherRep: props.initialValues?.adjustmentForHigherRep ?? 0,
});

watch(() => props.initialValues, (val) => {
  if (val) {
    form.value = { ...val };
  }
});

const offerSchema = z.object({
  type: z.enum(['BUY', 'SELL'], { required_error: t('offerForm.validation.offerTypeRequired') }),
  amount: z.number({ required_error: t('offerForm.validation.amountRequired') }).positive(t('offerForm.validation.amountPositive')),
  baseRate: z.number({ required_error: t('offerForm.validation.baseRateRequired') }).positive(t('offerForm.validation.baseRatePositive')),
  adjustmentForLowerRep: z.number().min(0, t('offerForm.validation.adjustmentNonNegative')).max(100, t('offerForm.validation.adjustmentMaxHundred')).default(0),
  adjustmentForHigherRep: z.number().min(0, t('offerForm.validation.adjustmentNonNegative')).max(100, t('offerForm.validation.adjustmentMaxHundred')).default(0),
});

const rules = computed(() => ({
  type: [
    { required: true, message: t('offerForm.validation.offerTypeRequired'), trigger: ['blur', 'change'] }
  ],
  amount: [
    { required: true, type: 'number' as const, message: t('offerForm.validation.amountRequired'), trigger: ['blur', 'input'] },
    { type: 'number' as const, min: 0.01, message: t('offerForm.validation.amountPositive'), trigger: ['blur', 'input'] }
  ],
  baseRate: [
    { required: true, type: 'number' as const, message: t('offerForm.validation.baseRateRequired'), trigger: ['blur', 'input'] },
    { type: 'number' as const, min: 0.01, message: t('offerForm.validation.baseRatePositive'), trigger: ['blur', 'input'] }
  ],
  adjustmentForLowerRep: [
    { type: 'number' as const, min: 0, message: t('offerForm.validation.adjustmentNonNegative'), trigger: ['input', 'blur'] },
    { type: 'number' as const, max: 100, message: t('offerForm.validation.adjustmentMaxHundred'), trigger: ['input', 'blur'] }
  ],
  adjustmentForHigherRep: [
    { type: 'number' as const, min: 0, message: t('offerForm.validation.adjustmentNonNegative'), trigger: ['input', 'blur'] },
    { type: 'number' as const, max: 100, message: t('offerForm.validation.adjustmentMaxHundred'), trigger: ['input', 'blur'] }
  ],
}));

function getCurrentTierLabel() {
  return t('offerForm.tierLabel', { tier: props.userTier || 3 });
}

// Dynamic tier label helpers
function getLowerTierLabel() {
  if (!props.userTier) return t('offerForm.lowerTiersDefault');
  if (props.userTier === 1) return t('offerForm.noLowerTiers');
  if (props.userTier === 2) return t('offerForm.tierSingle', { tier: 1 });
  if (props.userTier === 3) return t('offerForm.tiersRange', { start: 1, end: 2 });
  if (props.userTier === 4) return t('offerForm.tiersRange', { start: 1, end: 3 });
  if (props.userTier === 5) return t('offerForm.tiersRange', { start: 1, end: 4 });
  return t('offerForm.lowerTiers');
}

function getHigherTierLabel() {
  if (!props.userTier) return t('offerForm.higherTiersDefault');
  if (props.userTier === 5) return t('offerForm.noHigherTiers');
  if (props.userTier === 4) return t('offerForm.tierSingle', { tier: 5 });
  if (props.userTier === 3) return t('offerForm.tiersRange', { start: 4, end: 5 });
  if (props.userTier === 2) return t('offerForm.tiersRange', { start: 3, end: 5 });
  if (props.userTier === 1) return t('offerForm.tiersRange', { start: 2, end: 5 });
  return t('offerForm.higherTiers');
}

const explanationL1L2DetailText = computed(() => {
  if (!form.value.type) return t('offerForm.selectOfferTypeForDetails');
  const lowerLabel = getLowerTierLabel();
  const isNoLowerTiers = lowerLabel === t('offerForm.noLowerTiers');
  
  return form.value.type === 'SELL'
    ? t('offerForm.lowerTierSellExplanation', { 
        lowerLabel, 
        entity: isNoLowerTiers ? t('offerForm.noOne') : t('offerForm.they') 
      })
    : t('offerForm.lowerTierBuyExplanation', { 
        lowerLabel, 
        entity: isNoLowerTiers ? t('offerForm.noOne') : t('offerForm.they') 
      });
});

const explanationL4L5DetailText = computed(() => {
  if (!form.value.type) return t('offerForm.selectOfferTypeForDetails');
  const higherLabel = getHigherTierLabel();
  const isNoHigherTiers = higherLabel === t('offerForm.noHigherTiers');
  
  return form.value.type === 'SELL'
    ? t('offerForm.higherTierSellExplanation', { 
        higherLabel, 
        entity: isNoHigherTiers ? t('offerForm.noOne') : t('offerForm.they') 
      })
    : t('offerForm.higherTierBuyExplanation', { 
        higherLabel, 
        entity: isNoHigherTiers ? t('offerForm.noOne') : t('offerForm.they') 
      });
});

const lowerRepHelperText = computed(() => {
  let detail = '';
  if (form.value.type === 'SELL') {
    detail = t('offerForm.helperDetails.lowerRepSell', { adjustment: form.value.adjustmentForLowerRep });
  } else if (form.value.type === 'BUY') {
    detail = t('offerForm.helperDetails.lowerRepBuy', { adjustment: form.value.adjustmentForLowerRep });
  }
  const tiers = getLowerTierLabel();
  const tiersText = tiers === t('offerForm.noLowerTiers') ? t('offerForm.lowerTierUsers') : tiers;
  return t('offerForm.helperText.lowerRep', { tiersText, detail: detail || t('offerForm.selectOfferTypeForDetails') });
});

const lowerRepRateDirectionText = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForLowerRep === 0) return '';
  return form.value.type === 'SELL' ? t('offerForm.higherRate') : t('offerForm.lowerRate');
});

const lowerRepRateIcon = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForLowerRep === 0) return undefined;
  return form.value.type === 'SELL' ? ArrowUpOutline : ArrowDownOutline;
});

const lowerRepRateDirectionClass = computed(() => {
  return 'rate-direction-worse';
});

const lowerRepTagText = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForLowerRep === 0) return t('offerForm.noAdjustment');
  const adj = form.value.adjustmentForLowerRep.toFixed(2);
  if (form.value.type === 'SELL') return t('offerForm.penaltyPayMore', { adjustment: adj });
  return t('offerForm.penaltyReceiveLess', { adjustment: adj });
});

const higherRepHelperText = computed(() => {
  let detail = '';
  if (form.value.type === 'SELL') {
    detail = t('offerForm.helperDetails.higherRepSell', { adjustment: form.value.adjustmentForHigherRep });
  } else if (form.value.type === 'BUY') {
    detail = t('offerForm.helperDetails.higherRepBuy', { adjustment: form.value.adjustmentForHigherRep });
  }
  const tiers = getHigherTierLabel();
  const tiersText = tiers === t('offerForm.noHigherTiers') ? t('offerForm.higherTierUsers') : tiers;
  return t('offerForm.helperText.higherRep', { tiersText, detail: detail || t('offerForm.selectOfferTypeForDetails') });
});

const higherRepRateDirectionText = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForHigherRep === 0) return '';
  return form.value.type === 'SELL' ? t('offerForm.lowerRate') : t('offerForm.higherRate');
});

const higherRepRateIcon = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForHigherRep === 0) return undefined;
  return form.value.type === 'SELL' ? ArrowDownOutline : ArrowUpOutline;
});

const higherRepRateDirectionClass = computed(() => {
  return 'rate-direction-better';
});

const higherRepTagText = computed(() => {
  if (!form.value.type || !form.value.baseRate || form.value.adjustmentForHigherRep === 0) return t('offerForm.noAdjustment');
  const adj = form.value.adjustmentForHigherRep.toFixed(2);
  if (form.value.type === 'SELL') return t('offerForm.bonusPayLess', { adjustment: adj });
  return t('offerForm.bonusReceiveMore', { adjustment: adj });
});

// Computed properties for calculated rates
const calcLowerAdjustedRate = computed(() => {
  if (!form.value.baseRate || form.value.baseRate <= 0 || !form.value.type) {
    return '-';
  }
  const base = form.value.baseRate;
  const offerType = form.value.type;
  const adjMagnitude = form.value.adjustmentForLowerRep;
  
  // If no adjustment, return base rate
  if (adjMagnitude === 0) return base.toFixed(0);
  
  let effectivePercent = 0;
  if (offerType === 'SELL') {
    effectivePercent = adjMagnitude;
  } else if (offerType === 'BUY') {
    effectivePercent = -adjMagnitude;
  }
  
  const result = base * (1 + (effectivePercent / 100));
  return result.toFixed(0);
});

const calcHigherAdjustedRate = computed(() => {
  if (!form.value.baseRate || form.value.baseRate <= 0 || !form.value.type) {
    return '-';
  }
  const base = form.value.baseRate;
  const offerType = form.value.type;
  const adjMagnitude = form.value.adjustmentForHigherRep;
  
  // If no adjustment, return base rate
  if (adjMagnitude === 0) return base.toFixed(0);
  
  let effectivePercent = 0;
  if (offerType === 'SELL') {
    effectivePercent = -adjMagnitude;
  } else if (offerType === 'BUY') {
    effectivePercent = adjMagnitude;
  }
  
  const result = base * (1 + (effectivePercent / 100));
  return result.toFixed(0);
});

function calcAdjustedRate(tier: 'lower' | 'higher'): string {
  return tier === 'lower' ? calcLowerAdjustedRate.value : calcHigherAdjustedRate.value;
}

const submitLabel = computed(() => props.mode === 'edit' ? t('offerForm.updateOffer') : t('offerForm.createOffer'));

async function handleSubmit() {
  isSubmitting.value = true;
  try {
    await formRef.value?.validate();
    const parsedForm = offerSchema.parse(form.value);
    emit('submit', parsedForm);
  } catch (error) {
    // Validation errors are shown by Naive UI
  } finally {
    isSubmitting.value = false;
  }
}

// Computed property to determine when to show calculated impact
const showCalculatedImpact = computed(() => {
  return form.value.baseRate && form.value.baseRate > 0 && form.value.type;
});
</script>

<style scoped>
/* Add a soft gradient background and more color accents */
.offerform-bg {
  min-height: 100vh;
  transition: background 0.3s ease;
}

[data-theme="light"] .offerform-bg {
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
}

[data-theme="dark"] .offerform-bg {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.create-offer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 1.5rem;
  padding: 20px;
  max-width: 100vw; /* Prevent horizontal overflow */
  box-sizing: border-box;
}

/* Mobile responsive container */
@media (max-width: 768px) {
  .create-offer-container {
    padding: 12px;
    gap: 1rem;
  }
}

:deep(.n-form-item .n-form-item-blank),
:deep(.n-form-item .n-form-item-feedback-wrapper) {
  width: 100%;
}

.explanation-card {
  width: 100%;
  max-width: 700px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* Mobile responsive explanation card */
@media (max-width: 768px) {
  .explanation-card {
    margin-bottom: 16px;
    border-radius: 6px;
  }
  
  .explanation-card :deep(.n-card__content) {
    padding: 12px 16px 16px !important;
  }
}

[data-theme="light"] .explanation-card {
  background: linear-gradient(90deg, #e0e7ff 0%, #f1f5f9 100%);
  border: 1.5px solid #a5b4fc;
  box-shadow: 0 4px 16px 0 rgba(80, 80, 180, 0.15);
}

[data-theme="dark"] .explanation-card {
  background: linear-gradient(90deg, #2a2a3e 0%, #1e1e2e 100%);
  border: 1.5px solid #4c4c6c;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.3);
}

.explanation-card :deep(.n-card__content) {
  padding: 16px 20px 20px;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  display: flex;
  align-items: center;
}

/* Mobile responsive title */
@media (max-width: 768px) {
  .card-title {
    font-size: 1.1rem;
  }
}

[data-theme="light"] .card-title {
  color: #3730a3;
}

[data-theme="dark"] .card-title {
  color: #a5b4fc;
}

.explanation-toggle {
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  padding: 0;
  cursor: pointer;
  transition: background 0.2s;
  outline: none;
  font-family: inherit;
  font-size: inherit;
  min-height: 44px; /* Better touch target for mobile */
}

@media (max-width: 768px) {
  .explanation-toggle {
    min-height: 48px;
    padding: 4px 0;
  }
}

.explanation-toggle:focus {
  box-shadow: 0 0 0 2px #6366f1;
}

[data-theme="light"] .explanation-toggle:focus {
  background: #e0e7ff;
}

[data-theme="dark"] .explanation-toggle:focus {
  background: #2a2a3e;
}

[data-theme="light"] .explanation-toggle:hover {
  background: #f1f5f9;
}

[data-theme="dark"] .explanation-toggle:hover {
  background: #1e1e2e;
}

.chevron {
  transition: transform 0.3s;
}

.chevron.open {
  transform: rotate(90deg);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(.4, 0, .2, 1);
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-8px);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
}

.explanation-content {
  padding-top: 8px;
}

.tier-box {
  border-radius: 8px;
  padding: 14px 16px;
  height: 100%;
  overflow: hidden;
  margin: 0;
  transition: all 0.3s ease;
}

/* Mobile responsive tier boxes */
@media (max-width: 768px) {
  .tier-box {
    border-radius: 6px;
    padding: 12px 14px;
    margin-bottom: 12px;
  }
  
  .tier-box h3 {
    font-size: 1rem;
    margin-bottom: 6px;
  }
  
  .tier-content {
    font-size: 0.88rem;
  }
  
  .tier-icon {
    font-size: 18px;
    margin-right: 4px;
  }
}

[data-theme="light"] .tier-box {
  background: linear-gradient(90deg, #fff7ed 0%, #fef9c3 100%);
  box-shadow: 0 1px 6px 0 rgba(255, 186, 73, 0.07);
}

[data-theme="dark"] .tier-box {
  background: linear-gradient(90deg, #2a2a1a 0%, #2e2e1a 100%);
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .lower-rep {
  border: 1.5px solid var(--n-warning-color-suppl);
  background: linear-gradient(90deg, #fff7ed 0%, #fef9c3 100%);
}

[data-theme="dark"] .lower-rep {
  border: 1.5px solid #4a4a2a;
  background: linear-gradient(90deg, #2a2a1a 0%, #2e2e1a 100%);
}

[data-theme="light"] .higher-rep {
  border: 1.5px solid var(--n-success-color-suppl);
  background: linear-gradient(90deg, #f0fdf4 0%, #bbf7d0 100%);
}

[data-theme="dark"] .higher-rep {
  border: 1.5px solid #2a4a2a;
  background: linear-gradient(90deg, #1a2a1a 0%, #1e2e1e 100%);
}

.tier-box h3 {
  display: flex;
  align-items: center;
  font-size: 1.08rem;
  margin-bottom: 8px;
}

.tier-icon {
  vertical-align: middle;
  margin-right: 6px;
  font-size: 20px;
}

.lower-rep .tier-icon {
  color: var(--n-warning-color);
}

.higher-rep .tier-icon {
  color: var(--n-success-color);
}

.tier-content {
  font-size: 0.93rem;
}

.tier-content p {
  margin: 4px 0;
  line-height: 1.5;
}

.text-worse {
  color: var(--n-error-color);
  font-weight: bold;
}

.text-better {
  color: var(--n-success-color);
  font-weight: bold;
}

.adjustment-card {
  width: 100%;
  max-width: 700px;
  transition: all 0.3s ease;
}

/* Mobile responsive adjustment cards */
@media (max-width: 768px) {
  .adjustment-card {
    margin-bottom: 16px;
    border-radius: 6px;
  }
  
  .adjustment-card :deep(.n-card-header) {
    padding: 10px 14px !important;
  }
  
  .adjustment-card :deep(.n-card__content) {
    padding: 14px !important;
  }
  
  .adjustment-card h3 {
    font-size: 1rem;
  }
}

[data-theme="light"] .adjustment-card {
  background: linear-gradient(90deg, #f1f5f9 0%, #e0e7ff 100%);
  border: 1.5px solid #a5b4fc;
  box-shadow: 0 2px 12px 0 rgba(80, 80, 180, 0.07);
}

[data-theme="dark"] .adjustment-card {
  background: linear-gradient(90deg, #1e1e2e 0%, #2a2a3e 100%);
  border: 1.5px solid #4c4c6c;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.adjustment-card :deep(.n-card-header) {
  padding: 12px 16px;
}

.adjustment-card :deep(.n-card__content) {
  padding: 16px;
}

.helper-text {
  font-size: 0.87em;
  color: var(--n-text-color-disabled);
  margin-top: 4px;
  line-height: 1.4;
}

.helper-text .worse {
  color: var(--n-error-color);
  font-weight: bold;
}

.helper-text .better {
  color: var(--n-success-color);
  font-weight: bold;
}

.calculated-impact {
  margin-top: 15px;
  padding: 12px;
  border-radius: var(--n-border-radius);
  transition: all 0.3s ease;
}

/* Mobile responsive calculated impact section */
@media (max-width: 768px) {
  .calculated-impact {
    margin-top: 12px;
    padding: 10px;
    border-radius: 6px;
  }
  
  .calculated-impact h4 {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }
}

[data-theme="light"] .calculated-impact {
  background: linear-gradient(90deg, #f0fdf4 0%, #e0e7ff 100%);
  border: 1px dashed var(--n-border-color);
}

[data-theme="dark"] .calculated-impact {
  background: linear-gradient(90deg, #1a2a1a 0%, #2a2a3e 100%);
  border: 1px dashed #4c4c6c;
}

.calculated-impact .rate-value {
  font-weight: bold;
  color: var(--n-text-color-base);
}

.calculated-impact .rate-direction-worse {
  color: var(--n-error-color);
}

.calculated-impact .rate-direction-better {
  color: var(--n-success-color);
}

:deep(.n-input-number),
:deep(.n-input) {
  border-radius: 6px;
}

[data-theme="light"] :deep(.n-input-number),
[data-theme="light"] :deep(.n-input) {
  background: #f8fafc;
}

[data-theme="dark"] :deep(.n-input-number),
[data-theme="dark"] :deep(.n-input) {
  background: #2a2a2a;
}

:deep(.n-form-item-label) {
  font-weight: 600 !important;
}

:deep(.n-h2) {
  color: var(--n-title-text-color);
  font-weight: 600;
}

:deep(.n-h3) {
  color: var(--n-title-text-color);
  font-weight: 600;
}

:deep(.n-h4) {
  color: var(--n-title-text-color);
  font-weight: 600;
}

/* Responsive input styling */
.percentage-input {
  width: 150px;
}

.offer-type-radio-group {
  width: 100%;
}

.offer-type-radio-group :deep(.n-radio-group) {
  width: 100%;
}

.offer-type-radio-group :deep(.n-radio-button) {
  flex: 1;
}

.tier-grid {
  width: 100%;
}

@media (max-width: 768px) {
  .tier-grid {
    gap: 12px !important;
  }
  
  .tier-grid :deep(.n-grid) {
    gap: 12px !important;
  }
}

@media (max-width: 768px) {
  .percentage-input {
    width: 100%;
    max-width: 200px;
  }
  
  /* Mobile form improvements */
  :deep(.n-form) {
    max-width: 100% !important;
  }
  
  :deep(.n-card) {
    margin-bottom: 16px !important;
    border-radius: 6px;
  }
  
  :deep(.n-card-header__main) {
    font-size: 1.1rem !important;
  }
  
  :deep(.n-card__content) {
    padding: 14px !important;
  }
  
  /* Mobile form items */
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 0.95rem !important;
    margin-bottom: 6px !important;
  }
  
  /* Mobile buttons */
  :deep(.n-button) {
    height: 44px !important;
    font-size: 1rem !important;
  }
  
  /* Mobile inputs */
  :deep(.n-input),
  :deep(.n-input-number) {
    min-height: 40px;
  }
  
  :deep(.n-input__input-el),
  :deep(.n-input-number-input) {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
    /* Mobile radio buttons */
  :deep(.n-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
  
  :deep(.n-radio-button) {
    flex: 1;
    text-align: center;
    min-height: 44px; /* Better touch target */
    font-size: 1rem;
  }
  
  /* Improve radio button layout on mobile */
  .offer-type-radio-group :deep(.n-radio-group) {
    flex-direction: row !important;
    gap: 8px !important;
  }
  
  .offer-type-radio-group :deep(.n-radio-button) {
    min-height: 44px !important;
  }
  
  /* Mobile headings */
  :deep(.n-h2) {
    font-size: 1.1rem !important;
    margin-bottom: 8px !important;
    margin-top: 20px !important;
  }
  
  /* Mobile helper text */
  .helper-text {
    font-size: 0.85rem !important;
    line-height: 1.3;
  }
  
  /* Mobile grid improvements */
  :deep(.n-grid) {
    gap: 12px !important;
  }
  
  /* Mobile spacing adjustments */
  :deep(.n-space) {
    gap: 8px !important;
  }
    :deep(.n-tag) {
    font-size: 0.85rem;
    padding: 4px 8px;
  }
}

/* Tablet/landscape mobile optimizations */
@media (max-width: 1024px) and (min-width: 769px) {
  .create-offer-container {
    padding: 16px;
  }
  
  :deep(.n-card__content) {
    padding: 16px !important;
  }
  
  .tier-box {
    padding: 12px 14px;
  }
  
  .calculated-impact {
    padding: 10px;
  }
  
  .adjustment-card :deep(.n-card-header) {
    padding: 12px 16px !important;
  }
  
  .adjustment-card :deep(.n-card__content) {
    padding: 16px !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .create-offer-container {
    padding: 8px;
    gap: 0.75rem;
  }
  
  .card-title {
    font-size: 1rem !important;
  }
  
  :deep(.n-card__content) {
    padding: 12px !important;
  }
  
  .tier-box {
    padding: 10px 12px;
  }
  
  .tier-box h3 {
    font-size: 0.95rem;
  }
  
  .tier-content {
    font-size: 0.85rem;
  }
  
  .calculated-impact {
    padding: 8px;
  }
  
  .percentage-input {
    max-width: 100% !important;
  }
  
  :deep(.n-form-item-label) {
    font-size: 0.9rem !important;
  }
  
  .helper-text {
    font-size: 0.8rem !important;
  }
}
</style>
