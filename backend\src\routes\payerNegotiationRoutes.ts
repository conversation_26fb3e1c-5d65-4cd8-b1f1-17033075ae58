import { Hono } from 'hono';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authMiddleware } from '../middleware/auth';
import { PayerNegotiationService } from '../services/payerNegotiationService'; // Import the CLASS, not the instance
import { validateRequest } from '../utils/validateRequest';

// Export a factory function instead of a direct router
export default function createPayerNegotiationRoutes(
  payerNegotiationService: PayerNegotiationService // Accept the service as a parameter
) {
  const router = new Hono();
  const prisma = new PrismaClient(); // Keep your existing Prisma instantiation

  // Apply auth middleware to all routes
  router.use('*', authMiddleware);
  const receiveInfoSchema = z.object({
    bankName: z.string(),
    accountNumber: z.string(),
    accountHolderName: z.string(),
    saveToProfile: z.boolean().optional(),
    isDefaultForUser: z.boolean().optional()
  });

  const proposePayerSchema = z.object({
    proposedPayerId: z.string()
  });

  const proposeFirstPayerSchema = z.object({
    proposedPayerId: z.string().cuid(),
    proposalMessage: z.string().optional()
  });

  // Initialize or get negotiation state - SAME EXACT PATH
  router.get('/payer-negotiation', async (c) => {
    const transactionId = c.req.param('transactionId');
    const { userId } = c.get('jwtPayload');    try {
      let negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId },
        include: {
          partyA_PaymentReceivingInfo: true,
          partyB_PaymentReceivingInfo: true
        }
      });

      if (!negotiation) {
        // Before initializing, ensure the current user is part of the transaction
        const transaction = await prisma.transaction.findUnique({
          where: { id: transactionId },
          select: { currencyAProviderId: true, currencyBProviderId: true }
        });

        if (!transaction || (transaction.currencyAProviderId !== userId && transaction.currencyBProviderId !== userId)) {
          return c.json({ error: 'User not part of this transaction or transaction not found' }, 403);
        }

        if (!transactionId) {
          return c.json({ error: 'Transaction ID is missing' }, 400);
        }
          // Use the injected service instead of the imported singleton
        await payerNegotiationService.initializeNegotiation(transactionId);
        negotiation = await prisma.payerNegotiation.findUnique({
          where: { transactionId },
          include: {
            partyA_PaymentReceivingInfo: true,
            partyB_PaymentReceivingInfo: true
          }
        });
      }
      
      return c.json(negotiation);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Submit receiving info - SAME EXACT PATH
  router.post('/payer-negotiation/receiving-info', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');
    const validation = await validateRequest(c, receiveInfoSchema);
    if (!validation.success) return validation.response;

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }      // Use the injected service
      await payerNegotiationService.submitReceivingInfo(
        negotiation.negotiationId,
        userId,
        validation.data
      );

      const updatedNegotiation = await prisma.payerNegotiation.findUnique({
        where: { negotiationId: negotiation.negotiationId },
        include: {
          partyA_PaymentReceivingInfo: true,
          partyB_PaymentReceivingInfo: true
        }
      });
      return c.json(updatedNegotiation);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Propose first payer - SAME EXACT PATH
  router.post('/payer-negotiation/propose', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');
    const validation = await validateRequest(c, proposeFirstPayerSchema);
    if (!validation.success) return validation.response;

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }      // Use the injected service
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userId,
        validation.data.proposedPayerId,
        validation.data.proposalMessage
      );

      const updatedNegotiation = await prisma.payerNegotiation.findUnique({
        where: { negotiationId: negotiation.negotiationId },
        include: {
          partyA_PaymentReceivingInfo: true,
          partyB_PaymentReceivingInfo: true
        }
      });
      return c.json(updatedNegotiation);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Agree to current proposal - SAME EXACT PATH
  router.post('/payer-negotiation/agree', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }      // Use the injected service
      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userId
      );

      const updatedNegotiation = await prisma.payerNegotiation.findUnique({
        where: { negotiationId: negotiation.negotiationId },
        include: {
          partyA_PaymentReceivingInfo: true,
          partyB_PaymentReceivingInfo: true
        }
      });
      return c.json(updatedNegotiation);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  return router; // Return the configured router
}
