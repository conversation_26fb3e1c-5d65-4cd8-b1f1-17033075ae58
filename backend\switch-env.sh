#!/bin/bash

# Cross-platform environment switcher for Unix-like systems
# Usage: ./switch-env.sh [sqlite|postgres]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}%s${NC}\n" "$2"
}

# Check if environment argument is provided
if [ $# -eq 0 ]; then
    print_color $RED "❌ Error: Environment argument required"
    echo "Usage: $0 [sqlite|postgres]"
    exit 1
fi

ENV=$1

# Validate environment argument
if [ "$ENV" != "sqlite" ] && [ "$ENV" != "postgres" ]; then
    print_color $RED "❌ Error: Invalid environment. Use 'sqlite' or 'postgres'"
    exit 1
fi

print_color $YELLOW "Switching to $ENV environment..."

if [ "$ENV" = "sqlite" ]; then
    # Check if required files exist
    if [ ! -f "prisma/schema.sqlite.template" ]; then
        print_color $RED "❌ Error: prisma/schema.sqlite.template not found"
        exit 1
    fi
    if [ ! -f ".env.sqlite" ]; then
        print_color $RED "❌ Error: .env.sqlite not found"
        exit 1
    fi
    
    # Copy files
    cp "prisma/schema.sqlite.template" "prisma/schema.prisma"
    cp ".env.sqlite" ".env"
    
    print_color $GREEN "✅ Switched to SQLite (local development)"
    print_color $CYAN "   Database: file:./dev.db"
    print_color $CYAN "   Frontend URL: http://localhost:5173"
    
else
    # Check if required files exist
    if [ ! -f "prisma/schema.postgres.template" ]; then
        print_color $RED "❌ Error: prisma/schema.postgres.template not found"
        exit 1
    fi
    if [ ! -f ".env.postgres" ]; then
        print_color $RED "❌ Error: .env.postgres not found"
        exit 1
    fi
    
    # Copy files
    cp "prisma/schema.postgres.template" "prisma/schema.prisma"
    cp ".env.postgres" ".env"
    
    print_color $GREEN "✅ Switched to PostgreSQL (Docker)"
    print_color $CYAN "   Database: PostgreSQL on localhost:5432"
    print_color $CYAN "   Frontend URL: http://localhost"
fi

print_color $YELLOW "Generating Prisma client..."
if ! npx prisma generate; then
    print_color $RED "❌ Error: Prisma client generation failed"
    exit 1
fi

echo ""
print_color $GREEN "🎉 Environment switched to $ENV successfully!"
echo ""
print_color $WHITE "Next steps:"
if [ "$ENV" = "sqlite" ]; then
    print_color $GRAY "  • Run: npm run migrate:sqlite (if needed)"
    print_color $GRAY "  • Run: npm run dev"
    print_color $GRAY "  • Studio: npm run studio:sqlite"
else
    print_color $GRAY "  • Make sure Docker PostgreSQL is running"
    print_color $GRAY "  • Run: npm run migrate:postgres (if needed)"
    print_color $GRAY "  • Run: npm run dev"
    print_color $GRAY "  • Studio: npm run studio:postgres"
fi
