{"reportId":"report_1748856318594_f3uc7rn6b","timestamp":"2025-06-02T09:25:18.594Z","serverReceivedAt":"2025-06-02T09:25:18.594Z","clientTimestamp":"2025-06-02T09:25:18.563Z","sessionId":"session_1748856298353_m2oz7b52g","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"test","logCount":8,"logs":[{"timestamp":"2025-06-02T09:24:58.374Z","level":"INFO","message":"MUNygo application starting","context":{"userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T09:24:58.374Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.383Z","level":"INFO","message":"Socket connection initializing for authenticated user","context":{"userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.395Z","level":"INFO","message":"MUNygo application mounted successfully","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.585Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.574Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.690Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.802Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:25:18.563Z","level":"INFO","message":"User initiated debug report send","context":{"logCount":7,"hasUserNotes":true,"currentPage":"http://localhost:5173/home"},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748858892146_68dyb2d2v","timestamp":"2025-06-02T10:08:12.146Z","serverReceivedAt":"2025-06-02T10:08:12.146Z","clientTimestamp":"2025-06-02T10:08:12.003Z","sessionId":"session_1748858812570_b5i7cxuw1","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","logCount":14,"logs":[{"timestamp":"2025-06-02T10:06:52.597Z","level":"INFO","message":"MUNygo application starting","context":{"userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T10:06:52.597Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.604Z","level":"INFO","message":"Socket connection initializing for authenticated user","context":{"userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.616Z","level":"INFO","message":"MUNygo application mounted successfully","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.813Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.841Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.639Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-02T10:06:53.639Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.640Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.810Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:59.590Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:46.739Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:47.658Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ویژگی جدید"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:48.418Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهینه‌سازی"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:08:12.002Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748864532649_b7z12r0fo","timestamp":"2025-06-02T11:42:12.649Z","serverReceivedAt":"2025-06-02T11:42:12.649Z","clientTimestamp":"2025-06-02T11:42:12.584Z","sessionId":"session_1748864474570_zeszl7q20","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"dsfdsf","logCount":14,"logs":[{"timestamp":"2025-06-02T11:41:14.591Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T11:41:14.591Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.596Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.606Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.785Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.808Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.464Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-02T11:41:15.464Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.465Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.528Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.609Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:39.134Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:56.547Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کمک"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:57.072Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:57.685Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:42:12.583Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748868758189_qer5mlz54","timestamp":"2025-06-02T12:52:38.190Z","serverReceivedAt":"2025-06-02T12:52:38.190Z","clientTimestamp":"2025-06-02T12:52:38.125Z","sessionId":"session_1748868460401_bxqgaqnh1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.101.0-insider Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36","currentUrl":"http://localhost:5173/home","userNotes":"","logCount":45,"logs":[{"timestamp":"2025-06-02T12:47:40.428Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.101.0-insider Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36","url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","timestamp":"2025-06-02T12:47:40.428Z"},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.438Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.587Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.594Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.595Z","level":"WARN","message":"Route access denied - authentication required","context":{"route":"/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","isAuthenticated":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.597Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.731Z","level":"INFO","message":"User action: navigation","context":{"to":"/","from":"/","timestamp":"2025-06-02T12:47:40.731Z"},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:40.731Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/","routeName":"landing"},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:42.357Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:42.390Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/","timestamp":"2025-06-02T12:47:42.390Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:42.391Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.067Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-02T12:47:48.067Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.067Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.168Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.169Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.248Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:57.026Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/create-offer","routeName":"CreateOffer","requiresAuth":true,"requiresPhoneVerified":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:57.274Z","level":"INFO","message":"User action: navigation","context":{"to":"/create-offer","from":"/home","timestamp":"2025-06-02T12:47:57.274Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:47:57.274Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/create-offer","routeName":"CreateOffer"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.357Z","level":"INFO","message":"Creating new offer","context":{"amount":123},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.427Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.434Z","level":"INFO","message":"Offer created successfully","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.469Z","level":"INFO","message":"Route navigation started","context":{"from":"/create-offer","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.478Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.616Z","level":"INFO","message":"User action: navigation","context":{"to":"/my-offers","from":"/create-offer","timestamp":"2025-06-02T12:48:07.616Z"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:48:07.616Z","level":"INFO","message":"Route navigation completed","context":{"from":"/create-offer","to":"/my-offers","routeName":"MyOffers"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:49:10.191Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers","to":"/chat/cmbf35wmm0005vl9kokya0ozg","routeName":"ChatSession","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:49:10.663Z","level":"INFO","message":"User action: navigation","context":{"to":"/chat/cmbf35wmm0005vl9kokya0ozg","from":"/my-offers","timestamp":"2025-06-02T12:49:10.663Z"},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:49:10.663Z","level":"INFO","message":"Route navigation completed","context":{"from":"/my-offers","to":"/chat/cmbf35wmm0005vl9kokya0ozg","routeName":"ChatSession"},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:32.627Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.731Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.731Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.732Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.734Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/chat/cmbf35wmm0005vl9kokya0ozg","timestamp":"2025-06-02T12:50:33.734Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.734Z","level":"INFO","message":"Route navigation completed","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.796Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.858Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:35.348Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:54.349Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:51:02.807Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"نیاز به رفع"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:51:03.549Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:52:38.124Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748877414046_3wmvp1anq","timestamp":"2025-06-02T15:16:54.047Z","serverReceivedAt":"2025-06-02T15:16:54.047Z","clientTimestamp":"2025-06-02T15:16:53.981Z","sessionId":"session_1748877269763_2a1ig8wm8","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"test extra","reportType":"bug","reportSeverity":"high","reportTitle":"test title","reportDescription":"test description","stepsToReproduce":"test steps to reproduce","expectedBehavior":"test expected","actualBehavior":"test real","tags":["خطا","ایده","سرعت"],"hasTags":true,"logCount":12,"logs":[{"timestamp":"2025-06-02T15:14:29.813Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T15:14:29.813Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.826Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.845Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.255Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.307Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:31.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.098Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:34.757Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:45.823Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:46.635Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:47.505Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"high","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748878105697_hkfskybt8","timestamp":"2025-06-02T15:28:25.697Z","serverReceivedAt":"2025-06-02T15:28:25.697Z","clientTimestamp":"2025-06-02T15:28:25.631Z","sessionId":"session_1748877269763_2a1ig8wm8","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/my-offers","userNotes":"اسا اضافی","reportType":"bug","reportSeverity":"medium","reportTitle":"تست عنوان","reportDescription":"تست توضیح","stepsToReproduce":"تست مراحل","expectedBehavior":"تست رفتار مورد انتظار","actualBehavior":"تست رفتار واقعی","tags":["خطا","ایده","سرعت","رابط کاربری","پیشنهاد"],"hasTags":true,"logCount":43,"logs":[{"timestamp":"2025-06-02T15:14:29.813Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T15:14:29.813Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.826Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.845Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.255Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.307Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:31.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.098Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:34.757Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:45.823Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:46.635Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:47.505Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"high","hasTags":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":12,"correlatedLogCount":12,"userActionCount":6,"reportType":"bug","reportSeverity":"high","hasStepsToReproduce":true,"tags":["خطا","ایده","سرعت"]},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:54.060Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748877414046_3wmvp1anq","reportType":"bug"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:22:17.030Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:22:17.606Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:17.679Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/browse-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:22:17.755Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:18.750Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:29.690Z","level":"INFO","message":"Fetching browsable offer by ID","context":{"offerId":"cmbe8iyp40001vlaomppvj1wc"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:29.712Z","level":"INFO","message":"Browsable offer fetched successfully","context":{"offerId":"cmbe8iyp40001vlaomppvj1wc"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:32.357Z","level":"INFO","message":"Route navigation started","context":{"from":"/browse-offers","to":"/chat/cmbeazxls000evlyctlocn2wx","routeName":"ChatSession","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:33.107Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:22:34.972Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:22:55.690Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:07.978Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:08.923Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbeazxls000evlyctlocn2wx","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:09.270Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:10.842Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers","to":"/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","routeName":"EditOffer","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:23:11.009Z","level":"INFO","message":"Fetching offer by ID","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:11.016Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:11.091Z","level":"INFO","message":"Offer fetched successfully","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.745Z","level":"INFO","message":"Updating offer","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0","payload":{"type":"SELL","amount":100,"baseRate":100,"adjustmentForLowerRep":0,"adjustmentForHigherRep":2.25}},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.783Z","level":"INFO","message":"Offer updated successfully","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.794Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.901Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:17.178Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:22.728Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:23.882Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:24.607Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:25.452Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"رابط کاربری"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:26.369Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"پیشنهاد"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:28:25.631Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/my-offers"}]}
{"reportId":"report_1748878263051_lsi74rgj2","timestamp":"2025-06-02T15:31:03.051Z","serverReceivedAt":"2025-06-02T15:31:03.051Z","clientTimestamp":"2025-06-02T15:31:02.961Z","sessionId":"session_1748877269830_i65biscz9","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":null,"reportType":"bug","reportSeverity":"medium","reportTitle":"ظظظ","reportDescription":"zzz","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["خطا","ایده","سرعت","رابط کاربری","پیشنهاد","مستندات"],"hasTags":true,"logCount":19,"logs":[{"timestamp":"2025-06-02T15:14:29.881Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/login","timestamp":"2025-06-02T15:14:29.880Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:29.894Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:29.915Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.305Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.350Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.352Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"login","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.355Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:31.500Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.126Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:23:15.782Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:23:15.876Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:30.606Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:35.092Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:35.736Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:36.432Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:37.015Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"رابط کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:37.843Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"پیشنهاد"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:38.853Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"مستندات"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:31:02.960Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748951567945_ssatgr3jr","timestamp":"2025-06-03T11:52:47.945Z","serverReceivedAt":"2025-06-03T11:52:47.945Z","clientTimestamp":"2025-06-03T11:52:47.890Z","sessionId":"session_1748951516751_h1c4chi3e","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"حوسو اضافی","reportType":"bug","reportSeverity":"medium","reportTitle":"تست حوسو","reportDescription":"توضیح حوسو","stepsToReproduce":"مراحل حوسو","expectedBehavior":"رفتار مورد انتظار حوسو","actualBehavior":"رفتار واقعی حوسو","tags":["فوری","کند"],"hasTags":true,"logCount":10,"logs":[{"timestamp":"2025-06-03T11:51:56.759Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T11:51:56.759Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.761Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.764Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.825Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.852Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:11.504Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:12.657Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کند"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:47.889Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportDetails":{"type":"bug","severity":"medium","title":"تست حوسو","description":"توضیح حوسو","stepsToReproduce":"مراحل حوسو","expectedBehavior":"رفتار مورد انتظار حوسو","actualBehavior":"رفتار واقعی حوسو","additionalNotes":"حوسو اضافی","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1872,"height":991},"timestamp":"2025-06-03T11:52:47.890Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T11:51:56.825Z"},{"action":"navigation","timestamp":"2025-06-03T11:51:57.367Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T11:52:11.504Z","details":{"tag":"فوری"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T11:52:12.657Z","details":{"tag":"کند"}},{"action":"debug-report-submit","timestamp":"2025-06-03T11:52:47.889Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T11:51:56.759Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T11:51:56.759Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.761Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.764Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.825Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.852Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:11.504Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:12.657Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کند"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:47.889Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["فوری","کند"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"VcvymD1koc7_QQghAAAR","socketConnected":true},"piniaStoreSnapshot":{},"captureTimestamp":"2025-06-03T11:52:47.890Z"}}
{"reportId":"report_1748952316266_89q2v0isf","timestamp":"2025-06-03T12:05:16.266Z","serverReceivedAt":"2025-06-03T12:05:16.266Z","clientTimestamp":"2025-06-03T12:05:16.216Z","sessionId":"session_1748952286667_aebupwgwg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"تست 1","reportType":"bug","reportSeverity":"critical","reportTitle":"تست 1","reportDescription":"تست 1","stepsToReproduce":"تست 1","expectedBehavior":"تست 1","actualBehavior":"تست 1","tags":["بهبود","طراحی"],"hasTags":true,"logCount":10,"logs":[{"timestamp":"2025-06-03T12:04:46.682Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:04:46.681Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.684Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.687Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.786Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.812Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:55.191Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:56.112Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:05:16.215Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"critical","title":"تست 1","description":"تست 1","stepsToReproduce":"تست 1","expectedBehavior":"تست 1","actualBehavior":"تست 1","additionalNotes":"تست 1","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1872,"height":991},"timestamp":"2025-06-03T12:05:16.216Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:04:46.786Z"},{"action":"navigation","timestamp":"2025-06-03T12:04:46.938Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:04:55.191Z","details":{"tag":"بهبود"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:04:56.112Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:05:16.215Z","details":{"reportType":"bug","reportSeverity":"critical","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:04:46.682Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:04:46.681Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.684Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.687Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.786Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.812Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:55.191Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:56.112Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:05:16.215Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["بهبود","طراحی"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"r6u2Ny8NSEG_iZWLAAAP","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952286881},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":8,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:05:16.216Z"}}
{"reportId":"report_1748953380614_66y3zstrj","timestamp":"2025-06-03T12:23:00.614Z","serverReceivedAt":"2025-06-03T12:23:00.615Z","clientTimestamp":"2025-06-03T12:23:00.232Z","sessionId":"session_1748952956568_k3i12xb48","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"","reportType":"bug","reportSeverity":"medium","reportTitle":"xzvhpd","reportDescription":"طراحی","stepsToReproduce":"مراحل طراحی","expectedBehavior":"","actualBehavior":"","tags":["طراحی"],"hasTags":true,"logCount":9,"logs":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"medium","title":"xzvhpd","description":"طراحی","stepsToReproduce":"مراحل طراحی","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1241,"height":991},"timestamp":"2025-06-03T12:23:00.232Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:15:56.723Z"},{"action":"navigation","timestamp":"2025-06-03T12:15:57.176Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:22:31.146Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:00.231Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["طراحی"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"W8rvT0uSnfgNqaNDAAAR","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952957026},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":9,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:23:00.232Z"}}
{"reportId":"report_1748953425757_zvd1sxvj2","timestamp":"2025-06-03T12:23:45.757Z","serverReceivedAt":"2025-06-03T12:23:45.757Z","clientTimestamp":"2025-06-03T12:23:45.347Z","sessionId":"session_1748952956568_k3i12xb48","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"","reportType":"bug","reportSeverity":"low","reportTitle":"بهبود","reportDescription":"سیبیس","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["بهبود","روش بهتر"],"hasTags":true,"logCount":14,"logs":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.232Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":9,"correlatedLogCount":9,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":true,"tags":["طراحی"],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":7},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.626Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748953380614_66y3zstrj","reportType":"bug"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:24.264Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:25.906Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"روش بهتر"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:45.345Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"low","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"low","title":"بهبود","description":"سیبیس","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1241,"height":991},"timestamp":"2025-06-03T12:23:45.347Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:15:56.723Z"},{"action":"navigation","timestamp":"2025-06-03T12:15:57.176Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:22:31.146Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:00.231Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:23:24.264Z","details":{"tag":"بهبود"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:23:25.906Z","details":{"tag":"روش بهتر"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:45.345Z","details":{"reportType":"bug","reportSeverity":"low","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.232Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":9,"correlatedLogCount":9,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":true,"tags":["طراحی"],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":7},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.626Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748953380614_66y3zstrj","reportType":"bug"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:24.264Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:25.906Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"روش بهتر"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:45.345Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"low","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["بهبود","روش بهتر"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"W8rvT0uSnfgNqaNDAAAR","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952957026},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":10,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:23:45.347Z"}}
