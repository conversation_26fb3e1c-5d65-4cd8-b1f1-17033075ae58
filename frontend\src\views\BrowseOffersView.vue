<template>
  <n-page-header :title="t('navigation.browse')" />
  <n-space vertical size="large" style="padding-top: 16px;">
    <n-spin :show="isLoading">
      <n-alert v-if="error" type="error">{{ error }}</n-alert>
      <n-empty v-else-if="activeBrowsableOffers.length === 0 && !isLoading" :description="t('offers.noOffersFound')" />
      <!-- Use NGrid for a responsive grid layout -->
      <n-grid v-else :x-gap="12" :y-gap="16" :responsive="'screen'" cols="1 s:2 m:3 l:4 xl:4">
        <n-gi v-for="offer in activeBrowsableOffers" :key="offer.id">
          <OfferSummaryCard :offer="offer" @click="handleOfferClick(offer)" @showInterest="handleShowInterest" />
        </n-gi>
      </n-grid>
    </n-spin>
  </n-space>
  <!-- Modal for Detailed Offer View -->
  <OfferDetailsModal v-model:show="showOfferDetailModal" />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { 
  NPageHeader, 
  NSpin, 
  NAlert, 
  NSpace, 
  NEmpty, 
  NGrid, 
  NGi
} from 'naive-ui';
import OfferSummaryCard from '@/components/OfferSummaryCard.vue';
import OfferDetailsModal from '@/components/OfferDetailsModal.vue';
import { useOfferStore } from '@/stores/offerStore';
import { useInterestStore } from '@/stores/interestStore';
import { useTranslation } from '@/composables/useTranslation';
import { storeToRefs } from 'pinia';
import type { BrowseOffer } from '@/types/offer';
import { offerService } from '@/services/offerService';

const { t } = useTranslation();
const offerStore = useOfferStore();
const interestStore = useInterestStore();
const { activeBrowsableOffers, isLoading, error } = storeToRefs(offerStore);
// Ensure interestStore is fully activated by accessing its state
storeToRefs(interestStore);
const showOfferDetailModal = ref(false);

// Event Handlers
async function handleOfferClick(offer: BrowseOffer) {
  await offerStore.fetchAndDisplayOfferForModal(offer.id);
  showOfferDetailModal.value = true;
}

async function handleShowInterest(offerId: string) {
  await offerService.showInterest(offerId);
}

// Lifecycle
onMounted(async () => {
  offerStore.initializeSocketListeners();
  interestStore.initializeSocketListeners();
  await offerStore.loadOffers();
});

onUnmounted(() => {
  offerStore.cleanup();
  offerStore.clearViewedOfferDetails();
});
</script>

<style scoped>
/* Add any specific styles for BrowseOffersView if needed */
.n-page-header {
  margin-bottom: 16px; /* Add some space below the header */
}
</style>
