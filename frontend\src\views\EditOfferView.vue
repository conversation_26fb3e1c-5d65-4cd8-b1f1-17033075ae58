<template>
  <OfferForm
    v-if="offer"
    :initial-values="form"
    mode="edit"
    :loading="isLoading"
    :user-tier="authStore.user?.reputationLevel ?? 3"
    @submit="handleUpdate"
  />
  <n-empty v-else description="Offer not found or failed to load." />
</template>

<script setup lang="ts">

import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NEmpty, useMessage } from 'naive-ui';
import OfferForm from '@/components/OfferForm.vue';
import { offerService } from '@/services/offerService';
import { ZodError } from 'zod';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const message = useMessage();
const isLoading = ref(true);
const offer = ref<any>(null);
const form = ref({
  type: null as 'BUY' | 'SELL' | null,
  amount: null as number | null,
  baseRate: null as number | null,
  adjustmentForLowerRep: 0,
  adjustmentForHigherRep: 0,
});

onMounted(async () => {
  isLoading.value = true;
  try {    const offerId = route.params.offerId as string;
    const data = await offerService.getOfferById(offerId);
    offer.value = data;
    form.value = {
      type: data.type,
      amount: data.amount,
      baseRate: data.baseRate,
      adjustmentForLowerRep: data.adjustmentForLowerRep,
      adjustmentForHigherRep: data.adjustmentForHigherRep,
    };
  } catch (err) {
    message.error('Failed to load offer.');
  } finally {
    isLoading.value = false;
  }
});

async function handleUpdate(payload: any) {  try {
    await offerService.updateOffer(offer.value.id, payload);
    message.success('Offer updated successfully!');
    router.push({ name: 'MyOffers' });
  } catch (error) {
    if (error instanceof ZodError) {
      message.error('Please check the form for errors.');
    } else {
      message.error('Failed to update offer.');
    }
  }
}
</script>
