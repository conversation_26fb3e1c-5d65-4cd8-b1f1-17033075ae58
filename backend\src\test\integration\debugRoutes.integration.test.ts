import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Hono } from 'hono';
import debugRoutes from '../../routes/debugRoutes';
import { ClientLogService } from '../../services/clientLogService';
import type { ClientReportPayload, ReportDetails, LogEntry, UserContext } from '../../types/schemas/debugSchemas';

// Mock the ClientLogService
vi.mock('../../services/clientLogService');
const MockedClientLogService = vi.mocked(ClientLogService);

describe('Debug Routes Integration Tests', () => {
  let app: Hono;
  let mockClientLogService: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock instance of ClientLogService
    mockClientLogService = {
      saveReport: vi.fn()
    };
    
    MockedClientLogService.mockImplementation(() => mockClientLogService);
    
    // Create test app with debug routes
    app = new Hono();
    app.route('/debug', debugRoutes(mockClientLogService));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('POST /debug/report-issue', () => {
    it('should accept complete bug report and save all form fields', async () => {
      // Mock successful save
      const mockReportId = 'report_integration_test_123';
      mockClientLogService.saveReport.mockResolvedValue(mockReportId);

      // Create comprehensive test payload matching frontend format
      const userContext: UserContext = {
        currentPage: 'http://localhost:5173/create-offer',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 },
        timestamp: '2024-01-15T10:30:00.000Z',
        userActions: [
          { action: 'navigation', timestamp: '2024-01-15T10:29:00.000Z', details: { to: '/create-offer', from: '/browse-offers' } },
          { action: 'form_focus', timestamp: '2024-01-15T10:29:30.000Z', details: { field: 'title' } },
          { action: 'opened_debug_report_modal', timestamp: '2024-01-15T10:30:00.000Z' }
        ],
        routeHistory: ['/home', '/browse-offers', '/create-offer']
      };

      const reportDetails: ReportDetails = {
        type: 'bug',
        severity: 'medium',
        title: 'Submit button not working on offer creation form',
        description: 'When I fill out the offer creation form and click the submit button, nothing happens. The form does not submit and no error message is displayed.',
        stepsToReproduce: '1. Navigate to the Create Offer page\n2. Fill in all required fields (Title: "Test Offer", Description: "Test description", Amount: 100, Currency: USD)\n3. Click the "Submit Offer" button\n4. Observe that nothing happens - no loading state, no error, no success message',
        expectedBehavior: 'The form should submit successfully, create the offer in the database, show a success message, and redirect to the user\'s offers page.',
        actualBehavior: 'The submit button appears to be clicked (brief visual feedback) but then nothing happens. The form remains on screen with the same data, no loading indicator shows, and no error or success message appears.',
        additionalNotes: 'This has been happening for the past 2 days. I\'ve tried on both Chrome and Firefox with the same result. I\'ve also tried clearing my browser cache and cookies but the issue persists. The network tab shows no outgoing requests when I click submit.',
        userContext,
        correlatedLogEntries: [],
        reportTags: ['frontend', 'form-submission', 'critical-flow', 'needs-urgent-fix']
      };

      const logEntries: LogEntry[] = [
        {
          timestamp: '2024-01-15T10:29:00.000Z',
          level: 'INFO',
          message: 'User navigated to create offer page',
          context: { route: '/create-offer', previousRoute: '/browse-offers' },
          url: 'http://localhost:5173/create-offer'
        },
        {
          timestamp: '2024-01-15T10:29:45.000Z',
          level: 'DEBUG',
          message: 'Form validation passed',
          context: { 
            formData: { title: 'Test Offer', description: 'Test description', amount: 100, currency: 'USD' },
            validationResult: { isValid: true, errors: [] }
          },
          url: 'http://localhost:5173/create-offer'
        },
        {
          timestamp: '2024-01-15T10:29:50.000Z',
          level: 'ERROR',
          message: 'Form submission failed - no response from server',
          context: { 
            submitAttempt: 1,
            formData: { title: 'Test Offer', description: 'Test description' },
            error: 'No network request initiated'
          },
          url: 'http://localhost:5173/create-offer',
          stackTrace: 'Error: Submit handler not triggered\n    at OfferForm.vue:245:12\n    at HTMLButtonElement.click'
        }
      ];

      const testPayload: ClientReportPayload = {
        logs: logEntries,
        reportDetails,
        timestamp: '2024-01-15T10:30:00.000Z',
        sessionId: 'session_1705312200000_abc123def'
      };

      // Send request to the debug route
      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPayload)
      });      // Verify response
      expect(response.status).toBe(201);
      
      const responseData = await response.json();
      expect(responseData).toMatchObject({
        success: true,
        message: 'Debug report received successfully. Thank you for helping us improve!',
        reportId: mockReportId
      });

      // Verify that ClientLogService.saveReport was called with the exact payload
      expect(mockClientLogService.saveReport).toHaveBeenCalledTimes(1);
      expect(mockClientLogService.saveReport).toHaveBeenCalledWith(testPayload);

      // Verify that ALL form fields were passed through to the service
      const savedPayload = mockClientLogService.saveReport.mock.calls[0][0] as ClientReportPayload;
      
      expect(savedPayload.reportDetails.type).toBe('bug');
      expect(savedPayload.reportDetails.severity).toBe('medium');
      expect(savedPayload.reportDetails.title).toBe('Submit button not working on offer creation form');
      expect(savedPayload.reportDetails.description).toContain('fill out the offer creation form');
      expect(savedPayload.reportDetails.stepsToReproduce).toContain('Navigate to the Create Offer page');
      expect(savedPayload.reportDetails.expectedBehavior).toContain('should submit successfully');
      expect(savedPayload.reportDetails.actualBehavior).toContain('appears to be clicked');
      expect(savedPayload.reportDetails.additionalNotes).toContain('happening for the past 2 days');
      expect(savedPayload.reportDetails.reportTags).toEqual(['frontend', 'form-submission', 'critical-flow', 'needs-urgent-fix']);
    });

    it('should handle feature request with minimal data correctly', async () => {
      const mockReportId = 'report_feature_456';
      mockClientLogService.saveReport.mockResolvedValue(mockReportId);

      const minimalPayload: ClientReportPayload = {
        logs: [],
        reportDetails: {
          type: 'feature-request',
          severity: 'low',
          title: 'Add export functionality for offers',
          description: 'Please add the ability to export offer data to CSV or Excel format for record keeping purposes.',
          userContext: {
            currentPage: 'http://localhost:5173/my-offers',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
            viewport: { width: 1440, height: 900 },
            timestamp: '2024-01-15T11:00:00.000Z',
            userActions: [],
            routeHistory: ['/my-offers']
          },
          correlatedLogEntries: []
        },
        timestamp: '2024-01-15T11:00:00.000Z',
        sessionId: 'session_feature_request_789'
      };

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },        body: JSON.stringify(minimalPayload)
      });

      expect(response.status).toBe(201);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.reportId).toBe(mockReportId);

      // Verify minimal data was saved correctly
      const savedPayload = mockClientLogService.saveReport.mock.calls[0][0] as ClientReportPayload;
      expect(savedPayload.reportDetails.type).toBe('feature-request');
      expect(savedPayload.reportDetails.title).toBe('Add export functionality for offers');
      expect(savedPayload.reportDetails.description).toContain('export offer data to CSV');
      
      // Verify optional fields are not present
      expect(savedPayload.reportDetails.stepsToReproduce).toBeUndefined();
      expect(savedPayload.reportDetails.expectedBehavior).toBeUndefined();
      expect(savedPayload.reportDetails.actualBehavior).toBeUndefined();
      expect(savedPayload.reportDetails.additionalNotes).toBeUndefined();
      expect(savedPayload.reportDetails.reportTags).toBeUndefined();
    });

    it('should handle performance report with extensive logging data', async () => {
      const mockReportId = 'report_performance_789';
      mockClientLogService.saveReport.mockResolvedValue(mockReportId);

      const performanceLogs: LogEntry[] = [
        {
          timestamp: '2024-01-15T12:00:00.000Z',
          level: 'INFO',
          message: 'Page load started',
          context: { page: '/browse-offers', loadStart: 1705312800000 },
          url: 'http://localhost:5173/browse-offers'
        },
        {
          timestamp: '2024-01-15T12:00:02.000Z',
          level: 'WARN',
          message: 'API response slow',
          context: { 
            endpoint: '/api/offers/browse',
            responseTime: 2000,
            expectedTime: 500
          },
          url: 'http://localhost:5173/browse-offers'
        },
        {
          timestamp: '2024-01-15T12:00:05.000Z',
          level: 'ERROR',
          message: 'API request timeout',
          context: { 
            endpoint: '/api/offers/browse',
            timeout: 5000,
            actualTime: 5100
          },
          url: 'http://localhost:5173/browse-offers',
          stackTrace: 'TimeoutError: Request timeout after 5000ms\n    at apiClient.js:45:12'
        }
      ];

      const performancePayload: ClientReportPayload = {
        logs: performanceLogs,
        reportDetails: {
          type: 'performance',
          severity: 'critical',
          title: 'Browse offers page extremely slow and timing out',
          description: 'The browse offers page is taking more than 5 seconds to load and frequently times out completely, making the application unusable.',
          stepsToReproduce: '1. Navigate to Browse Offers page from the main menu\n2. Wait for the page to load\n3. Observe slow loading times (5+ seconds)\n4. In many cases, the page times out and shows an error',
          expectedBehavior: 'The browse offers page should load within 1-2 seconds and display all available offers without timeouts.',
          actualBehavior: 'Page consistently takes 5+ seconds to load, with frequent timeout errors. When it does load, the user experience is poor due to the delay.',
          additionalNotes: 'This issue started after the recent backend deployment on January 10th. Multiple users have complained about this issue. The problem affects all browsers and devices.',
          userContext: {
            currentPage: 'http://localhost:5173/browse-offers',
            userAgent: 'Mozilla/5.0 (performance test agent)',
            viewport: { width: 1200, height: 800 },
            timestamp: '2024-01-15T12:00:00.000Z',
            userActions: [
              { action: 'page_load_start', timestamp: '2024-01-15T12:00:00.000Z', details: { page: '/browse-offers' } },
              { action: 'api_request_initiated', timestamp: '2024-01-15T12:00:01.000Z', details: { endpoint: '/api/offers/browse' } },
              { action: 'timeout_detected', timestamp: '2024-01-15T12:00:05.000Z', details: { duration: 5000 } }
            ],
            routeHistory: ['/home', '/browse-offers']
          },
          correlatedLogEntries: performanceLogs,
          reportTags: ['performance', 'critical', 'api-timeout', 'post-deployment']
        },
        timestamp: '2024-01-15T12:00:10.000Z',
        sessionId: 'session_performance_critical_999'
      };

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },        body: JSON.stringify(performancePayload)
      });

      expect(response.status).toBe(201);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(true);

      // Verify performance data was saved with full context
      const savedPayload = mockClientLogService.saveReport.mock.calls[0][0] as ClientReportPayload;
      expect(savedPayload.logs).toHaveLength(3);
      expect(savedPayload.logs[2].level).toBe('ERROR');
      expect(savedPayload.logs[2].message).toContain('timeout');
      
      expect(savedPayload.reportDetails.severity).toBe('critical');
      expect(savedPayload.reportDetails.reportTags).toContain('performance');
      expect(savedPayload.reportDetails.reportTags).toContain('api-timeout');
    });

    it('should handle service errors and return appropriate error responses', async () => {
      // Mock service failure
      mockClientLogService.saveReport.mockRejectedValue(new Error('Database connection failed'));

      const testPayload: ClientReportPayload = {
        logs: [],
        reportDetails: {
          type: 'bug',
          severity: 'low',
          title: 'Service error test',
          description: 'Testing service error handling',
          userContext: {
            currentPage: 'http://localhost:5173/test',
            userAgent: 'Test agent',
            viewport: { width: 1024, height: 768 },
            timestamp: '2024-01-15T13:00:00.000Z',
            userActions: [],
            routeHistory: []
          },
          correlatedLogEntries: []
        },
        timestamp: '2024-01-15T13:00:00.000Z',
        sessionId: 'session_error_test'
      };

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPayload)
      });

      // Should return 500 error
      expect(response.status).toBe(500);
        const responseData = await response.json();
      expect(responseData).toMatchObject({
        success: false,
        message: 'Failed to process debug report. Please try again or contact support.'
      });

      // Verify service was called despite the error
      expect(mockClientLogService.saveReport).toHaveBeenCalledTimes(1);
    });

    it('should validate request data format and reject invalid payloads', async () => {
      // Test with invalid payload (missing required fields)
      const invalidPayload = {
        logs: [],
        // Missing reportDetails
        timestamp: '2024-01-15T14:00:00.000Z'
      };

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(invalidPayload)
      });

      // Should return 400 bad request
      expect(response.status).toBe(400);

      // Service should not be called with invalid data
      expect(mockClientLogService.saveReport).not.toHaveBeenCalled();
    });

    it('should preserve data integrity through the entire flow', async () => {
      const mockReportId = 'report_data_integrity_test';
      mockClientLogService.saveReport.mockResolvedValue(mockReportId);

      // Create payload with special characters and edge cases
      const specialCharacterPayload: ClientReportPayload = {
        logs: [
          {
            timestamp: '2024-01-15T15:00:00.000Z',
            level: 'ERROR',
            message: 'Error with special chars: "quotes", \'apostrophes\', and émojis 🚀',
            context: { 
              data: 'Multi-line\nstring with\ttabs and\rcarriage returns',
              unicode: 'Testing unicode: 中文, العربية, 🌟⭐✨'
            },
            url: 'http://localhost:5173/test?param=value&other=123'
          }
        ],
        reportDetails: {
          type: 'bug',
          severity: 'medium',
          title: 'Special characters test: "quotes" & émojis 🐛',
          description: 'Testing data integrity with special characters:\n- Newlines\n- "Double quotes"\n- \'Single quotes\'\n- Unicode: 中文\n- Emojis: 🚀🌟',
          stepsToReproduce: '1. Enter text with "quotes"\n2. Add émojis 🚀\n3. Include unicode: 中文',
          expectedBehavior: 'All characters should be preserved exactly',
          actualBehavior: 'Some characters might be corrupted or escaped incorrectly',
          additionalNotes: 'Edge case testing for: \n\t- JSON serialization\n\t- Database storage\n\t- Unicode handling',
          userContext: {
            currentPage: 'http://localhost:5173/test?special=chars&unicode=中文',
            userAgent: 'Mozilla/5.0 (Special Test Agent)',
            viewport: { width: 1024, height: 768 },
            timestamp: '2024-01-15T15:00:00.000Z',
            userActions: [],
            routeHistory: []
          },
          correlatedLogEntries: [],
          reportTags: ['data-integrity', 'unicode-test', 'special-chars']
        },
        timestamp: '2024-01-15T15:00:00.000Z',
        sessionId: 'session_special_chars_test'
      };

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },        body: JSON.stringify(specialCharacterPayload)
      });

      expect(response.status).toBe(201);

      // Verify that the exact payload (including special characters) was passed to the service
      const savedPayload = mockClientLogService.saveReport.mock.calls[0][0] as ClientReportPayload;
      
      // Check that special characters are preserved
      expect(savedPayload.reportDetails.title).toBe('Special characters test: "quotes" & émojis 🐛');
      expect(savedPayload.reportDetails.description).toContain('Unicode: 中文');
      expect(savedPayload.reportDetails.description).toContain('Emojis: 🚀🌟');
      expect(savedPayload.logs[0].message).toContain('émojis 🚀');
      expect(savedPayload.logs[0].context?.unicode).toBe('Testing unicode: 中文, العربية, 🌟⭐✨');
    });
  });
});
