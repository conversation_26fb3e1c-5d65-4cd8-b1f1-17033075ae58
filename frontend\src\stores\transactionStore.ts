import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import type { Transaction } from '@/types/transaction'; // Ensure this path is correct
import { TransactionStatusEnum } from '@/types/transaction'; // Ensure this path is correct
import type { TransactionStatusUpdatePayload } from '@/types/socketEvents';

// Import API functions from the service
import {
  getTransactionById,
  getTransactionByChatSessionId as apiGetTransactionByChatSessionId,
  agreeToTerms as apiAgreeToTerms,
  designateFirstPayer as apiDesignateFirstPayer,
  declarePayment as apiDeclarePayment,
  confirmReceipt as apiConfirmReceipt,
  cancelTransaction as apiCancelTransaction,
  disputeTransaction as apiDisputeTransaction
} from '@/services/transactionApiService';



import { useAuthStore } from './auth';
import centralizedSocketManager from '@/services/centralizedSocketManager';
// Corrected import for Naive UI provider types
import { type NotificationProviderInst, type MessageProviderInst, type NotificationType } from 'naive-ui';
import { celebrateTransactionCompletion } from '@/utils/confetti';

export const useTransactionStore = defineStore('transaction', () => {  const authStore = useAuthStore();
  // Type for the provider instance, can be Notification or Message provider
  let naiveUiNotificationProvider: NotificationProviderInst | MessageProviderInst | null = null;
  const currentTransaction = ref<Transaction | null>(null);
  const previousStatus = ref<TransactionStatusEnum | null | undefined>(null); // To track status changes
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);
  
  // Socket event unsubscribe function for cleanup
  let transactionUpdateUnsubscribe: (() => void) | null = null;

  function setError(message: string) {
    error.value = message;
    // Assuming naiveUiNotificationProvider is NotificationProviderInst as set by AppContent.vue
    // NotificationProviderInst.error() expects an options object.
    if (naiveUiNotificationProvider && typeof (naiveUiNotificationProvider as NotificationProviderInst).error === 'function') {
      (naiveUiNotificationProvider as NotificationProviderInst).error({
        title: 'Error',
        content: message,
        duration: 4500 // Example duration, adjust as needed
      });
    } else {
      // Fallback if provider is not set or doesn't have an 'error' method.
      console.error("TransactionStore setError (UI notification not shown or provider issue):", message);
    }
  }

  // Function to set the notification instance from a component
  function setNotificationInstance(instance: NotificationProviderInst | MessageProviderInst) {
    naiveUiNotificationProvider = instance;
    console.log('[TransactionStore] Naive UI Notification/Message service instance set.');
  }

  function clearError() {
    error.value = null;
  }

  // --- Getters / Computed ---
  const transactionId = computed(() => currentTransaction.value?.id);
  const currentChatSessionId = computed(() => currentTransaction.value?.chatSessionId);
  const status = computed(() => currentTransaction.value?.status);

  const userRole = computed(() => {
    if (!currentTransaction.value || !authStore.user?.id) return null;
    if (authStore.user.id === currentTransaction.value.currencyAProviderId) return 'currencyAProvider';
    if (authStore.user.id === currentTransaction.value.currencyBProviderId) return 'currencyBProvider';
    return null;
  });

  const isUserTurn = computed(() => {
    if (!currentTransaction.value || !authStore.user?.id || !status.value) return false;
    const tx = currentTransaction.value;
    const userId = authStore.user.id;

    switch (status.value) {
      case TransactionStatusEnum.PENDING_AGREEMENT:
        if (userId === tx.currencyAProviderId && !tx.termsAgreementTimestampPayer1) return true;
        if (userId === tx.currencyBProviderId && !tx.termsAgreementTimestampPayer2) return true;
        if (tx.termsAgreementTimestampPayer1 && tx.termsAgreementTimestampPayer2 && !tx.agreedFirstPayerId) {
          return true;
        }
        return false;
      case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION:
        return (userId === tx.currencyAProviderId || userId === tx.currencyBProviderId) && !tx.agreedFirstPayerId;
      case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
        return userId === tx.agreedFirstPayerId;
      case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
        return tx.agreedFirstPayerId !== null && userId !== tx.agreedFirstPayerId;
      case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
        return tx.agreedFirstPayerId !== null && userId !== tx.agreedFirstPayerId;
      case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
        return userId === tx.agreedFirstPayerId;
      default:
        return false;
    }
  });

  // --- Notification Logic ---
  function determineAndShowNotification(tx: Transaction, oldStatus?: TransactionStatusEnum | null) {
    if (!authStore.user?.id) return;

    if (!naiveUiNotificationProvider) {
      console.warn('[TransactionStore] Notification service not initialized. Cannot show notification.');
      return;
    }

    const newStatus = tx.status;
    if (newStatus === oldStatus) return;

    let notifTitle = 'Transaction Update';
    let notifContent = `Status changed to ${newStatus}`;
    let notifType: NotificationType = 'info';
    let shouldNotify = true;

    const userId = authStore.user.id;
    const isPayerA = userId === tx.currencyAProviderId;
    const isPayerB = userId === tx.currencyBProviderId;
    
    let firstPayerUsername = 'the first payer';
    let secondPayerUsername = 'the other party';

    if (tx.agreedFirstPayerId) {
      if (tx.agreedFirstPayerId === tx.currencyAProviderId) {
        firstPayerUsername = tx.currencyAProvider?.username ?? 'the first payer';
        secondPayerUsername = tx.currencyBProvider?.username ?? 'the other party';
      } else {
        firstPayerUsername = tx.currencyBProvider?.username ?? 'the first payer';
        secondPayerUsername = tx.currencyAProvider?.username ?? 'the other party';
      }
    }
    
    let isFirstDesignatedPayer = false;
    let isSecondDesignatedPayer = false;
    if (tx.agreedFirstPayerId) {
      isFirstDesignatedPayer = userId === tx.agreedFirstPayerId;
      isSecondDesignatedPayer = (isPayerA || isPayerB) && userId !== tx.agreedFirstPayerId;
    }
    
    const isParticipant = isPayerA || isPayerB;

    if (!isParticipant) {
      shouldNotify = false;
    }

    switch (newStatus) {
      case TransactionStatusEnum.PENDING_AGREEMENT:
        notifTitle = 'Action Required';
        if ((isPayerA && !tx.termsAgreementTimestampPayer1) || (isPayerB && !tx.termsAgreementTimestampPayer2)) {
          notifContent = 'Please review and agree to the transaction terms.';
        } else if (tx.termsAgreementTimestampPayer1 && tx.termsAgreementTimestampPayer2 && !tx.agreedFirstPayerId) {
           shouldNotify = false; 
        } else {
          notifContent = 'Transaction is pending agreement from one or both parties.';
        }
        break;
      
      case TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION:
        notifTitle = 'Action Required';
        notifContent = 'Terms agreed. Please designate the first payer.';
        break;

      case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
        notifTitle = isFirstDesignatedPayer ? 'Your Action: Payment Required' : 'Awaiting Payment';
        notifContent = isFirstDesignatedPayer 
          ? `Please make your payment of ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.amountA : tx.amountB} ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyA : tx.currencyB}.`
          : `Waiting for ${firstPayerUsername} to make their payment.`;
        notifType = isFirstDesignatedPayer ? 'warning' : 'info';
        break;

      case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
        notifTitle = isSecondDesignatedPayer ? 'Action Required: Confirm Receipt' : 'Awaiting Confirmation';
        notifContent = isSecondDesignatedPayer 
          ? `${firstPayerUsername} has declared payment. Please confirm receipt.`
          : `Waiting for ${secondPayerUsername} to confirm receipt.`;
        notifType = isSecondDesignatedPayer ? 'warning' : 'info';
        break;

      case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
        notifTitle = isSecondDesignatedPayer ? 'Your Action: Payment Required' : 'Awaiting Payment';
        notifContent = isSecondDesignatedPayer
          ? `Please make your payment of ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.amountB : tx.amountA} ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyB : tx.currencyA}.`
          : `Waiting for ${secondPayerUsername} to make their payment.`;
        notifType = isSecondDesignatedPayer ? 'warning' : 'info';
        break;

      case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
        notifTitle = isFirstDesignatedPayer ? 'Action Required: Confirm Receipt' : 'Awaiting Confirmation';
        notifContent = isFirstDesignatedPayer
          ? `${secondPayerUsername} has declared payment. Please confirm receipt.`
          : `Waiting for ${firstPayerUsername} to confirm receipt.`;
        notifType = isFirstDesignatedPayer ? 'warning' : 'info';
        break;      case TransactionStatusEnum.COMPLETED:
        notifTitle = 'Transaction Completed!';
        notifContent = 'The transaction has been successfully completed by both parties.';
        notifType = 'success';
        
        // Trigger confetti celebration for transaction completion
        try {
          celebrateTransactionCompletion();
          console.log('[TransactionStore] Confetti celebration triggered for transaction completion! 🎉');
        } catch (error) {
          console.error('[TransactionStore] Error triggering confetti celebration:', error);
        }
        break;

      case TransactionStatusEnum.CANCELLED:
        notifTitle = 'Transaction Cancelled';
        notifContent = `The transaction was cancelled. Reason: ${tx.cancellationReason || 'Not specified'}`;
        notifType = 'error';
        break;
      
      case TransactionStatusEnum.DISPUTED:
        notifTitle = 'Transaction Disputed';
        notifContent = `The transaction has been disputed. Reason: ${tx.disputeReason || 'Not specified'}. An admin will review.`;
        notifType = 'error';
        break;
        
      default:
        notifTitle = 'Transaction Status Changed';
        notifContent = `The transaction status is now: ${newStatus}.`;
        notifType = 'info';
        break; 
    }

    if (shouldNotify && naiveUiNotificationProvider && isParticipant) {
      // Ensure the provider has the method for the determined notifType
      const provider = naiveUiNotificationProvider as NotificationProviderInst; // Assuming NotificationProviderInst
      if (typeof provider[notifType] === 'function') {
        provider[notifType]({ 
          title: notifTitle,
          content: notifContent,
          duration: newStatus === TransactionStatusEnum.COMPLETED || newStatus === TransactionStatusEnum.CANCELLED || newStatus === TransactionStatusEnum.DISPUTED ? 7000 : 5000,
          keepAliveOnHover: true
        });
      } else {
        console.warn(`[TransactionStore] Notification provider does not have method for type: ${notifType}`);
      }
    }
  }

  // Helper function to set actionDeadline based on transaction status and specific deadline fields
  function augmentTransactionWithDeadline(txData: Transaction): Transaction {
    const augmentedTx = { ...txData }; // Create a shallow copy

    // Reset or default actionDeadline
    augmentedTx.actionDeadline = null;

    switch (augmentedTx.status) {
      case TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT:
        augmentedTx.actionDeadline = augmentedTx.paymentExpectedByPayer1;
        break;
      case TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT:
        augmentedTx.actionDeadline = augmentedTx.paymentExpectedByPayer2;
        break;
      // If confirmations have their own deadlines from the backend, add cases here:
      // For example:
      // case TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION:
      //   augmentedTx.actionDeadline = augmentedTx.confirmationExpectedByPayer2; // Assuming such a field exists
      //   break;
      // case TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION:
      //   augmentedTx.actionDeadline = augmentedTx.confirmationExpectedByPayer1; // Assuming such a field exists
      //   break;
    }    return augmentedTx;
  }
  // --- Socket Event Handler ---
function handleTransactionUpdate(updatedTransaction: TransactionStatusUpdatePayload) {
    try {
      console.log('[TransactionStore] Received TRANSACTION_STATUS_UPDATED:', updatedTransaction);
      console.log('[TransactionStore] Current transaction status before update:', currentTransaction.value?.status);
      console.log('[TransactionStore] Updated transaction status:', updatedTransaction.status);
      console.log('[TransactionStore] Current transaction BEFORE update:', JSON.stringify(currentTransaction.value, null, 2));
      
      // Debug the condition check
      console.log('[TransactionStore] DEBUG: Checking condition for transaction update');
      console.log('[TransactionStore] DEBUG: currentTransaction.value exists?', !!currentTransaction.value);
      console.log('[TransactionStore] DEBUG: currentTransaction.value.id:', currentTransaction.value?.id);
      console.log('[TransactionStore] DEBUG: updatedTransaction.transactionId:', updatedTransaction.transactionId);
      console.log('[TransactionStore] DEBUG: IDs match?', currentTransaction.value?.id === updatedTransaction.transactionId);
    
      // The socket event sends transactionId, but we need to map it to id for our Transaction interface
      const transactionId = updatedTransaction.transactionId;
      
      if (currentTransaction.value && transactionId === currentTransaction.value.id) {
        const oldStatus = currentTransaction.value.status;
        
        // Map the socket payload to a Transaction object structure
        const mappedTransaction = {
          ...currentTransaction.value, // Keep existing transaction data
          id: transactionId, // Map transactionId to id
          status: updatedTransaction.status,
          agreedFirstPayerId: updatedTransaction.agreedFirstPayerId,
          firstPayerDesignationTimestamp: updatedTransaction.firstPayerDesignationTimestamp,
          paymentExpectedByPayer1: updatedTransaction.paymentExpectedByPayer1,
          paymentDeclaredAtPayer1: updatedTransaction.paymentDeclaredAtPayer1,
          paymentTrackingNumberPayer1: updatedTransaction.paymentTrackingNumberPayer1,
          firstPaymentConfirmedByPayer2At: updatedTransaction.firstPaymentConfirmedByPayer2At,
          paymentExpectedByPayer2: updatedTransaction.paymentExpectedByPayer2,
          paymentDeclaredAtPayer2: updatedTransaction.paymentDeclaredAtPayer2,
          paymentTrackingNumberPayer2: updatedTransaction.paymentTrackingNumberPayer2,
          secondPaymentConfirmedByPayer1At: updatedTransaction.secondPaymentConfirmedByPayer1At,
          cancellationReason: updatedTransaction.cancellationReason,
          cancelledByUserId: updatedTransaction.cancelledByUserId,
          disputeReason: updatedTransaction.disputeReason,
          disputedByUserId: updatedTransaction.disputedByUserId,
          disputeResolvedAt: updatedTransaction.disputeResolvedAt,
          disputeResolutionNotes: updatedTransaction.disputeResolutionNotes,
          updatedAt: updatedTransaction.updatedAt,
          termsAgreementTimestampPayer1: updatedTransaction.termsAgreementTimestampPayer1,
          termsAgreementTimestampPayer2: updatedTransaction.termsAgreementTimestampPayer2,
        };
        
        const augmentedTx = augmentTransactionWithDeadline(mappedTransaction);
        console.log('[TransactionStore] About to assign new transaction object');
        console.log('[TransactionStore] OLD currentTransaction.value status:', currentTransaction.value.status);
        console.log('[TransactionStore] NEW augmentedTx status:', augmentedTx.status);
        
        // Force reactivity by assigning to the ref
        currentTransaction.value = augmentedTx;
        
        console.log('[TransactionStore] Transaction updated. Old status:', oldStatus, 'New status:', currentTransaction.value.status);
        console.log('[TransactionStore] Current transaction AFTER update:', JSON.stringify(currentTransaction.value, null, 2));
        console.log('[TransactionStore] DEBUG: Ref value assigned, checking reactivity trigger...');
        console.log('[TransactionStore] DEBUG: currentTransaction.value ID:', currentTransaction.value.id);
        console.log('[TransactionStore] DEBUG: currentTransaction.value status:', currentTransaction.value.status);
        
        // Add a small delay and check if the value is still correct
        setTimeout(() => {
          console.log('[TransactionStore] DEBUG: After timeout - currentTransaction.value status:', currentTransaction.value?.status);
        }, 100);
        determineAndShowNotification(currentTransaction.value, oldStatus);
        previousStatus.value = oldStatus; // Update previousStatus after using it
      } else if (!currentTransaction.value && updatedTransaction.chatSessionId === currentChatSessionId.value) {
        // If currentTransaction was null but the update matches the expected chat session
        const mappedTransaction = {
          id: updatedTransaction.transactionId,
          offerId: updatedTransaction.offerId,
          chatSessionId: updatedTransaction.chatSessionId,
          status: updatedTransaction.status,
          currencyA: updatedTransaction.currencyA,
          amountA: updatedTransaction.amountA,
          currencyAProviderId: updatedTransaction.currencyAProviderId,
          currencyB: updatedTransaction.currencyB,
          amountB: updatedTransaction.amountB,
          currencyBProviderId: updatedTransaction.currencyBProviderId,
          agreedFirstPayerId: updatedTransaction.agreedFirstPayerId,
          firstPayerDesignationTimestamp: updatedTransaction.firstPayerDesignationTimestamp,
          paymentExpectedByPayer1: updatedTransaction.paymentExpectedByPayer1,
          paymentDeclaredAtPayer1: updatedTransaction.paymentDeclaredAtPayer1,
          paymentTrackingNumberPayer1: updatedTransaction.paymentTrackingNumberPayer1,
          firstPaymentConfirmedByPayer2At: updatedTransaction.firstPaymentConfirmedByPayer2At,
          paymentExpectedByPayer2: updatedTransaction.paymentExpectedByPayer2,
          paymentDeclaredAtPayer2: updatedTransaction.paymentDeclaredAtPayer2,
          paymentTrackingNumberPayer2: updatedTransaction.paymentTrackingNumberPayer2,
          secondPaymentConfirmedByPayer1At: updatedTransaction.secondPaymentConfirmedByPayer1At,
          cancellationReason: updatedTransaction.cancellationReason,
          cancelledByUserId: updatedTransaction.cancelledByUserId,
          disputeReason: updatedTransaction.disputeReason,
          disputedByUserId: updatedTransaction.disputedByUserId,
          disputeResolvedAt: updatedTransaction.disputeResolvedAt,
          disputeResolutionNotes: updatedTransaction.disputeResolutionNotes,
          createdAt: updatedTransaction.createdAt,
          updatedAt: updatedTransaction.updatedAt,
          termsAgreementTimestampPayer1: updatedTransaction.termsAgreementTimestampPayer1,
          termsAgreementTimestampPayer2: updatedTransaction.termsAgreementTimestampPayer2,
        };
        currentTransaction.value = augmentTransactionWithDeadline(mappedTransaction);
        console.log('[TransactionStore] Transaction set from null. New status:', currentTransaction.value.status);
        determineAndShowNotification(currentTransaction.value);
      } else {
        console.log('[TransactionStore] DEBUG: Transaction update NOT applied - conditions not met');
        console.log('[TransactionStore] DEBUG: currentTransaction.value exists?', !!currentTransaction.value);
        console.log('[TransactionStore] DEBUG: currentChatSessionId.value:', currentChatSessionId.value);
        console.log('[TransactionStore] DEBUG: updatedTransaction.chatSessionId:', updatedTransaction.chatSessionId);
      }
    } catch (error) {
      console.error('[TransactionStore] ERROR in handleTransactionUpdate:', error);
      console.error('[TransactionStore] Stack trace:', (error as Error)?.stack);
    }
  }
  // --- Socket Listener Management ---
  function initializeSocketListeners() {
    console.log('[TransactionStore] Setting up socket listeners with centralized socket manager.');
    
    // Clean up previous listeners
    if (transactionUpdateUnsubscribe) {
      transactionUpdateUnsubscribe();
      transactionUpdateUnsubscribe = null;
    }

    if (currentChatSessionId.value) { 
      // Register handler with centralized socket manager
      transactionUpdateUnsubscribe = centralizedSocketManager.on('TRANSACTION_STATUS_UPDATED', handleTransactionUpdate);
      console.log(`[TransactionStore] Listening for TRANSACTION_STATUS_UPDATED on chatSessionId: ${currentChatSessionId.value} (Transaction ID: ${transactionId.value})`);
    } else {
      console.warn("[TransactionStore] currentChatSessionId not available for initializing listeners.");
    }
  }

  function removeSocketListeners() {
    if (transactionUpdateUnsubscribe) {
      transactionUpdateUnsubscribe();
      transactionUpdateUnsubscribe = null;
      console.log('[TransactionStore] Removed TRANSACTION_STATUS_UPDATED listener.');
    }
  }

  watch(currentChatSessionId, (newChatId, oldChatId) => {
    if (newChatId && newChatId !== oldChatId) {
      fetchTransactionByChatSessionId(newChatId);
    } else if (!newChatId) {
      clearTransaction();
    }
  }, { immediate: true }); 

  // --- Actions ---
  async function fetchTransaction(id: string) {
    isLoading.value = true;
    error.value = null;
    try {
      const fetchedTx = await getTransactionById(id);
      
      if (currentTransaction.value) {
        previousStatus.value = currentTransaction.value.status;
      } else {
        previousStatus.value = null;
      }
      
      currentTransaction.value = augmentTransactionWithDeadline(fetchedTx); // Augment here

      if (fetchedTx) {
        initializeSocketListeners(); 
      } else {
        removeSocketListeners(); 
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || 'Failed to fetch transaction';
      if (currentTransaction.value) {
        previousStatus.value = currentTransaction.value.status;
      } else {
        previousStatus.value = null;
      }
      currentTransaction.value = null;
      removeSocketListeners();
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchTransactionByChatSessionId(chatId: string) {
    isLoading.value = true;
    error.value = null;
    try {
      const fetchedTx = await apiGetTransactionByChatSessionId(chatId);
      
      if (currentTransaction.value) {
        previousStatus.value = currentTransaction.value.status;
      } else {
        previousStatus.value = null;
      }
        if (fetchedTx) {
        currentTransaction.value = augmentTransactionWithDeadline(fetchedTx); // Augment here
        initializeSocketListeners();
      } else {
        clearTransaction();
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || 'Failed to fetch transaction by chat session ID';
      if (currentTransaction.value && currentTransaction.value.chatSessionId === chatId) {
        clearTransaction();
      }
    } finally {
      isLoading.value = false;
    }
  }
  async function agreeToTerms() {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiAgreeToTerms(currentTransaction.value.id);
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to agree to terms.');
    } finally {
      isLoading.value = false;
    }
  }
  async function designateFirstPayer(firstPayerId: string) {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiDesignateFirstPayer(currentTransaction.value.id, { designatedPayerId: firstPayerId });
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to designate first payer.');
    } finally {
      isLoading.value = false;
    }
  }
  async function declarePayment(paymentTrackingNumber?: string) {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiDeclarePayment(currentTransaction.value.id, { trackingNumber: paymentTrackingNumber });
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to declare payment.');
    } finally {
      isLoading.value = false;
    }
  }
  async function confirmReceipt() {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiConfirmReceipt(currentTransaction.value.id);
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to confirm receipt.');
    } finally {
      isLoading.value = false;
    }
  }
  async function cancelTransaction(reason: string) {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiCancelTransaction(currentTransaction.value.id, { reason });
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to cancel transaction.');
    } finally {
      isLoading.value = false;
    }
  }
  async function disputeTransaction(reason: string) {
    if (!currentTransaction.value?.id) {
      setError('Transaction ID is missing.');
      return;
    }
    isLoading.value = true;
    clearError();
    try {
      const updatedTx = await apiDisputeTransaction(currentTransaction.value.id, { reason });
      currentTransaction.value = augmentTransactionWithDeadline(updatedTx); // Augment here
      // Notification will be handled by socket event listener
      previousStatus.value = updatedTx.status;
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to dispute transaction.');
    } finally {
      isLoading.value = false;
    }
  }
  function clearTransaction() {
    if (currentTransaction.value) {
        previousStatus.value = currentTransaction.value.status;
    }
    currentTransaction.value = null;
    error.value = null;
    removeSocketListeners();
    console.log('[TransactionStore] Transaction cleared.');
  }

  // Removed status watcher to prevent duplicate notifications
  // Socket event listener is the single source of truth for notifications

  return {
    // State
    currentTransaction,
    isLoading,
    error,
    // Getters
    transactionId,
    currentChatSessionId,
    status,
    userRole,
    isUserTurn,
    // Actions
    setNotificationInstance,
    fetchTransaction,
    fetchTransactionByChatSessionId,
    agreeToTerms,
    designateFirstPayer,
    declarePayment,    confirmReceipt,
    cancelTransaction,
    disputeTransaction,
    clearTransaction,
    clearError, // Export clearError
    setError, // Export setError for direct use if needed
    // Socket related functions
    initializeSocketListeners, 
    removeSocketListeners
  };
});


