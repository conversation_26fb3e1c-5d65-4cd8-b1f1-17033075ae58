<template>
  <section class="filters-section">
    <div class="filters-header">
      <h3>Filters & Search</h3>
      <button @click="showFilters = !showFilters" class="btn-toggle-filters">
        {{ showFilters ? 'Hide' : 'Show' }} Filters
      </button>
    </div>
    <div v-show="showFilters" class="filters-content">
      <div class="filter-grid">
        <div class="filter-item">
          <label for="searchQuery">Search</label>
          <input 
            type="text" 
            id="searchQuery" 
            v-model="currentFilters.searchQuery" 
            placeholder="Search reports..." 
            @input="onSearchInput" 
          />
        </div>
        <div class="filter-item">
          <label for="reportType">Type</label>
          <select id="reportType" v-model="currentFilters.type">
            <option :value="undefined">All Types</option>
            <option v-for="option in reportTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="filter-item">
          <label for="severity">Severity</label>
          <select id="severity" v-model="currentFilters.severity">
            <option :value="undefined">All Severities</option>
            <option v-for="option in severityOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="filter-item date-range">
          <label>Date Range</label>
          <div class="date-inputs">
            <input type="date" v-model="dateStartInput" />
            <span>to</span>
            <input type="date" v-model="dateEndInput" />
          </div>
        </div>
      </div>
      <div class="filter-actions">
        <button @click="apply" class="btn btn-primary">Apply Filters</button>
        <button @click="clear" class="btn btn-outline">Clear All</button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';

interface ReportFiltersPayload {
  searchQuery?: string;
  type?: string;
  severity?: string;
  dateStart?: string;
  dateEnd?: string;
}

interface ReportTypeOption {
  label: string;
  value: string;
}

interface SeverityOption {
  label: string;
  value: string;
}

defineOptions({
  name: 'DebugReportFilters'
});

const props = defineProps<{
  initialFilters: ReportFiltersPayload;
  reportTypeOptions: ReportTypeOption[];
  severityOptions: SeverityOption[];
}>();

const emit = defineEmits<{
  'apply-filters': [filters: ReportFiltersPayload];
  'clear-filters': [];
}>();

const showFilters = ref(true);
const currentFilters = ref<ReportFiltersPayload>({ ...props.initialFilters });
const dateStartInput = ref('');
const dateEndInput = ref('');
let searchDebounceTimer: number | undefined;

function toISODateString(dateString: string): string | undefined {
  return dateString ? new Date(dateString).toISOString() : undefined;
}

function fromISODateString(isoDate?: string): string {
  return isoDate ? isoDate.split('T')[0] : '';
}

onMounted(() => {
  currentFilters.value = { ...props.initialFilters };
  dateStartInput.value = fromISODateString(props.initialFilters.dateStart);
  dateEndInput.value = fromISODateString(props.initialFilters.dateEnd);
});

watch(() => props.initialFilters, (newFilters) => {
  currentFilters.value = { ...newFilters };
  dateStartInput.value = fromISODateString(newFilters.dateStart);
  dateEndInput.value = fromISODateString(newFilters.dateEnd);
}, { deep: true });

function onSearchInput() {
  clearTimeout(searchDebounceTimer);
  searchDebounceTimer = window.setTimeout(() => {
    apply();
  }, 500);
}

function apply() {
  currentFilters.value.dateStart = toISODateString(dateStartInput.value);
  currentFilters.value.dateEnd = toISODateString(dateEndInput.value);
  emit('apply-filters', { ...currentFilters.value });
}

function clear() {
  currentFilters.value = {
    searchQuery: undefined,
    type: undefined,
    severity: undefined,
    dateStart: undefined,
    dateEnd: undefined,
  };
  dateStartInput.value = '';
  dateEndInput.value = '';
  emit('clear-filters');
}
</script>

<style scoped>
.filters-section {
  background-color: var(--bg-surface, #fff);
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: var(--radius-lg, 8px);
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1));
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filters-header h3 {
  margin: 0;
  font-size: var(--font-size-lg, 1.125rem);
  color: var(--text-primary, #333);
}

.btn-toggle-filters {
  background: none;
  border: none;
  color: var(--primary-500, #3b82f6);
  cursor: pointer;
  font-weight: var(--font-weight-medium, 500);
  font-size: var(--font-size-sm, 0.875rem);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 4px);
  transition: background-color 0.2s ease-in-out;
}

.btn-toggle-filters:hover {
  background-color: var(--primary-50, #eff6ff);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .filter-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop (969px - 1199px) */
@media (max-width: 1199px) and (min-width: 969px) {
  .filter-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }
}

/* Tablet (769px - 968px) */
@media (max-width: 968px) and (min-width: 769px) {
  .filters-section {
    padding: 0.875rem;
  }

  .filter-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .filter-item.date-range {
    grid-column: span 2;
  }

  .filter-actions {
    justify-content: center;
  }
}

/* Mobile (481px - 768px) */
@media (max-width: 768px) {
  .filters-section {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .filters-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
    text-align: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .date-range .date-inputs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .date-range span {
    align-self: center;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn {
    width: 100%;
  }
}

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .filters-section {
    padding: 0.625rem;
  }

  .filter-item input[type="text"],
  .filter-item input[type="date"],
  .filter-item select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.625rem;
  }

  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn,
  .btn-toggle-filters {
    min-height: 44px; /* Minimum touch target size */
  }

  .filter-item input[type="text"],
  .filter-item input[type="date"],
  .filter-item select {
    min-height: 44px;
  }

  .btn-toggle-filters:hover,
  .btn:hover {
    background-color: initial;
  }

  .btn:active {
    transform: scale(0.98);
  }
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: 0.25rem;
  font-size: var(--font-size-sm, 0.875rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-primary, #333);
}

.filter-item input[type="text"],
.filter-item input[type="date"],
.filter-item select {
  padding: 0.5rem;
  border: 1px solid var(--border-base, #e0e0e0);
  border-radius: var(--radius-md, 6px);
  font-size: var(--font-size-base, 1rem);
  background-color: var(--bg-surface, #fff);
  color: var(--text-primary, #333);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.filter-item input[type="text"]:focus,
.filter-item input[type="date"]:focus,
.filter-item select:focus {
  outline: none;
  border-color: var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px var(--primary-100, #dbeafe);
}

.date-range .date-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-range span {
  margin: 0 0.25rem;
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--text-secondary, #666);
  flex-shrink: 0;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-base, #e0e0e0);
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
}

.btn-primary:hover {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-500, #3b82f6);
  border-color: var(--primary-500, #3b82f6);
}

.btn-outline:hover {
  background-color: var(--primary-50, #eff6ff);
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .filters-section {
  background-color: var(--bg-surface-dark, #1f2937);
  border: 1px solid var(--border-base-dark, #374151);
}

[data-theme="dark"] .filters-header h3 {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .btn-toggle-filters {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn-toggle-filters:hover {
  background-color: var(--primary-900, #1e3a8a);
}

[data-theme="dark"] .filter-item label {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .filter-item input[type="text"],
[data-theme="dark"] .filter-item input[type="date"],
[data-theme="dark"] .filter-item select {
  background-color: var(--bg-input-dark, #374151);
  border-color: var(--border-base-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .filter-item input[type="text"]:focus,
[data-theme="dark"] .filter-item input[type="date"]:focus,
[data-theme="dark"] .filter-item select:focus {
  border-color: var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px var(--primary-900, #1e3a8a);
}

[data-theme="dark"] .date-range span {
  color: var(--text-secondary-dark, #d1d5db);
}

[data-theme="dark"] .filter-actions {
  border-top-color: var(--border-base-dark, #374151);
}

[data-theme="dark"] .btn-primary {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}

[data-theme="dark"] .btn-primary:hover {
  background-color: var(--primary-700, #1d4ed8);
  border-color: var(--primary-700, #1d4ed8);
}

[data-theme="dark"] .btn-outline {
  color: var(--primary-400, #60a5fa);
  border-color: var(--primary-500, #3b82f6);
}

[data-theme="dark"] .btn-outline:hover {
  background-color: var(--primary-900, #1e3a8a);
}
</style>