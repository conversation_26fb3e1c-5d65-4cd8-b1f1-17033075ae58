param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("sqlite", "postgres")]
    [string]$env
)

Write-Host "Switching to $env environment..." -ForegroundColor Yellow

if ($env -eq "sqlite") {
if (!(Test-Path "prisma\schema.sqlite.template")) {
    Write-Host "❌ Error: prisma\schema.sqlite.template not found" -ForegroundColor Red
    exit 1
}
if (!(Test-Path ".env.sqlite")) {
    Write-Host "❌ Error: .env.sqlite not found" -ForegroundColor Red
    exit 1
}
 Copy-Item "prisma\schema.sqlite.template" "prisma\schema.prisma" -Force
 Copy-Item ".env.sqlite" ".env" -Force
    Write-Host "✅ Switched to SQLite (local development)" -ForegroundColor Green
    Write-Host "   Database: file:./dev.db" -ForegroundColor Cyan
    Write-Host "   Frontend URL: http://localhost:5173" -ForegroundColor Cyan
} else {
if (!(Test-Path "prisma\schema.postgres.template")) {
    Write-Host "❌ Error: prisma\schema.postgres.template not found" -ForegroundColor Red
    exit 1
}
if (!(Test-Path ".env.postgres")) {
    Write-Host "❌ Error: .env.postgres not found" -ForegroundColor Red
    exit 1
}
 Copy-Item "prisma\schema.postgres.template" "prisma\schema.prisma" -Force
 Copy-Item ".env.postgres" ".env" -Force
    Write-Host "✅ Switched to PostgreSQL (Docker)" -ForegroundColor Green
    Write-Host "   Database: PostgreSQL on localhost:5432" -ForegroundColor Cyan
    Write-Host "   Frontend URL: http://localhost" -ForegroundColor Cyan
}

Write-Host "Generating Prisma client..." -ForegroundColor Yellow
Write-Host "Generating Prisma client..." -ForegroundColor Yellow
$generateResult = npx prisma generate
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Prisma client generation failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Environment switched to $env successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor White
if ($env -eq "sqlite") {
    Write-Host "  • Run: npm run migrate:sqlite (if needed)" -ForegroundColor Gray
    Write-Host "  • Run: npm run dev" -ForegroundColor Gray
    Write-Host "  • Studio: npm run studio:sqlite" -ForegroundColor Gray
} else {
    Write-Host "  • Make sure Docker PostgreSQL is running" -ForegroundColor Gray
    Write-Host "  • Run: npm run migrate:postgres (if needed)" -ForegroundColor Gray
    Write-Host "  • Run: npm run dev" -ForegroundColor Gray
    Write-Host "  • Studio: npm run studio:postgres" -ForegroundColor Gray
}
