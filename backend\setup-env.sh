#!/bin/bash

# Cross-platform setup script for MUNygo backend
# This script sets up the development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}%s${NC}\n" "$2"
}

print_color $WHITE "🚀 MUNygo Backend Environment Setup"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_color $RED "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_color $RED "❌ npm is not installed. Please install npm first."
    exit 1
fi

print_color $YELLOW "📦 Installing dependencies..."
if ! npm install; then
    print_color $RED "❌ Failed to install dependencies"
    exit 1
fi

print_color $GREEN "✅ Dependencies installed successfully"

# Check if template files exist
if [ ! -f "prisma/schema.sqlite.template" ]; then
    print_color $RED "❌ Error: prisma/schema.sqlite.template not found"
    exit 1
fi

if [ ! -f "prisma/schema.postgres.template" ]; then
    print_color $RED "❌ Error: prisma/schema.postgres.template not found"
    exit 1
fi

# Check if environment files exist
if [ ! -f ".env.sqlite" ]; then
    print_color $RED "❌ Error: .env.sqlite not found"
    exit 1
fi

if [ ! -f ".env.postgres" ]; then
    print_color $RED "❌ Error: .env.postgres not found"
    exit 1
fi

print_color $YELLOW "🔧 Setting up SQLite environment (default)..."
if ! ./switch-env.sh sqlite; then
    print_color $RED "❌ Failed to set up SQLite environment"
    exit 1
fi

print_color $GREEN "✅ Environment setup completed successfully!"
echo ""
print_color $WHITE "Next steps:"
print_color $CYAN "  • Run 'npm run dev' to start development"
print_color $CYAN "  • Run 'npm run migrate:sqlite' to set up the database"
print_color $CYAN "  • Run 'npm run studio:sqlite' to open Prisma Studio"
echo ""
print_color $WHITE "Environment switching:"
print_color $CYAN "  • npm run env:sqlite  - Switch to SQLite"
print_color $CYAN "  • npm run env:postgres - Switch to PostgreSQL"
echo ""
print_color $GREEN "🎉 Ready to develop!"
