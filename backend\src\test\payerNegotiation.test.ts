import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient, User, Transaction, PayerNegotiation, NegotiationStatus, ReceivingInfoStatus } from '@prisma/client';
import { PayerNegotiationService } from '../services/payerNegotiationService';
import { ChatService } from '../services/chatService';
import { TransactionService } from '../services/transactionService';
import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';
import type { ILogger } from '../utils/logger';

const prisma = new PrismaClient();

// Mock Socket.IO server
const httpServer = createServer();
const io = new SocketIOServer(httpServer);

// Mock logger implementation
const mockLogger: ILogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock services
const mockChatService = {
  addSystemMessageToChat: vi.fn().mockResolvedValue(undefined)
} as unknown as ChatService;

const mockTransactionService = {
  designateFirstPayer: vi.fn().mockResolvedValue(undefined)
} as unknown as TransactionService;

describe('PayerNegotiationService - Counter Proposal System', () => {
  let payerNegotiationService: PayerNegotiationService;
  let userA: User;
  let userB: User;
  let transaction: Transaction;
  let negotiation: PayerNegotiation;  beforeAll(async () => {
const mockNotificationService = {
  io,
      createNotification: vi.fn().mockResolvedValue({}),
      getNotificationsForUser: vi.fn().mockResolvedValue([]),
      markNotificationAsRead: vi.fn().mockResolvedValue({}),
      markAllNotificationsAsRead: vi.fn().mockResolvedValue({ count: 0 }),
    };
    payerNegotiationService = new PayerNegotiationService(prisma, mockChatService, io, mockLogger, mockTransactionService, mockNotificationService as any);
  });

afterAll(async () => {
  await cleanupTestData();
  await prisma.$disconnect();
  io.close();          // <- close Socket.IO
  httpServer.close();  // then close HTTP server
});

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create fresh test data for each test
    await setupTestData();
  });

  afterEach(async () => {
    // Clean up test data after each test to ensure isolation
    await cleanupTestData();
  });

  async function setupTestData() {
    // Create test users
    userA = await prisma.user.create({
      data: {
        email: `usera_${Date.now()}@test.com`,
        password: 'hashedpassword',
        emailVerified: true,
        phoneVerified: true,
        username: `userA_${Date.now()}`,
        reputationLevel: 3,
      },
    });    userB = await prisma.user.create({
      data: {
        email: `userb_${Date.now()}@test.com`,
        password: 'hashedpassword',
        emailVerified: true,
        phoneVerified: true,
        username: `userB_${Date.now()}`,
        reputationLevel: 3,
      },
    });

    // Create test offer
    const offer = await prisma.offer.create({
      data: {
        userId: userA.id,
        type: 'BUY',
        currencyPair: 'CAD-IRR',
        amount: 100,
        baseRate: 880.63,
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0,
        status: 'ACTIVE',
      },
    });

    // Create chat session
    const chatSession = await prisma.chatSession.create({
      data: {
        offerId: offer.id,
        userOneId: userA.id,
        userTwoId: userB.id,
      },
    });

    // Create test transaction
    transaction = await prisma.transaction.create({
      data: {
        currencyAProviderId: userA.id,
        currencyBProviderId: userB.id,
        currencyA: 'CAD',
        currencyB: 'IRR',
        amountA: 100,
        amountB: 88063,
        status: 'AWAITING_FIRST_PAYER_DESIGNATION',
        chatSessionId: chatSession.id,
        offerId: offer.id,
      },
    });

    // Create test payment receiving info for both users
    const receivingInfoA = await prisma.paymentReceivingInfo.create({
      data: {
        userId: userA.id,
        bankName: 'Bank A',
        accountNumber: '*********',
        accountHolderName: 'User A',
        isDefaultForUser: true,
      },
    });

    const receivingInfoB = await prisma.paymentReceivingInfo.create({
      data: {
        userId: userB.id,
        bankName: 'Bank B',
        accountNumber: '*********',
        accountHolderName: 'User B',
        isDefaultForUser: true,
      },
    });

    // Create test payer negotiation with initial system recommendation
    negotiation = await prisma.payerNegotiation.create({
      data: {
        transactionId: transaction.id,
        partyA_Id: userA.id,
        partyB_Id: userB.id,
        partyA_receivingInfoStatus: ReceivingInfoStatus.PROVIDED,
        partyB_receivingInfoStatus: ReceivingInfoStatus.PROVIDED,
        partyA_PaymentReceivingInfoId: receivingInfoA.id,
        partyB_PaymentReceivingInfoId: receivingInfoB.id,
        systemRecommendedPayerId: userA.id,
        systemRecommendationRule: 'CURRENCY',
        systemRecommendationReason: 'User A provides CAD and should pay first',
        currentProposal_PayerId: userA.id,
        currentProposal_ById: 'system',
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        partyA_agreedToCurrentProposal: false,
        partyB_agreedToCurrentProposal: false,
      },
    });
  }  async function cleanupTestData() {
    // Delete in correct order to avoid foreign key violations
    if (negotiation) {
      await prisma.payerNegotiation.deleteMany({
        where: { transactionId: transaction.id }
      });
    }
    if (transaction) {
      // Delete transaction first (this should cascade to related records)
      await prisma.transaction.deleteMany({
        where: { id: transaction.id }
      });
      // Then delete chat session and offer if they still exist
      if (transaction.chatSessionId) {
        await prisma.chatSession.deleteMany({
          where: { id: transaction.chatSessionId }
        }).catch(() => {}); // Ignore if already deleted by cascade
      }
      if (transaction.offerId) {
        await prisma.offer.deleteMany({
          where: { id: transaction.offerId }
        }).catch(() => {}); // Ignore if already deleted by cascade
      }
    }
    if (userA) {
      await prisma.paymentReceivingInfo.deleteMany({
        where: { userId: userA.id }
      });
      await prisma.user.deleteMany({
        where: { id: userA.id }
      });
    }
    if (userB) {
      await prisma.paymentReceivingInfo.deleteMany({
        where: { userId: userB.id }
      });
      await prisma.user.deleteMany({
        where: { id: userB.id }
      });
    }
  }

  describe('Initial System Proposal', () => {
    it('should initialize negotiation with system recommendation', async () => {
      expect(negotiation.currentProposal_ById).toBe('system');
      expect(negotiation.currentProposal_PayerId).toBe(userA.id);
      expect(negotiation.systemRecommendedPayerId).toBe(userA.id);
      expect(negotiation.negotiationStatus).toBe(NegotiationStatus.PENDING_RESPONSE);
      expect(negotiation.partyA_agreedToCurrentProposal).toBe(false);
      expect(negotiation.partyB_agreedToCurrentProposal).toBe(false);
    });

    it('should allow user B to agree to system recommendation', async () => {
      const result = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userB.id
      );

      expect(result.partyB_agreedToCurrentProposal).toBe(true);
      expect(result.partyA_agreedToCurrentProposal).toBe(false);
      expect(result.negotiationStatus).toBe(NegotiationStatus.PENDING_RESPONSE);
      expect(result.finalizedPayerId).toBeNull();
    });

    it('should finalize when both parties agree to system recommendation', async () => {
      // User B agrees first
      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userB.id
      );

      // User A agrees second
      const result = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      expect(result.partyA_agreedToCurrentProposal).toBe(true);
      expect(result.partyB_agreedToCurrentProposal).toBe(true);
      expect(result.negotiationStatus).toBe(NegotiationStatus.FINALIZED);
      expect(result.finalizedPayerId).toBe(userA.id);
      expect(mockTransactionService.designateFirstPayer).toHaveBeenCalledWith(
        transaction.id,
        userA.id,
        userA.id
      );
    });
  });

  describe('User Counter Proposals', () => {    it('should allow user B to counter-propose user B pays first', async () => {
      const result = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I think I should pay first instead'
      );

      expect(result.currentProposal_PayerId).toBe(userB.id);
      expect(result.currentProposal_ById).toBe(userB.id);
      expect(result.currentProposal_Message).toBe('I think I should pay first instead');
      expect(result.partyA_agreedToCurrentProposal).toBe(false);
      expect(result.partyB_agreedToCurrentProposal).toBe(true); // User B automatically agrees to their own proposal
      expect(result.negotiationStatus).toBe(NegotiationStatus.PENDING_RESPONSE);
      expect(mockChatService.addSystemMessageToChat).toHaveBeenCalled();
    });

    it('should allow user A to counter-propose after user B made a proposal', async () => {
      // User B makes initial counter-proposal
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I should pay first'
      );

      // User A makes counter-proposal
      const result = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userA.id,
        userA.id,
        'Actually, I prefer to pay first as originally planned'
      );      expect(result.currentProposal_PayerId).toBe(userA.id);
      expect(result.currentProposal_ById).toBe(userA.id);
      expect(result.currentProposal_Message).toBe('Actually, I prefer to pay first as originally planned');
      expect(result.partyA_agreedToCurrentProposal).toBe(true); // User A automatically agrees to their own proposal
      expect(result.partyB_agreedToCurrentProposal).toBe(false);
      expect(result.negotiationStatus).toBe(NegotiationStatus.PENDING_RESPONSE);
    });

    it('should handle multiple rounds of counter-proposals', async () => {
      // Round 1: User B proposes user B pays
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I should pay first'
      );

      // Round 2: User A counter-proposes user A pays
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userA.id,
        userA.id,
        'No, I should pay first'
      );

      // Round 3: User B counter-proposes user B pays again
      const result = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I insist, let me pay first'
      );      expect(result.currentProposal_PayerId).toBe(userB.id);
      expect(result.currentProposal_ById).toBe(userB.id);
      expect(result.currentProposal_Message).toBe('I insist, let me pay first');
      expect(result.partyA_agreedToCurrentProposal).toBe(false);
      expect(result.partyB_agreedToCurrentProposal).toBe(true); // User B automatically agrees to their own proposal
      expect(result.negotiationStatus).toBe(NegotiationStatus.PENDING_RESPONSE);
    });

    it('should allow agreement on a counter-proposal', async () => {
      // User B makes counter-proposal
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I should pay first'
      );      // User A agrees to the counter-proposal
      const result = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      expect(result.partyA_agreedToCurrentProposal).toBe(true);
      expect(result.partyB_agreedToCurrentProposal).toBe(true); // User B already agreed when they made the proposal
      expect(result.negotiationStatus).toBe(NegotiationStatus.FINALIZED); // Should be finalized since both parties now agree
      expect(result.finalizedPayerId).toBe(userB.id); // User B was the proposed payer
    });

    it('should reset agreement flags when new counter-proposal is made', async () => {
      // User A agrees to system recommendation
      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );      // Verify user A has agreed
      const afterAgreement = await payerNegotiationService.getNegotiation(negotiation.negotiationId);
      expect(afterAgreement).not.toBeNull();
      expect(afterAgreement!.partyA_agreedToCurrentProposal).toBe(true);

      // User B makes counter-proposal, should reset agreement flags
      const result = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'Counter proposal'
      );      expect(result.partyA_agreedToCurrentProposal).toBe(false); // User A's agreement is reset
      expect(result.partyB_agreedToCurrentProposal).toBe(true); // User B automatically agrees to their own proposal
      expect(result.currentProposal_PayerId).toBe(userB.id);
      expect(result.currentProposal_ById).toBe(userB.id);
    });
  });

  describe('Proposal Validation and Edge Cases', () => {
    it('should reject proposal from user not part of negotiation', async () => {
      const outsideUser = await prisma.user.create({
        data: {
          email: `outside_${Date.now()}@test.com`,
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: `outside_${Date.now()}`,
        },
      });

      await expect(
        payerNegotiationService.proposeFirstPayer(
          negotiation.negotiationId,
          outsideUser.id,
          userA.id,
          'I want to propose'
        )
      ).rejects.toThrow('Proposer is not part of this negotiation');

      // Cleanup
      await prisma.user.delete({ where: { id: outsideUser.id } });
    });

    it('should reject proposal for payer not part of negotiation', async () => {
      const outsideUser = await prisma.user.create({
        data: {
          email: `outside_${Date.now()}@test.com`,
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: `outside_${Date.now()}`,
        },
      });

      await expect(
        payerNegotiationService.proposeFirstPayer(
          negotiation.negotiationId,
          userA.id,
          outsideUser.id,
          'Outside user should pay'
        )
      ).rejects.toThrow('Proposed payer is not part of this negotiation');

      // Cleanup
      await prisma.user.delete({ where: { id: outsideUser.id } });
    });    it('should reject proposal when user has not provided payment info', async () => {
      // Modify existing negotiation to simulate user A without payment info
      await prisma.payerNegotiation.update({
        where: { negotiationId: negotiation.negotiationId },
        data: {
          partyA_receivingInfoStatus: ReceivingInfoStatus.PENDING_INPUT,
          negotiationStatus: NegotiationStatus.AWAITING_PARTY_A_RECEIVING_INFO,
        },
      });      await expect(
        payerNegotiationService.proposeFirstPayer(
          negotiation.negotiationId,
          userA.id,
          userA.id,
          'I should pay first'
        )
      ).rejects.toThrow('User must provide their payment receiving information before making a proposal');

      // Reset the negotiation state back to normal for other tests
      await prisma.payerNegotiation.update({
        where: { negotiationId: negotiation.negotiationId },
        data: {
          partyA_receivingInfoStatus: ReceivingInfoStatus.PROVIDED,
          negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        },
      });
    });

    it('should reject proposal when negotiation is finalized', async () => {
      // Finalize the negotiation first
      await prisma.payerNegotiation.update({
        where: { negotiationId: negotiation.negotiationId },
        data: {
          negotiationStatus: NegotiationStatus.FINALIZED,
          finalizedPayerId: userA.id,
        },
      });

      await expect(
        payerNegotiationService.proposeFirstPayer(
          negotiation.negotiationId,
          userB.id,
          userB.id,
          'New proposal'
        )
      ).rejects.toThrow('Cannot make a proposal at this time');
    });

    it('should reject agreement from user not part of negotiation', async () => {
      const outsideUser = await prisma.user.create({
        data: {
          email: `outside_${Date.now()}@test.com`,
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: `outside_${Date.now()}`,
        },
      });

      await expect(
        payerNegotiationService.agreeToProposal(
          negotiation.negotiationId,
          outsideUser.id
        )
      ).rejects.toThrow('User is not part of this negotiation');

      // Cleanup
      await prisma.user.delete({ where: { id: outsideUser.id } });
    });
  });

  describe('Socket Event Emissions', () => {
    it('should emit PROPOSAL_MADE and NEGOTIATION_STATE_UPDATED events when making proposal', async () => {
      const emitSpy = vi.spyOn(io, 'to').mockReturnValue({
        emit: vi.fn()
      } as any);

      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I should pay first'
      );

      expect(emitSpy).toHaveBeenCalledWith(userA.id);
      expect(emitSpy).toHaveBeenCalledWith(userB.id);
    });

    it('should emit NEGOTIATION_STATE_UPDATED when agreeing to proposal', async () => {
      const emitSpy = vi.spyOn(io, 'to').mockReturnValue({
        emit: vi.fn()
      } as any);

      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      expect(emitSpy).toHaveBeenCalledWith(userA.id);
      expect(emitSpy).toHaveBeenCalledWith(userB.id);
    });

    it('should emit NEGOTIATION_FINALIZED when both parties agree', async () => {
      const emitSpy = vi.spyOn(io, 'to').mockReturnValue({
        emit: vi.fn()
      } as any);

      // Both parties agree to finalize
      await payerNegotiationService.agreeToProposal(negotiation.negotiationId, userA.id);
      await payerNegotiationService.agreeToProposal(negotiation.negotiationId, userB.id);

      expect(emitSpy).toHaveBeenCalledWith(userA.id);
      expect(emitSpy).toHaveBeenCalledWith(userB.id);
    });
  });

  describe('Chat Integration', () => {
    it('should send system message to chat when proposal includes message', async () => {
      const proposalMessage = 'I believe I should pay first for this transaction';

      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        proposalMessage
      );

      expect(mockChatService.addSystemMessageToChat).toHaveBeenCalledWith(
        transaction.chatSessionId,
        expect.stringContaining(proposalMessage),
        transaction.id
      );
    });

    it('should not send system message when proposal has no message', async () => {
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id
      );

      expect(mockChatService.addSystemMessageToChat).not.toHaveBeenCalled();
    });

    it('should send system message when user agrees to proposal', async () => {
      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      expect(mockChatService.addSystemMessageToChat).toHaveBeenCalledWith(
        transaction.chatSessionId,
        expect.stringContaining('agreed to the proposal'),
        transaction.id
      );
    });
  });

  describe('Data Integrity', () => {
    it('should maintain proposal history through multiple counter-proposals', async () => {      // Track initial state
      const initial = await payerNegotiationService.getNegotiation(negotiation.negotiationId);
      expect(initial).not.toBeNull();
      expect(initial!.currentProposal_ById).toBe('system');

      // User B proposes
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'User B proposal'
      );      const afterB = await payerNegotiationService.getNegotiation(negotiation.negotiationId);
      expect(afterB).not.toBeNull();
      expect(afterB!.currentProposal_ById).toBe(userB.id);
      expect(afterB!.currentProposal_Message).toBe('User B proposal');

      // User A counter-proposes
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userA.id,
        userA.id,
        'User A counter-proposal'
      );      const afterA = await payerNegotiationService.getNegotiation(negotiation.negotiationId);
      expect(afterA).not.toBeNull();
      expect(afterA!.currentProposal_ById).toBe(userA.id);
      expect(afterA!.currentProposal_Message).toBe('User A counter-proposal');
      expect(afterA!.partyA_agreedToCurrentProposal).toBe(true); // User A automatically agrees to their own proposal
      expect(afterA!.partyB_agreedToCurrentProposal).toBe(false);
    });

    it('should correctly update party agreement status', async () => {
      // User A agrees to system recommendation
      const afterAAgrees = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );
      expect(afterAAgrees.partyA_agreedToCurrentProposal).toBe(true);
      expect(afterAAgrees.partyB_agreedToCurrentProposal).toBe(false);

      // User B also agrees
      const afterBothAgree = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userB.id
      );
      expect(afterBothAgree.partyA_agreedToCurrentProposal).toBe(true);
      expect(afterBothAgree.partyB_agreedToCurrentProposal).toBe(true);
      expect(afterBothAgree.negotiationStatus).toBe(NegotiationStatus.FINALIZED);
    });
  });

  describe('Complex Counter Proposal Scenarios', () => {
    it('should handle scenario where proposer changes their mind', async () => {
      // User B proposes user B pays first
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I will pay first'
      );

      // User B changes their mind and proposes user A pays first
      const result = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userA.id,
        'Actually, you should pay first'
      );

      expect(result.currentProposal_PayerId).toBe(userA.id);
      expect(result.currentProposal_ById).toBe(userB.id);
      expect(result.currentProposal_Message).toBe('Actually, you should pay first');
    });

    it('should handle rapid sequential proposals', async () => {
      // Rapid proposals from both users
      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'Proposal 1'
      );

      await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userA.id,
        userA.id,
        'Proposal 2'
      );

      const finalResult = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'Final proposal'
      );      expect(finalResult.currentProposal_ById).toBe(userB.id);
      expect(finalResult.currentProposal_Message).toBe('Final proposal');
      expect(finalResult.partyA_agreedToCurrentProposal).toBe(false);
      expect(finalResult.partyB_agreedToCurrentProposal).toBe(true); // User B automatically agrees to their own proposal
    });

    it('should handle mixed agreement and counter-proposal scenario', async () => {
      // User A agrees to system recommendation
      await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      // User B counter-proposes instead of agreeing
      const afterCounter = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userB.id,
        userB.id,
        'I prefer to pay first'
      );      expect(afterCounter.partyA_agreedToCurrentProposal).toBe(false); // Reset
      expect(afterCounter.partyB_agreedToCurrentProposal).toBe(true); // User B automatically agrees to their own proposal
      expect(afterCounter.currentProposal_ById).toBe(userB.id);

      // Now User A agrees to User B's proposal
      const finalResult = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userA.id
      );

      expect(finalResult.partyA_agreedToCurrentProposal).toBe(true);
      expect(finalResult.partyB_agreedToCurrentProposal).toBe(true); // User B already agreed when they made the proposal
      expect(finalResult.negotiationStatus).toBe(NegotiationStatus.FINALIZED); // Should be finalized since both parties now agree
      expect(finalResult.finalizedPayerId).toBe(userB.id); // User B was the proposed payer
    });
  });
});
