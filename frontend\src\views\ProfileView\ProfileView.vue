<template>
  <div class="profile-container">
    <!-- Profile Header -->
    <section class="profile-header">
      <div class="header-content">        <div class="avatar-section">
          <n-avatar 
            :size="120" 
            :src="user?.profile?.avatarUrl"
            class="profile-avatar"
            round
          >
            <n-icon size="48">
              <UserOutlined />
            </n-icon>
          </n-avatar>          <n-button text class="change-avatar-btn" size="small">
            <template #icon>
              <n-icon><CameraOutlined /></n-icon>
            </template>
            {{ t('profile.changePhoto') }}
          </n-button>
        </div>
        <div class="profile-info">
          <h1 class="profile-name">{{ username }}</h1>
          <p class="profile-email">{{ user?.email }}</p>
          <div class="reputation-badge">
            <n-tag 
              :type="reputationTagType" 
              size="large"
              data-testid="reputation-level"
            >
              <template #icon>
                <n-icon><StarOutlined /></n-icon>
              </template>
              {{ reputationDisplayText }}
            </n-tag>
          </div>          <p class="member-since">
            <n-icon><CalendarOutlined /></n-icon>
            {{ t('profile.memberSince', { date: joinDateDisplay }) }}
          </p>
        </div>
      </div>
    </section>    <!-- Main Content -->
    <div class="profile-content">
      <n-grid :cols="24" :x-gap="16" :y-gap="16" responsive="screen">
        <!-- Left Column - Profile Details -->
        <n-grid-item :span="24" :lg="16" :md="24">
          <!-- Account Status Card -->          <n-card class="status-card" :title="t('profile.accountStatus')">
            <template #header-extra>
              <n-icon><SecurityScanOutlined /></n-icon>
            </template>
            <n-space vertical size="large">
              <!-- Email Status -->
              <div class="status-item">
                <div class="status-header">
                  <n-icon size="20" color="#18a058"><MailOutlined /></n-icon>
                  <span class="status-label">{{ t('profile.emailVerification') }}</span>
                </div>
                <div class="status-content">
                  <n-tag :type="user?.emailVerified ? 'success' : 'warning'">
                    <template #icon>
                      <n-icon>
                        <CheckCircleOutlined v-if="user?.emailVerified" />
                        <ClockCircleOutlined v-else />
                      </n-icon>
                    </template>
                    {{ user?.emailVerified ? t('profile.verified') : t('profile.pendingVerification') }}
                  </n-tag>                  <n-button 
                    v-if="!user?.emailVerified" 
                    text 
                    type="primary" 
                    size="small"
                    class="resend-btn"
                    @click="resendVerificationEmail"
                    :loading="loadingResendEmail"
                  >
                    {{ t('profile.resendEmail') }}
                  </n-button>
                </div>
              </div>

              <!-- Phone Status -->
              <div class="status-item">
                <div class="status-header">
                  <n-icon size="20" color="#2080f0"><PhoneOutlined /></n-icon>
                  <span class="status-label">{{ t('profile.phoneVerification') }}</span>
                </div>
                <div class="status-content">
                  <n-tag 
                    :type="isPhoneVerified ? 'success' : 'warning'" 
                    :data-testid="isPhoneVerified ? 'phone-status-verified' : 'phone-status-unverified'"
                  >
                    <template #icon>
                      <n-icon>
                        <CheckCircleOutlined v-if="isPhoneVerified" />
                        <ClockCircleOutlined v-else />
                      </n-icon>
                    </template>
                    {{ isPhoneVerified ? t('profile.verified') : t('profile.notVerified') }}
                  </n-tag>
                  <span v-if="isPhoneVerified && user?.phoneNumber" class="phone-number">
                    {{ user.phoneNumber }}
                  </span>
                </div>
              </div>

              <!-- Security Score -->
              <div class="status-item">
                <div class="status-header">
                  <n-icon size="20" color="#f0a020"><SafetyOutlined /></n-icon>
                  <span class="status-label">{{ t('profile.securityScore') }}</span>
                </div>
                <div class="status-content">
                  <n-progress 
                    type="line" 
                    :percentage="securityScore" 
                    :color="securityScoreColor"
                    :height="8"
                    :border-radius="4"
                  />
                  <span class="security-text">{{ securityScoreText }}</span>
                </div>
              </div>
            </n-space>
          </n-card>

          <!-- Phone Verification Section -->
          <n-card 
            v-if="!isPhoneVerified" 
            class="verification-card" 
            :title="t('profile.completePhoneVerification')"
          >
            <template #header-extra>
              <n-icon><PhoneOutlined /></n-icon>
            </template>
              <n-alert
              type="info"
              :title="t('profile.whyVerifyPhone')"
              style="margin-bottom: 24px;"
            >
              {{ t('profile.phoneVerificationBenefit') }}
            </n-alert>

            <!-- Rate Limit Alerts -->
            <n-alert
              v-if="isRateLimitError()"
              type="error"
              :title="t('profile.rateLimitExceeded')"
              :closable="true"
              @close="clearMessages"
              style="margin-bottom: 20px;"
              data-testid="rate-limit-alert"
            >              <div v-if="blockTimeRemaining > 0">
                {{ t('profile.tooManyAttempts', { seconds: blockTimeRemaining }) }}
              </div>
              <div v-else-if="remainingAttempts !== null && remainingAttempts > 0">
                {{ t('profile.remainingAttempts', { count: remainingAttempts }) }}
              </div>
              <div v-else>
                {{ error }}
              </div>
            </n-alert>

            <n-alert
              v-else-if="blockedUntil && !error"
              type="info"
              style="margin-bottom: 20px;"
            >
              {{ t('profile.waitBeforeRetrying', { seconds: blockTimeRemaining }) }}                <div v-if="remainingAttempts !== null" style="margin-top: 4px;">
                {{ t('profile.remainingAttempts', { count: remainingAttempts }) }}
              </div>
            </n-alert>

            <n-spin :show="loadingRequest || loadingVerify">
              <!-- Phone Number Input Form -->
              <n-form
                v-show="!showOtpForm"
                ref="phoneFormRef"
                :model="phoneFormValue"
                :rules="phoneValidationRules"
                data-testid="phone-form"
                size="large"
              >
                <n-form-item path="phoneNumber">
                  <template #label>                    <div class="form-label">
                      {{ t('profile.phoneNumberE164') }}
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="16" class="help-icon">
                            <QuestionCircleOutlined />
                          </n-icon>
                        </template>
                        {{ t('profile.phoneNumberHelpText') }}
                      </n-tooltip>
                    </div>
                  </template>                  <n-input
                    ref="phoneInputRef"
                    v-model:value="phoneFormValue.phoneNumber"
                    :placeholder="t('profile.phoneNumberPlaceholder')"
                    data-testid="phone-input"
                    :disabled="loadingRequest || !!blockedUntil || remainingAttempts === 0"
                    size="large"
                    type="tel"
                    autocomplete="tel"
                  >
                    <template #prefix>
                      <n-icon><PhoneOutlined /></n-icon>
                    </template>
                  </n-input>
                </n-form-item>
                <n-form-item>
                  <n-button
                    block
                    type="primary"
                    size="large"
                    data-testid="send-otp-button"
                    @click="requestOtp"
                    :disabled="loadingRequest || !!blockedUntil || remainingAttempts === 0"
                    :loading="loadingRequest"
                  >
                    <template #icon>
                      <n-icon><SendOutlined /></n-icon>
                    </template>
                    {{ t('profile.sendVerificationCode') }}
                  </n-button>
                </n-form-item>
              </n-form>

              <!-- OTP Input Form -->
              <n-form
                v-show="showOtpForm"
                ref="otpFormRef"
                :model="otpFormData"
                :rules="otpValidationRules"
                data-testid="otp-form"
                size="large"
              >                <div class="otp-info">
                  <n-text depth="3">{{ t('profile.verifyingNumber') }}</n-text>
                  <n-text strong>{{ lastSubmittedPhone }}</n-text>
                </div>
                  <n-form-item path="otpCode">
                  <template #label>                    <div class="form-label">
                      {{ t('profile.verificationCode') }}
                    </div>
                  </template>
                  <n-input
                    ref="otpInputRef"
                    v-model:value="otpFormData.otpCode"
                    maxlength="6"
                    data-testid="otp-input"
                    :disabled="loadingVerify || !!blockedUntil || remainingAttempts === 0"
                    size="large"
                    :placeholder="t('profile.enterSixDigitCode')"
                    type="tel"
                    autocomplete="one-time-code"
                  >
                    <template #prefix>
                      <n-icon><LockOutlined /></n-icon>
                    </template>
                  </n-input>
                </n-form-item>                <n-form-item>
                  <n-space vertical size="medium" style="width: 100%;">
                    <n-button
                      block
                      type="primary"
                      size="large"
                      data-testid="verify-otp-button"
                      @click="verifyOtp"
                      :disabled="loadingVerify || !!blockedUntil || remainingAttempts === 0"
                      :loading="loadingVerify"
                    >
                      <template #icon>
                        <n-icon><CheckOutlined /></n-icon>
                      </template>
                      {{ t('profile.verifyCode') }}
                    </n-button>

                    <n-space size="small">
                      <n-button
                        style="flex: 1;"
                        @click="requestOtp"
                        :disabled="loadingRequest || !!blockedUntil || (remainingAttempts !== null && remainingAttempts === 0) || resendDisabledTime > 0"
                        :loading="loadingRequest"
                        data-testid="resend-otp-button"
                      >
                        <template #icon>
                          <n-icon><ReloadOutlined /></n-icon>
                        </template>
                        {{ t('profile.resendCode') }}<span v-if="resendDisabledTime > 0"> ({{ resendDisabledTime }}s)</span>
                      </n-button>
                      <n-button
                        style="flex: 1;"
                        @click="goBackToPhoneInput"
                        :disabled="loadingRequest || loadingVerify || !!blockedUntil || remainingAttempts === 0"
                        data-testid="change-number-button"
                      >
                        <template #icon>
                          <n-icon><EditOutlined /></n-icon>
                        </template>
                        {{ t('profile.changeNumber') }}
                      </n-button>
                    </n-space>                  </n-space>
                </n-form-item>
              </n-form>
            </n-spin>
          </n-card>

          <!-- Verified Success Message -->
          <n-card v-else-if="isPhoneVerified" class="success-card">            <n-result
              status="success"
              :title="t('profile.phoneVerified')"
              :description="t('profile.phoneVerifiedSuccess')"
            >
              <template #icon>
                <n-icon color="#18a058" size="48">
                  <CheckCircleOutlined />
                </n-icon>
              </template>
              <template #footer>                <n-text depth="3">
                  {{ t('profile.verifiedNumber', { number: user?.phoneNumber }) }}
                </n-text>
              </template>
            </n-result>
          </n-card>
        </n-grid-item>        <!-- Right Column - Quick Stats -->
        <n-grid-item :span="24" :lg="8" :md="24">
          <!-- Profile Stats Card -->
          <n-card class="stats-card" :title="t('profile.profileStatistics')">
            <template #header-extra>
              <n-icon><BarChartOutlined /></n-icon>
            </template>
            <n-space vertical size="large">
              <div class="stat-item">
                <div class="stat-icon">
                  <n-icon size="24" color="#18a058"><TrophyOutlined /></n-icon>
                </div>
                <div class="stat-content">
                  <n-statistic :label="t('profile.reputationScore')" :value="user?.reputationScore || 0" />
                </div>
              </div>
                <div class="stat-item">
                <div class="stat-icon">
                  <n-icon size="24" color="#2080f0"><ShopOutlined /></n-icon>
                </div>
                <div class="stat-content">
                  <n-statistic :label="t('profile.activeOffers')" :value="activeOffersCount" />
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <n-icon size="24" color="#f0a020"><CheckCircleOutlined /></n-icon>
                </div>
                <div class="stat-content">
                  <n-statistic :label="t('profile.completedTransactions')" :value="completedTransactionsCount" />
                </div>
              </div>
            </n-space>
          </n-card>

          <!-- Quick Actions Card -->
          <n-card class="actions-card" :title="t('profile.quickActions')" style="margin-top: 24px;">
            <template #header-extra>
              <n-icon><SettingOutlined /></n-icon>
            </template>
            <n-space vertical size="medium">
              <n-button block @click="goToCreateOffer">
                <template #icon>
                  <n-icon><PlusOutlined /></n-icon>
                </template>
                {{ t('profile.createNewOffer') }}
              </n-button>
              <n-button block @click="goToMyOffers">
                <template #icon>
                  <n-icon><FileTextOutlined /></n-icon>
                </template>
                {{ t('profile.viewMyOffers') }}
              </n-button>
              <n-button block @click="goToBrowseOffers">
                <template #icon>
                  <n-icon><SearchOutlined /></n-icon>
                </template>
                {{ t('profile.browseOffers') }}
              </n-button>
            </n-space>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>    <!-- Loading Overlay -->
    <div v-if="isLoadingProfile" class="profile-loading-overlay" @click="clearLoadingState">
      <div class="loading-content" @click.stop>
        <n-icon size="48"><LoadingOutlined /></n-icon>        <p>{{ t('profile.loadingProfile') }}</p>
        <n-button text @click="clearLoadingState" style="margin-top: 16px; color: #666;">
          {{ t('profile.clickIfStuck') }}
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import {
  NCard, NSpin, NTag, NAlert, NForm, NFormItem, NInput, NButton,
  NIcon, NTooltip, NText, NAvatar, NSpace, NGrid, NGridItem,
  NStatistic, NProgress, NResult,
  useMessage, type FormInst, type InputInst
} from 'naive-ui';
import {
  UserOutlined, CameraOutlined, StarOutlined, CalendarOutlined,
  SecurityScanOutlined, MailOutlined, PhoneOutlined, SafetyOutlined,
  CheckCircleOutlined, ClockCircleOutlined, QuestionCircleOutlined,
  SendOutlined, LockOutlined, CheckOutlined, ReloadOutlined, EditOutlined,
  BarChartOutlined, TrophyOutlined, ShopOutlined, SettingOutlined,
  PlusOutlined, FileTextOutlined, SearchOutlined, LoadingOutlined
} from '@vicons/antd';
import { useAuthStore } from '@/stores/auth';
import { useMyOffersStore } from '@/stores/myOffersStore';
import apiClient from '@/services/apiClient';
import { storeToRefs } from 'pinia';
import type { OtpSuccessResponse, VerifySuccessResponse, RateLimitInfo } from '@/types/api';
import { handleError as handleApiError } from '@/utils/errorHandler';
import { useTranslation } from '@/composables/useTranslation';

const router = useRouter();
const authStore = useAuthStore();
const myOffersStore = useMyOffersStore();
const { user } = storeToRefs(authStore);
const { myOffers } = storeToRefs(myOffersStore);
const message = useMessage();
const { t } = useTranslation();

// --- Refs for form instances ---
const phoneFormRef = ref<FormInst | null>(null);
const otpFormRef = ref<FormInst | null>(null);
const phoneInputRef = ref<InputInst | null>(null);
const otpInputRef = ref<InputInst | null>(null);
const lastSubmittedPhone = ref<string | null>(null);

// --- Reactive form data ---
const phoneFormValue = reactive({ phoneNumber: '' });
const otpFormData = reactive({ otpCode: '' });
const otpFormValue = otpFormData;

// --- Loading and state refs ---
const loadingProfile = ref(true);
const loadingRequest = ref(false);
const loadingVerify = ref(false);
const loadingResendEmail = ref(false);
const showOtpForm = ref(false);
const error = ref<string | null>(null);
const lastStatus = ref<string | null>(null);

// --- Rate Limiting Refs ---
const remainingAttempts = ref<number | null>(null);
const blockedUntil = ref<number | null>(null);
const resendDisabledTime = ref(0);
const blockTimerInterval = ref<number | null>(null);
const resendTimerInterval = ref<number | null>(null);
// Removed unused blockTimer ref to avoid confusion and potential resource leaks.
const currentTime = ref(Date.now());

// --- Navigation functions ---
function goToCreateOffer() {
  router.push({ name: 'CreateOffer' });
}

function goToMyOffers() {
  router.push({ name: 'MyOffers' });
}

function goToBrowseOffers() {
  router.push({ name: 'BrowseOffers' });
}

// --- Computed properties ---
const isPhoneVerified = computed(() => !!user.value?.phoneVerified);

// Force reactive computation for loading state
const isLoadingProfile = computed(() => {
  const loading = loadingProfile.value;
  return loading;
});

const blockTimeRemaining = computed(() => {
  if (!blockedUntil.value || blockedUntil.value <= currentTime.value) return 0;
  return Math.ceil((blockedUntil.value - currentTime.value) / 1000);
});

const securityScore = computed(() => {
  let score = 0;
  if (user.value?.emailVerified) score += 50;
  if (user.value?.phoneVerified) score += 50;
  return score;
});

const securityScoreColor = computed(() => {
  const score = securityScore.value;
  if (score >= 100) return '#18a058'; // Green
  if (score >= 50) return '#f0a020'; // Orange
  return '#d03050'; // Red
});

const securityScoreText = computed(() => {
  const score = securityScore.value;
  if (score >= 100) return t('profile.excellentSecurity');
  if (score >= 50) return t('profile.goodSecurity');
  return t('profile.basicSecurity');
});

const computedReputationLevel = computed(() => {
  let level = user.value?.reputationLevel;
  
  if (!level) {
    if (user.value?.emailVerified && user.value?.phoneVerified) {
      level = 3;
    } else if (user.value?.emailVerified || user.value?.phoneVerified) {
      level = 2;
    } else {
      level = 1;
    }
  }
  
  return level;
});

const reputationDisplayText = computed(() => {
  switch (computedReputationLevel.value) {
    case 1: return t('profile.bronze');
    case 2: return t('profile.silver');
    case 3: return t('profile.gold');
    case 4: return t('profile.platinum');
    case 5: return t('profile.diamond');
    default: return t('profile.bronze');
  }
});

const reputationTagType = computed(() => {
  switch (computedReputationLevel.value) {
    case 1: return 'default';
    case 2: return 'info';
    case 3: return 'success';
    case 4: return 'warning';
    case 5: return 'error';
    default: return 'default';
  }
});

// Add computed for rate limit error check if needed by template
const isRateLimitError = () => {
    return !!error.value && (
        error.value.includes('Rate limit exceeded') ||
        error.value.includes('Too many attempts') ||
        error.value.includes('Too Many Requests')
    );
};

const resendTimerLabel = computed(() => {
  return resendDisabledTime.value > 0 ? `(${resendDisabledTime.value}s)` : '';
});

// --- Profile related computed properties ---
const username = computed(() => {
  return user.value?.username || user.value?.email?.split('@')[0] || t('profile.defaultUsername');
});

const joinDateDisplay = computed(() => {
  if (!user.value?.createdAt) {
    return t('profile.defaultJoinDate');
  }
  try {
    return new Date(user.value.createdAt).toLocaleDateString('en-CA', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return t('profile.defaultJoinDate');
  }
});

// --- Statistics computed properties ---
const activeOffersCount = computed(() => {
  if (!myOffers.value || !Array.isArray(myOffers.value)) {
    return 0;
  }
  return myOffers.value.filter(offer => offer.status === 'ACTIVE').length;
});

const completedTransactionsCount = computed(() => {
  if (!myOffers.value || !Array.isArray(myOffers.value)) {
    return 0;
  }
  
  // Count completed transactions from interests on user's offers
  let completedCount = 0;
  myOffers.value.forEach(offer => {
    if (offer.interests && Array.isArray(offer.interests)) {
      offer.interests.forEach(interest => {
        if (interest.transactionStatus === 'COMPLETED') {
          completedCount++;
        }
      });
    }
  });
  
  return completedCount;
});

// --- Validation Rules ---
const phoneValidationRules = {
    phoneNumber: [
        { required: true, message: t('profile.validation.phoneNumberRequired'), trigger: ['input', 'blur'] },
        // Add E.164 format validation if possible with regex or a custom validator
        {
            pattern: /^\+[1-9]\d{1,14}$/, // Basic E.164 check
            message: t('profile.validation.phoneNumberFormat'),
            trigger: ['input', 'blur']
        }
    ]
};

const otpValidationRules = {
  otpCode: [
    { required: true, message: t('profile.validation.otpRequired'), trigger: ['input', 'blur'] },
    { len: 6, message: t('profile.validation.otpLength'), trigger: ['input', 'blur'] },
    { pattern: /^\d{6}$/, message: t('profile.validation.otpDigitsOnly'), trigger: ['input', 'blur'] }
    // Removed the Zod validator for simplicity, relying on Naive rules
  ]
};

// --- Helper Methods ---
const forceHideLoading = async () => {
  loadingProfile.value = false;
  await nextTick();
  // Double check and force hide if still showing
  setTimeout(() => {
    if (loadingProfile.value) {
      loadingProfile.value = false;
    }
  }, 100);
};

// --- Lifecycle Hooks ---
onMounted(async () => {
  // If user data is already available, don't show loading
  if (user.value && user.value.id) {
    loadingProfile.value = false;
    // Fetch user's offers for statistics
    try {
      await myOffersStore.fetchMyOffers();
    } catch (err) {
      console.error('Failed to fetch offers for statistics:', err);
    }
    return;
  }

  loadingProfile.value = true;
  error.value = null;

  // Immediate check - if user becomes available quickly, hide loading
  const immediateCheck = setTimeout(() => {
    if (user.value && user.value.id && loadingProfile.value) {
      forceHideLoading();
    }
  }, 100);

  // Safety timeout to ensure loading doesn't get stuck
  const loadingTimeout = setTimeout(() => {
    loadingProfile.value = false;
  }, 10000); // 10 second timeout

  try {
    const authStore = useAuthStore();
    await authStore.fetchUserProfile();
    
    // Fetch user's offers for statistics once profile is loaded
    if (user.value && user.value.id) {
      await myOffersStore.fetchMyOffers();
    }  } catch (err) {
    try {
      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.loadProfileError'));
    } catch (handlerError) {
      error.value = t('profile.messages.loadProfileError');
      message.error(t('profile.messages.loadProfileError'));
    }
  } finally {
    clearTimeout(loadingTimeout); // Clear the safety timeout
    clearTimeout(immediateCheck); // Clear the immediate check timeout
    await forceHideLoading();
  }
});

// --- Watchers ---
watch(user, (newUser, oldUser) => {
  // Reset only if the user actually changes (e.g., logs out and logs in as someone else, or logs out)
  // or if the phone verification status changes externally
  if (newUser?.id !== oldUser?.id || newUser?.phoneVerified !== oldUser?.phoneVerified) {
      resetVerification();
      // If the user becomes verified, ensure the phone number is displayed if available
      if (newUser?.phoneVerified && !phoneFormValue.phoneNumber && newUser.phoneNumber) {
          phoneFormValue.phoneNumber = newUser.phoneNumber; // Pre-fill if needed, though usually not necessary as form hides
      }
  }
}, { deep: true }); // Use deep watch if user object structure is complex

// Debug watcher for loading state
watch(loadingProfile, (newValue, oldValue) => {
}, { immediate: true });

// Additional safety check - if user data exists but loading is still true
watch(user, (newUser) => {
  if (newUser && loadingProfile.value) {
    setTimeout(() => {
      if (loadingProfile.value) {
        forceHideLoading();
      }
    }, 2000);
  }
}, { immediate: true });

// --- Utility Functions ---
function clearMessages() {
  error.value = null;
}

// Force clear loading state (for debugging)
function clearLoadingState() {
  forceHideLoading();
}

// Make clearLoadingState available on window for debugging
if (typeof window !== 'undefined') {
  // Removed window debug method for production cleanliness
}

// --- Timer Functions ---
function startResendTimer() {
  // Clear any existing timer
  if (resendTimerInterval.value) {
    clearInterval(resendTimerInterval.value);
    resendTimerInterval.value = null;
  }
  resendDisabledTime.value = 30; // 30 seconds for example

  // Start a new timer
  resendTimerInterval.value = setInterval(() => {
    resendDisabledTime.value--;

    // Clear the timer when reaching 0
    if (resendDisabledTime.value <= 0) {
      clearInterval(resendTimerInterval.value ?? undefined);
      resendTimerInterval.value = null;
    }
  }, 1000);
}


function clearTimers() {
  if (blockTimerInterval.value) {
    clearInterval(blockTimerInterval.value);
    blockTimerInterval.value = null;
  }
  if (resendTimerInterval.value) {
    clearInterval(resendTimerInterval.value);
    resendTimerInterval.value = null;
  }
}

function startBlockTimer() {
  if (blockTimerInterval.value) {
    clearInterval(blockTimerInterval.value);
    blockTimerInterval.value = null;
  }

  if (blockedUntil.value && blockedUntil.value > Date.now()) {
    currentTime.value = Date.now(); // Initialize currentTime

    blockTimerInterval.value = setInterval(() => {
      // ---> FIX: Update currentTime ref <---
      currentTime.value = Date.now();
      // ---------------------------------

      // Use currentTime for calculation
      const remaining = Math.max(0, blockedUntil.value! - currentTime.value);

      if (remaining <= 0) {
        clearInterval(blockTimerInterval.value!);
        blockTimerInterval.value = null;
        blockedUntil.value = null;
        remainingAttempts.value = null;
        error.value = null;
      }
    }, 1000);
  } else {
    blockedUntil.value = null;
    remainingAttempts.value = null;
    error.value = null;
  }
}

// --- API Call Functions ---
async function requestOtp() {
  error.value = null; // Clear previous errors

  try {
    await phoneFormRef.value?.validate();
  } catch (validationError: any) {
    return; // Validation failed, Naive UI shows messages
  }

  // Store the submitted phone number
  lastSubmittedPhone.value = phoneFormValue.phoneNumber;

  loadingRequest.value = true;
  try {
    const response = await apiClient.post<OtpSuccessResponse | RateLimitInfo>('/auth/phone/send-otp', {
      phoneNumber: phoneFormValue.phoneNumber,
    });

    // THIS BLOCK MOVED: Only process response.data on success
    if ('remainingAttempts' in response.data) {
        remainingAttempts.value = response.data.remainingAttempts ?? null; // Use nullish coalescing
        if (response.data.blockedUntil) { // Check if blockedUntil is present
          blockedUntil.value = new Date(response.data.blockedUntil).getTime(); // Convert to timestamp
          startBlockTimer();
        } else {
          blockedUntil.value = null;
        }
    } else if ('message' in response.data) { // Assuming OtpSuccessResponse has a message
        // Handle non-rate-limit success response if necessary, though message.success is primary
    }


    message.success(response.data.message || t('profile.messages.otpSentSuccess'));
    showOtpForm.value = true;
    startResendTimer();
    otpFormData.otpCode = '';
    nextTick(() => {
      otpInputRef.value?.focus();
    });
    // END MOVED BLOCK
  } catch (err: any) {
    console.error('[TEST_DEBUG] apiClient.post for send-otp FAILED:', err);
    try {
      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.sendOtpError'));
    } catch (handlerError) {
      console.error('[ProfileView] Error in handleApiError:', handlerError);      
      error.value = t('profile.messages.sendOtpError');
      message.error(t('profile.messages.sendOtpError'));
    }
    // Extract rate limit info directly from err if it's a 429 error
    if (err?.response?.status === 429 && err.response.data) {
      remainingAttempts.value = err.response.data.remainingAttempts ?? null;
      if (err.response.data.blockedUntil) {
        blockedUntil.value = new Date(err.response.data.blockedUntil).getTime();
        startBlockTimer();
      } else {
        blockedUntil.value = null;
      }
      
      // ADD THIS LINE: Show the OTP form even when rate limited, if we have a number
      if (lastSubmittedPhone.value) {
        showOtpForm.value = true;
      }
    }
  } finally {
    console.log('[TEST_DEBUG] requestOtp finally block');
    loadingRequest.value = false;
  }
}

async function verifyOtp() {
  error.value = null;
  lastStatus.value = null; 

  try {
    await otpFormRef.value?.validate();
  } catch (validationError: any) {
    console.log('Naive UI OTP validation failed:', validationError);
    return; 
  }

  loadingVerify.value = true; 
  try {
    const response = await apiClient.post<VerifySuccessResponse | RateLimitInfo>('/auth/phone/verify-otp', {
        otpCode: otpFormData.otpCode 
    });

    // THIS BLOCK MOVED: Only process response.data on success
    if ('remainingAttempts' in response.data) {
        remainingAttempts.value = response.data.remainingAttempts ?? null; // Use nullish coalescing
        // Correctly handle blockedUntil if it's a string date
        if (response.data.blockedUntil && typeof response.data.blockedUntil === 'string') {
            blockedUntil.value = new Date(response.data.blockedUntil).getTime();
        } else {
            blockedUntil.value = response.data.blockedUntil ?? null; // Assuming it might already be a number or null
        }
        if (blockedUntil.value) startBlockTimer();
    }

    let verifiedPhoneNumber: string = phoneFormValue.phoneNumber;
    if ('phoneNumber' in response.data && typeof response.data.phoneNumber === 'string') {
      verifiedPhoneNumber = response.data.phoneNumber;
    }

    message.success(response.data.message || t('profile.messages.phoneVerifiedSuccess'));
    authStore.updatePhoneVerificationStatus(true, verifiedPhoneNumber); 
    showOtpForm.value = false; 
    clearTimers(); 
    // END MOVED BLOCK
  }   catch (err: any) {
    try {      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.verifyOtpError'));
    } catch (handlerError) {
      console.error('[ProfileView] Error in handleApiError:', handlerError);
      error.value = t('profile.messages.verifyOtpError');
      message.error(t('profile.messages.verifyOtpError'));
    }
  
    // Optionally extract rate limit info directly from err if present
    if (err?.response?.data?.remainingAttempts !== undefined) {
      remainingAttempts.value = err.response.data.remainingAttempts ?? null;
      // Correctly parse the blockedUntil ISO string to a timestamp
      if (err.response.data.blockedUntil && typeof err.response.data.blockedUntil === 'string') {
        blockedUntil.value = new Date(err.response.data.blockedUntil).getTime();
      } else {
        blockedUntil.value = err.response.data.blockedUntil ?? null; // Fallback if not a string
      }
      if (blockedUntil.value) {
        startBlockTimer();
      }
    }
  } finally {
    loadingVerify.value = false; 
  }
}

// --- Go Back to Phone Input ---
// --- Go Back to Phone Input ---
function goBackToPhoneInput() {
    // We need to remove the condition that keeps the user in the OTP form
    // when they click "Change Number". The user explicitly wants to go back.
    
    showOtpForm.value = false;
    otpFormValue.otpCode = ''; 
    error.value = null;
    lastStatus.value = null;
    nextTick(() => {
        phoneInputRef.value?.focus();
    });
}


// --- Reset Verification (called on user change/logout) ---
function resetVerification() {
  // console.log('[RESET_DEBUG] resetVerification called'); // Keep logs if needed for debugging
  showOtpForm.value = false; // Start with phone input
  phoneFormValue.phoneNumber = '';
  otpFormValue.otpCode = ''; // Use otpFormValue here
  error.value = null;
  lastStatus.value = null;
  remainingAttempts.value = null;
  blockedUntil.value = null;
  clearTimers(); // Clear interval timers first

  // Clear blockTimer specifically if it exists
  if (blockTimer.value) {
    clearInterval(blockTimer.value);
    blockTimer.value = null;
  }

  // Use nextTick to allow DOM updates from showOtpForm change
  nextTick(() => {
    // console.log('[RESET_DEBUG] nextTick in resetVerification');
    // ---> Explicitly check if refs exist before calling methods <---
    if (phoneFormRef.value) {
      // console.log('[RESET_DEBUG] Restoring phoneFormRef validation');
      phoneFormRef.value.restoreValidation();
    } else {
      // console.log('[RESET_DEBUG] phoneFormRef is null in resetVerification nextTick');
    }

    // Check otpFormRef as well, although it should be null if showOtpForm is false
    if (otpFormRef.value) {
      // console.log('[RESET_DEBUG] Restoring otpFormRef validation');
      otpFormRef.value.restoreValidation();
    } else {
      // console.log('[RESET_DEBUG] otpFormRef is null in resetVerification nextTick');
    }  });
}

// --- Resend Verification Email ---
async function resendVerificationEmail() {
  loadingResendEmail.value = true;
  
  try {
    const response = await apiClient.post('/auth/resend-verification-email');
    
    message.success(response.data.message || t('profile.messages.emailSentSuccess'));
  } catch (error: any) {
    const errorMessage = handleApiError(error, message, t('profile.messages.resendEmailError'));
    console.error('Resend email error:', errorMessage);
  } finally {
    loadingResendEmail.value = false;
  }
}

</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px 32px;
  min-height: calc(100vh - 64px);
}

/* Profile Header */
.profile-header {
  border-radius: 16px;
  margin-bottom: 32px;
  padding: 32px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

[data-theme="light"] .profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

[data-theme="dark"] .profile-header {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #e0e0e0;
  border: 1px solid #333;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(10px);
  border-radius: 16px;
  z-index: 0;
}

[data-theme="light"] .profile-header::before {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .profile-header::before {
  background: rgba(255, 255, 255, 0.05);
}

.header-content {
  position: relative;
  z-index: 1;
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.profile-avatar {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: border-color 0.3s ease;
}

[data-theme="light"] .profile-avatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .profile-avatar {
  border: 4px solid rgba(255, 255, 255, 0.2);
}

.change-avatar-btn {
  font-size: 12px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

[data-theme="light"] .change-avatar-btn {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .change-avatar-btn {
  color: rgba(255, 255, 255, 0.8);
}

.change-avatar-btn:hover {
  opacity: 1;
}

.profile-info {
  flex: 1;
  min-width: 300px;
}

.profile-name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-email {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 16px 0;
}

.reputation-badge {
  margin-bottom: 16px;
}

.member-since {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  opacity: 0.8;
  margin: 0;
}

/* Content Area */
.profile-content {
  margin-top: 24px;
}

/* Cards */
.status-card,
.verification-card,
.success-card,
.stats-card,
.actions-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  margin-bottom: 24px;
  overflow: hidden;
}

[data-theme="light"] .status-card,
[data-theme="light"] .verification-card,
[data-theme="light"] .stats-card,
[data-theme="light"] .actions-card {
  border: 1px solid #e0e0e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  background: #ffffff;
}

[data-theme="dark"] .status-card,
[data-theme="dark"] .verification-card,
[data-theme="dark"] .stats-card,
[data-theme="dark"] .actions-card {
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  background: #1e1e1e;
}

[data-theme="light"] .status-card:hover,
[data-theme="light"] .verification-card:hover,
[data-theme="light"] .stats-card:hover,
[data-theme="light"] .actions-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

[data-theme="dark"] .status-card:hover,
[data-theme="dark"] .verification-card:hover,
[data-theme="dark"] .stats-card:hover,
[data-theme="dark"] .actions-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* Success card special styling */
[data-theme="light"] .success-card {
  background: linear-gradient(135deg, #f8fff8 0%, #f0fff0 100%);
  border-color: #52c41a;
}

[data-theme="dark"] .success-card {
  background: linear-gradient(135deg, #1a2e1a 0%, #1f3e1f 100%);
  border-color: #52c41a;
}

/* Status Items */
.status-item {
  padding: 16px 0;
}

[data-theme="light"] .status-item {
  border-bottom: 1px solid #f0f0f5;
}

[data-theme="dark"] .status-item {
  border-bottom: 1px solid #333;
}

.status-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.status-label {
  font-weight: 600;
  font-size: 1rem;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.phone-number {
  font-size: 0.9rem;
}

[data-theme="light"] .phone-number {
  color: #666;
}

[data-theme="dark"] .phone-number {
  color: #999;
}

.security-text {
  font-size: 0.9rem;
  font-weight: 500;
  margin-left: 8px;
}

.resend-btn {
  font-size: 0.85rem;
}

/* Form Styling */
.form-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.help-icon {
  cursor: help;
}

[data-theme="light"] .help-icon {
  color: #999;
}

[data-theme="dark"] .help-icon {
  color: #666;
}

.otp-info {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

[data-theme="light"] .otp-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

[data-theme="dark"] .otp-info {
  background: #2a2a2a;
  border: 1px solid #404040;
}

/* Stats */
.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
}

[data-theme="light"] .stat-icon {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

[data-theme="dark"] .stat-icon {
  background: linear-gradient(135deg, #2a2a2a 0%, #333 100%);
}

.stat-content {
  flex: 1;
}

/* Loading */
.profile-loading,
.profile-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

[data-theme="light"] .profile-loading,
[data-theme="light"] .profile-loading-overlay {
  background: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .profile-loading,
[data-theme="dark"] .profile-loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.loading-content {
  text-align: center;
}

[data-theme="light"] .loading-content {
  color: #666;
}

[data-theme="dark"] .loading-content {
  color: #ccc;
}

.loading-content p {
  margin-top: 16px;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-container {
    padding: 0 16px 24px;
  }
  
  .profile-header {
    padding: 28px 24px;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 0 12px 20px;
    min-height: calc(100vh - 60px);
  }
  
  .profile-header {
    padding: 20px 16px;
    margin-bottom: 20px;
    border-radius: 12px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .profile-avatar {
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  }
  
  .profile-name {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .profile-email {
    font-size: 1rem;
  }
  
  .profile-info {
    min-width: auto;
    width: 100%;
  }
  
  .reputation-badge .n-tag {
    font-size: 0.9rem;
  }
  
  .member-since {
    justify-content: center;
    font-size: 0.9rem;
  }
  
  /* Card adjustments */
  .status-card,
  .verification-card,
  .success-card,
  .stats-card,
  .actions-card {
    margin-bottom: 16px;
    border-radius: 10px;
  }
  
  .status-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stat-item {
    padding: 14px 0;
    flex-direction: row;
    align-items: center;
  }
  
  .stat-icon {
    width: 44px;
    height: 44px;
    flex-shrink: 0;
  }
  
  .stat-content {
    flex: 1;
  }
  
  /* Form improvements */
  .n-form-item {
    margin-bottom: 20px;
  }
  
  .n-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .n-button {
    min-height: 44px; /* Better touch target */
    font-size: 1rem;
  }
  
  .form-label {
    font-size: 0.95rem;
  }
  
  .otp-info {
    padding: 14px;
    margin-bottom: 16px;
  }
}

@media (max-width: 640px) {
  .profile-container {
    padding: 0 8px 16px;
  }
  
  .profile-header {
    padding: 16px 12px;
    margin-bottom: 16px;
  }
  
  .profile-name {
    font-size: 1.8rem;
  }
  
  .profile-email {
    font-size: 0.95rem;
  }
  
  .avatar-section {
    gap: 8px;
  }
  
  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
    font-size: 40px;
  }
  
  .change-avatar-btn {
    font-size: 11px;
  }
  
  /* Compact card spacing */
  .status-card,
  .verification-card,
  .success-card,
  .stats-card,
  .actions-card {
    margin-bottom: 12px;
  }
  
  .status-item {
    padding: 12px 0;
  }
  
  .stat-item {
    padding: 12px 0;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  /* Better button spacing */
  .n-space .n-button {
    flex: 1;
    min-height: 48px;
  }
  
  /* Improved form experience */
  .n-form-item {
    margin-bottom: 18px;
  }
  
  .otp-info {
    padding: 12px;
    font-size: 0.9rem;
  }
  
  /* Alert improvements */
  .n-alert {
    font-size: 0.9rem;
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 0 6px 12px;
  }
  
  .profile-header {
    padding: 12px 10px;
    margin-bottom: 12px;
    border-radius: 8px;
  }
  
  .header-content {
    gap: 12px;
  }
  
  .profile-name {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }
  
  .profile-email {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }
  
  .profile-avatar {
    width: 80px !important;
    height: 80px !important;
    font-size: 32px;
  }
  
  .reputation-badge {
    margin-bottom: 12px;
  }
  
  .reputation-badge .n-tag {
    font-size: 0.85rem;
    padding: 4px 8px;
  }
  
  .member-since {
    font-size: 0.85rem;
  }
  
  /* Ultra-compact cards */
  .status-card,
  .verification-card,
  .success-card,
  .stats-card,
  .actions-card {
    margin-bottom: 10px;
    border-radius: 6px;
  }
  
  .n-card .n-card-header {
    padding: 12px 16px;
    font-size: 1rem;
  }
  
  .n-card__content {
    padding: 12px 16px;
  }
  
  .status-item {
    padding: 10px 0;
  }
  
  .status-header {
    margin-bottom: 8px;
  }
  
  .status-label {
    font-size: 0.9rem;
  }
  
  .stat-item {
    padding: 10px 0;
    gap: 12px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
  
  .stat-icon .n-icon {
    font-size: 20px;
  }
  
  /* Form optimizations for very small screens */
  .n-form-item {
    margin-bottom: 16px;
  }
  
  .n-input {
    font-size: 16px;
    min-height: 44px;
  }
  
  .n-button {
    min-height: 48px;
    font-size: 0.95rem;
    border-radius: 6px;
  }
  
  .form-label {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }
  
  .otp-info {
    padding: 10px;
    font-size: 0.85rem;
    border-radius: 6px;
  }
  
  /* Alert improvements */
  .n-alert {
    font-size: 0.85rem;
    margin-bottom: 12px;
    border-radius: 6px;
  }
  
  /* Quick actions improvements */
  .actions-card .n-button {
    font-size: 0.9rem;
    padding: 8px 12px;
  }
  
  /* Better spacing for phone verification */
  .phone-number {
    font-size: 0.8rem;
    margin-top: 4px;
    display: block;
    width: 100%;
  }
  
  .security-text {
    font-size: 0.8rem;
    margin-left: 0;
    margin-top: 4px;
    display: block;
  }
  
  .resend-btn {
    font-size: 0.8rem;
    margin-top: 4px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .profile-header {
    padding: 16px 20px;
  }
  
  .header-content {
    flex-direction: row;
    text-align: left;
    gap: 20px;
  }
  
  .profile-avatar {
    width: 80px !important;
    height: 80px !important;
  }
  
  .profile-name {
    font-size: 1.8rem;
  }
  
  .avatar-section {
    align-items: flex-start;
  }
}

/* Touch improvements */
@media (hover: none) and (pointer: coarse) {
  .n-button {
    min-height: 48px;
  }
  
  .n-input {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .change-avatar-btn {
    min-height: 36px;
    padding: 6px 12px;
  }
  
  .resend-btn {
    min-height: 36px;
    padding: 4px 8px;
  }
  
  /* Better touch targets for cards */
  .n-card {
    margin-bottom: 12px;
  }
  
  /* Improved form interactions */
  .n-form-item .n-form-item-label {
    margin-bottom: 8px;
  }
  
  /* Better spacing for touch */
  .status-content {
    gap: 12px;
  }
  
  /* Improved button groups */
  .n-space .n-button {
    padding: 12px 16px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .profile-container {
    animation: none;
  }
  
  .n-button {
    transition: none;
  }
  
  .n-button:hover {
    transform: none;
  }
  
  .profile-header,
  .status-card,
  .verification-card,
  .success-card,
  .stats-card,
  .actions-card {
    transition: none;
  }
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
  .profile-header {
    border: 2px solid;
  }
  
  .n-card {
    border: 2px solid;
  }
  
  .n-button {
    border: 2px solid;
  }
}

/* Dark mode mobile adjustments */
@media (max-width: 768px) {
  [data-theme="dark"] .profile-header {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  }
  
  [data-theme="dark"] .status-card,
  [data-theme="dark"] .verification-card,
  [data-theme="dark"] .stats-card,
  [data-theme="dark"] .actions-card {
    background: #1a1a1a;
    border-color: #2a2a2a;
  }
}

/* Animation */
.profile-container {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Improved form spacing */
.n-form-item {
  margin-bottom: 24px;
}

.n-form-item:last-child {
  margin-bottom: 0;
}

/* Better card headers */
.n-card .n-card-header {
  font-weight: 600;
}

[data-theme="light"] .n-card .n-card-header {
  border-bottom: 1px solid #f0f0f5;
}

[data-theme="dark"] .n-card .n-card-header {
  border-bottom: 1px solid #333;
}

/* Enhanced buttons */
.n-button {
  transition: all 0.3s ease;
}

.n-button:hover {
  transform: translateY(-1px);
}

/* Better spacing for grid items */
.n-grid-item {
  margin-bottom: 0;
}

/* Enhanced progress bar */
.n-progress {
  margin-bottom: 8px;
}

/* Improved alert styling */
.n-alert {
  border-radius: 8px;
  border: none;
}

/* Better tooltip styling */
.n-tooltip {
  max-width: 300px;
}
</style>