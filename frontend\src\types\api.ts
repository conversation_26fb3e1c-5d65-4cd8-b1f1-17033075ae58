
/**
 * Represents the structure of rate limit information potentially returned by the API.
 * Allows for flexibility in backend naming conventions (e.g., remaining vs remainingAttempts).
 */
export interface RateLimitInfo {
  remainingAttempts?: number | null;
  remaining?: number | null; // Alternative key
  blockedUntil?: number | null;
  blocked_until?: number | null; // Alternative key
  [key: string]: any; // Allow other potential fields
}

/**
 * Structure for successful OTP request/resend responses.
 */
export interface OtpSuccessResponse {
  message: string;
  rateLimitInfo?: RateLimitInfo | null;
}

/**
 * Structure for successful OTP verification responses.
 */
export interface VerifySuccessResponse {
  message: string;
  // Typically no rateLimitInfo needed on final success, but could be added if backend sends it
}

/**
 * Structure for common API error responses.
 * Covers various potential keys for the error message itself.
 */
export interface ApiErrorResponse {
  error?: string;
  message?: string;
  detail?: string;
  rateLimitInfo?: RateLimitInfo | null;
}

/**
 * Represents the structure of the Axios error object, specifically the response part.
 * Useful for typing mock rejections.
 */
export interface MockApiError {
  response: {
    status: number;
    data: ApiErrorResponse;
  };
}

/**
 * Represents a generic successful API response where only a message is expected.
 */
export interface GenericSuccessResponse {
  message: string;
}
