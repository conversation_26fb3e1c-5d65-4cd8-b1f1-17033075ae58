import { ReceivingInfoStatus, NegotiationStatus } from '@prisma/client';

export interface PaymentReceivingInfoInput {
  id?: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  saveToProfile?: boolean;
  isDefaultForUser?: boolean;
}

export interface PaymentReceivingInfoUpdateInput {
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  isDefaultForUser?: boolean;
}

export interface MappedPaymentReceivingInfo {
  id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  isDefaultForUser: boolean;
}

export interface PayerNegotiationStatePayload {
  negotiationId: string;
  transactionId: string;
  partyA_Id: string;
  partyB_Id: string;
  partyA_receivingInfoStatus: ReceivingInfoStatus;
  partyB_receivingInfoStatus: ReceivingInfoStatus;
  partyA_PaymentReceivingInfo: MappedPaymentReceivingInfo | null;
  partyB_PaymentReceivingInfo: MappedPaymentReceivingInfo | null;
  systemRecommendedPayerId: string | null;
  systemRecommendationReason: string | null;
  systemRecommendationRule: string | null;
  systemRecommendationDetails: any;
  currentProposal_PayerId: string | null;
  currentProposal_ById: string | null;
  currentProposal_Message: string | null;
  partyA_agreedToCurrentProposal: boolean;
  partyB_agreedToCurrentProposal: boolean;
  negotiationStatus: NegotiationStatus;
  finalizedPayerId: string | null;
  paymentTimerDueDate: string | null;
  createdAt: string;
  updatedAt: string;
}