/**
 * Test file to verify user identification in debug reports
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useClientLogger } from '@/composables/useClientLogger';
import { useAuthStore } from '@/stores/auth';

// Mock the auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn()
}));

// Mock other dependencies
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn().mockResolvedValue({
      data: { success: true, reportId: 'test-report-123' }
    })
  }
}));

vi.mock('@/stores/connection', () => ({
  useConnectionStore: vi.fn(() => ({
    isConnected: true,
    connectionQuality: 'excellent',
    connectionStatus: 'Connected',
    transportType: 'websocket',
    reconnectAttempts: 0,
    isReconnecting: false,
    lastDisconnectReason: null
  }))
}));

vi.mock('@/stores/theme', () => ({
  useThemeStore: vi.fn(() => ({
    isDark: false
  }))
}));

vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    getSocket: vi.fn(() => ({
      id: 'socket-123',
      connected: true
    }))
  }
}));

vi.mock('pinia', () => ({
  getActivePinia: vi.fn(() => ({
    state: {
      value: {
        auth: {
          user: { id: 'user-123', email: '<EMAIL>', username: 'testuser' },
          isAuthenticated: true,
          token: 'mock-token'
        },
        connection: {
          isConnected: true,
          connectionQuality: 'excellent'
        },
        theme: {
          isDark: false
        }
      }
    }
  }))
}));

describe('Debug Report User Identification', () => {
  let mockAuthStore: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockAuthStore = {
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        username: 'testuser'
      },
      isAuthenticated: true,
      token: 'mock-token'
    };

    vi.mocked(useAuthStore).mockReturnValue(mockAuthStore);
  });

  it('should capture user identification when user is authenticated', async () => {
    const logger = useClientLogger();
    
    const reportDetails = {
      type: 'bug' as const,
      severity: 'high' as const,
      title: 'Test Bug Report',
      description: 'This is a test bug report with user identification'
    };

    // Mock the API client to capture the payload
    const apiClient = await import('@/services/apiClient');
    const mockPost = vi.mocked(apiClient.default.post);

    await logger.sendLogsToServer(reportDetails);

    expect(mockPost).toHaveBeenCalledWith(
      '/debug/report-issue',
      expect.objectContaining({
        userIdentification: {
          userId: 'user-123',
          email: '<EMAIL>',
          username: 'testuser'
        }
      })
    );
  });

  it('should handle anonymous users gracefully', async () => {
    // Mock unauthenticated user
    mockAuthStore.isAuthenticated = false;
    mockAuthStore.user = null;

    const logger = useClientLogger();
    
    const reportDetails = {
      type: 'bug' as const,
      severity: 'medium' as const,
      title: 'Anonymous Bug Report',
      description: 'This is a test bug report from anonymous user'
    };

    const apiClient = await import('@/services/apiClient');
    const mockPost = vi.mocked(apiClient.default.post);

    await logger.sendLogsToServer(reportDetails);

    expect(mockPost).toHaveBeenCalledWith(
      '/debug/report-issue',
      expect.objectContaining({
        userIdentification: undefined
      })
    );
  });

  it('should capture enhanced store snapshots', async () => {
    const logger = useClientLogger();
    
    const diagnosticData = logger.captureDiagnosticData();

    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('auth');
    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('connection');
    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('theme');

    // Verify auth store data is captured correctly
    expect(diagnosticData.piniaStoreSnapshot.auth).toEqual({
      user: { id: 'user-123', email: '<EMAIL>', username: 'testuser' },
      isAuthenticated: true,
      token: 'mock-token'
    });

    // Verify connection store data
    expect(diagnosticData.piniaStoreSnapshot.connection).toEqual({
      isConnected: true,
      connectionQuality: 'excellent'
    });

    // Verify theme store data
    expect(diagnosticData.piniaStoreSnapshot.theme).toEqual({
      isDark: false
    });
  });

  it('should handle store snapshot capture errors gracefully', async () => {
    // Mock getActivePinia to return null
    const pinia = await import('pinia');
    vi.mocked(pinia.getActivePinia).mockReturnValue(null);

    const logger = useClientLogger();
    const diagnosticData = logger.captureDiagnosticData();

    // Should still capture some store data via fallback mechanism
    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('auth');
    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('connection');
    expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('theme');
  });
});
