/**
 * Demonstration of Enhanced Log Submission with Diagnostic Data
 * 
 * This file shows how the enhanced useClientLogger now automatically
 * captures and includes diagnostic information when submitting logs.
 */

import { useClientLogger } from '@/composables/useClientLogger';
import type { ReportDetails } from '@/types/logging';

// Example usage of the enhanced log submission functionality
export function demonstrateEnhancedLogging() {
  const logger = useClientLogger();

  // Example 1: Basic bug report with automatic diagnostic data
  const submitBugReport = async () => {
    const reportDetails: ReportDetails = {
      type: 'bug',
      severity: 'high',
      title: 'Application crashes when submitting form',
      description: 'The application crashes consistently when I try to submit the contact form.',
      stepsToReproduce: '1. Navigate to contact page\n2. Fill out form\n3. Click submit\n4. Application crashes',
      expectedBehavior: 'Form should submit successfully',
      actualBehavior: 'Application crashes with white screen',
      additionalNotes: 'This happens on both Chrome and Firefox'
    };

    try {
      // This will now automatically include:
      // 1. Connection status (WebSocket state, transport type, reconnection attempts, etc.)
      // 2. Pinia store snapshot (all store states serialized safely)
      // 3. Capture timestamp
      const response = await logger.sendLogsToServer(reportDetails);
      
      console.log('Bug report submitted successfully:', response.reportId);
      return response;
    } catch (error) {
      console.error('Failed to submit bug report:', error);
      throw error;
    }
  };

  // Example 2: Performance issue report
  const submitPerformanceReport = async () => {
    const reportDetails: ReportDetails = {
      type: 'performance',
      severity: 'medium',
      title: 'Slow page loading on offers page',
      description: 'The offers page takes more than 10 seconds to load',
      stepsToReproduce: '1. Navigate to /offers\n2. Observe loading time',
      expectedBehavior: 'Page should load within 3 seconds',
      actualBehavior: 'Page takes 10+ seconds to load',
      reportTags: ['performance', 'offers-page', 'slow-loading']
    };

    const response = await logger.sendLogsToServer(reportDetails);
    return response;
  };

  // Example 3: Manually capture diagnostic data (for debugging purposes)
  const inspectDiagnosticData = () => {
    const diagnosticData = logger.captureDiagnosticData();
    
    console.log('Current Connection Status:', diagnosticData.connectionStatus);
    console.log('Pinia Store Snapshot:', diagnosticData.piniaStoreSnapshot);
    console.log('Capture Timestamp:', diagnosticData.captureTimestamp);
    
    return diagnosticData;
  };

  return {
    submitBugReport,
    submitPerformanceReport,
    inspectDiagnosticData
  };
}

// Example of what the diagnostic data looks like:
export const exampleDiagnosticData = {
  connectionStatus: {
    isConnected: true,
    connectionQuality: 'excellent',
    connectionStatus: 'Connected - Real-time updates',
    transportType: 'websocket',
    reconnectAttempts: 0,
    isReconnecting: false,
    lastDisconnectReason: null,
    socketId: 'socket_abc123',
    socketConnected: true
  },
  piniaStoreSnapshot: {
    auth: {
      user: { id: 'user-123', email: '<EMAIL>' },
      token: 'jwt-token-here',
      isAuthenticated: true
    },
    connection: {
      isConnected: true,
      connectionQuality: 'excellent',
      transportType: 'websocket'
    },
    theme: {
      isDark: true,
      currentTheme: 'dark'
    },
    // ... other stores
  },
  captureTimestamp: '2024-01-15T10:30:00.000Z'
};

// Example of the complete payload sent to server:
export const examplePayload = {
  logs: [
    {
      timestamp: '2024-01-15T10:29:45.123Z',
      level: 'ERROR',
      message: 'Form submission failed',
      context: { formId: 'contact-form', errorCode: 'VALIDATION_ERROR' },
      url: 'http://localhost:5173/contact'
    }
    // ... more log entries
  ],
  reportDetails: {
    type: 'bug',
    severity: 'high',
    title: 'Application crashes when submitting form',
    description: 'The application crashes consistently...',
    // ... other report details
  },
  timestamp: '2024-01-15T10:30:00.000Z',
  sessionId: 'session_1705312200000_abc123',
  
  // NEW: Automatically included diagnostic data
  diagnosticData: exampleDiagnosticData
};

/**
 * Benefits of the Enhanced Logging:
 * 
 * 1. **Automatic Context Capture**: No need to manually gather connection status
 *    or application state - it's captured automatically at submission time.
 * 
 * 2. **Complete Application State**: The Pinia store snapshot provides a complete
 *    picture of the application state when the issue occurred.
 * 
 * 3. **Connection Diagnostics**: Detailed WebSocket connection information helps
 *    diagnose network-related issues.
 * 
 * 4. **Safe Serialization**: Non-serializable values (functions, symbols, etc.)
 *    are handled gracefully without breaking the submission.
 * 
 * 5. **Backward Compatibility**: Existing code continues to work unchanged.
 * 
 * 6. **Clear Metadata**: Each piece of diagnostic data is clearly labeled
 *    and timestamped for easy identification.
 */
