// filepath: c:\Code\MUNygo\frontend\src\utils\__tests__\autofillHandler.test.ts

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AutofillHandler } from '../autofillHandler';

// Mock the client logger
vi.mock('@/composables/useClientLogger', () => ({
  useClientLogger: () => ({
    logInfo: vi.fn(),
    logError: vi.fn(),
    logWarn: vi.fn()
  })
}));

describe('AutofillHandler', () => {
  let autofillHandler: AutofillHandler;
  let originalInsertBefore: typeof Node.prototype.insertBefore;
  let originalRemoveChild: typeof Node.prototype.removeChild;

  beforeEach(() => {
    // Store original DOM methods
    originalInsertBefore = Node.prototype.insertBefore;
    originalRemoveChild = Node.prototype.removeChild;
    
    // Create a new instance for each test
    autofillHandler = new AutofillHandler({
      enableMutationObserver: false, // Disable for testing
      enableFormStabilization: false, // Disable for testing
      debugMode: true
    });
  });

  afterEach(() => {
    // Cleanup after each test
    autofillHandler.cleanup();
    
    // Restore original DOM methods
    Node.prototype.insertBefore = originalInsertBefore;
    Node.prototype.removeChild = originalRemoveChild;
  });

  describe('initialization', () => {
    it('should initialize without errors', () => {
      expect(() => autofillHandler.initialize()).not.toThrow();
    });

    it('should not initialize twice', () => {
      autofillHandler.initialize();
      expect(() => autofillHandler.initialize()).not.toThrow();
    });
  });

  describe('DOM method interception', () => {
    beforeEach(() => {
      autofillHandler.initialize();
    });

    it('should intercept insertBefore and handle autofill errors gracefully', () => {
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');
      const mockReference = document.createElement('p');

      // Add the reference node to the parent first
      mockParent.appendChild(mockReference);

      // Store the intercepted method
      const interceptedMethod = Node.prototype.insertBefore;

      // Mock the original method to throw an autofill error
      autofillHandler['originalInsertBefore'] = vi.fn().mockImplementation(() => {
        const error = new Error('Failed to execute insertBefore on Node: bootstrap-autofill-overlay');
        error.name = 'NotFoundError';
        throw error;
      });

      // This should not throw an error due to interception
      expect(() => {
        mockParent.insertBefore(mockChild, mockReference);
      }).not.toThrow();
    });

    it('should intercept removeChild and handle autofill errors gracefully', () => {
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');

      // Add the child to the parent first
      mockParent.appendChild(mockChild);

      // Mock the original method to throw an autofill error
      autofillHandler['originalRemoveChild'] = vi.fn().mockImplementation(() => {
        const error = new Error('Failed to execute removeChild on Node: AutofillInlineMenuContentService');
        error.name = 'NotFoundError';
        throw error;
      });

      // This should not throw an error due to interception
      expect(() => {
        mockParent.removeChild(mockChild);
      }).not.toThrow();
    });

    it('should re-throw non-autofill errors', () => {
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');

      // Add the child to the parent first
      mockParent.appendChild(mockChild);

      // Mock the original method to throw a non-autofill error
      autofillHandler['originalRemoveChild'] = vi.fn().mockImplementation(() => {
        throw new Error('Some other DOM error');
      });

      // This should throw the error
      expect(() => {
        mockParent.removeChild(mockChild);
      }).toThrow('Some other DOM error');
    });
  });

  describe('error detection', () => {
    it('should correctly identify autofill errors', () => {
      const autofillErrors = [
        new Error('bootstrap-autofill-overlay.js:16'),
        new Error('AutofillInlineMenuContentService error'),
        new Error('Failed to execute insertBefore on Node: not a child'),
        new Error('Failed to execute removeChild on Node: not a child'),
        new Error('autofill overlay manipulation failed')
      ];

      autofillErrors.forEach(error => {
        expect(AutofillHandler['isAutofillError'](error)).toBe(true);
      });
    });

    it('should not identify non-autofill errors as autofill errors', () => {
      const nonAutofillErrors = [
        new Error('Regular DOM error'),
        new Error('Network request failed'),
        new Error('Validation error'),
        null,
        undefined,
        'string error'
      ];

      nonAutofillErrors.forEach(error => {
        expect(AutofillHandler['isAutofillError'](error)).toBe(false);
      });
    });
  });

  describe('cleanup', () => {
    it('should cleanup properly', () => {
      autofillHandler.initialize();
      expect(() => autofillHandler.cleanup()).not.toThrow();
    });

    it('should restore original DOM methods on cleanup', () => {
      autofillHandler.initialize();
      
      // Methods should be intercepted
      expect(Node.prototype.insertBefore).not.toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).not.toBe(originalRemoveChild);
      
      autofillHandler.cleanup();
      
      // Methods should be restored
      expect(Node.prototype.insertBefore).toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).toBe(originalRemoveChild);
    });
  });

  describe('configuration', () => {
    it('should respect configuration options', () => {
      const handler = new AutofillHandler({
        enableErrorSuppression: false,
        enableMutationObserver: false,
        enableFormStabilization: false,
        debugMode: false
      });

      handler.initialize();
      
      // Methods should not be intercepted when error suppression is disabled
      expect(Node.prototype.insertBefore).toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).toBe(originalRemoveChild);
      
      handler.cleanup();
    });
  });

  describe('form stabilization', () => {
    it('should add stabilization styles when enabled', () => {
      // Mock document.head for testing environment
      const mockHead = document.createElement('head');
      Object.defineProperty(document, 'head', {
        value: mockHead,
        writable: true
      });

      const handler = new AutofillHandler({
        enableFormStabilization: true,
        enableErrorSuppression: false,
        enableMutationObserver: false
      });

      handler.initialize();

      // Check if styles were added
      const styles = mockHead.querySelectorAll('style');
      const autofillStyles = Array.from(styles).find(style =>
        style.textContent?.includes('autofill-stable')
      );

      expect(autofillStyles).toBeTruthy();

      handler.cleanup();
    });
  });
});
