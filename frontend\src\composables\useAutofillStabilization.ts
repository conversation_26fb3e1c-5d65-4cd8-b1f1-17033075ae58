// filepath: c:\Code\MUNygo\frontend\src\composables\useAutofillStabilization.ts

/**
 * Composable for autofill stabilization in Vue components
 * 
 * Provides utilities to stabilize form elements and prevent
 * autofill overlay DOM manipulation conflicts.
 */

import { onMounted, onUnmounted, ref, type Ref } from 'vue';
import { autofillHandler } from '@/utils/autofillHandler';

interface UseAutofillStabilizationOptions {
  /**
   * Automatically apply stabilization classes to form elements
   */
  autoStabilize?: boolean;
  
  /**
   * CSS selector for elements to stabilize
   */
  selector?: string;
  
  /**
   * Additional CSS classes to apply for stabilization
   */
  additionalClasses?: string[];
}

interface UseAutofillStabilizationReturn {
  /**
   * Manually stabilize a specific element
   */
  stabilizeElement: (element: HTMLElement) => void;
  
  /**
   * Remove stabilization from a specific element
   */
  unstabilizeElement: (element: HTMLElement) => void;
  
  /**
   * Stabilize all form elements in the component
   */
  stabilizeForm: () => void;
  
  /**
   * Check if autofill handler is active
   */
  isStabilized: Ref<boolean>;
}

/**
 * Use autofill stabilization for form components
 */
export function useAutofillStabilization(
  options: UseAutofillStabilizationOptions = {}
): UseAutofillStabilizationReturn {
  const {
    autoStabilize = true,
    selector = 'input[type="email"], input[type="password"], input[type="text"], input[type="tel"], .n-input, .n-form-item',
    additionalClasses = []
  } = options;

  const isStabilized = ref(false);
  const stabilizedElements = new Set<HTMLElement>();

  /**
   * Apply stabilization classes to an element
   */
  const stabilizeElement = (element: HTMLElement): void => {
    if (!element || stabilizedElements.has(element)) {
      return;
    }

    // Add base stabilization class
    element.classList.add('autofill-stable');
    
    // Add any additional classes
    additionalClasses.forEach(className => {
      element.classList.add(className);
    });

    // Track stabilized elements
    stabilizedElements.add(element);
  };

  /**
   * Remove stabilization classes from an element
   */
  const unstabilizeElement = (element: HTMLElement): void => {
    if (!element || !stabilizedElements.has(element)) {
      return;
    }

    // Remove base stabilization class
    element.classList.remove('autofill-stable');
    
    // Remove any additional classes
    additionalClasses.forEach(className => {
      element.classList.remove(className);
    });

    // Remove from tracking
    stabilizedElements.delete(element);
  };

  /**
   * Stabilize all form elements in the current component
   */
  const stabilizeForm = (): void => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((element) => {
      if (element instanceof HTMLElement) {
        stabilizeElement(element);
      }
    });
    isStabilized.value = true;
  };

  /**
   * Remove stabilization from all tracked elements
   */
  const cleanup = (): void => {
    stabilizedElements.forEach(element => {
      unstabilizeElement(element);
    });
    stabilizedElements.clear();
    isStabilized.value = false;
  };

  // Auto-stabilize on mount if enabled
  onMounted(() => {
    if (autoStabilize) {
      // Use a small delay to ensure DOM is fully rendered
      setTimeout(() => {
        stabilizeForm();
      }, 50);
    }
  });

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    stabilizeElement,
    unstabilizeElement,
    stabilizeForm,
    isStabilized
  };
}

/**
 * Directive for autofill stabilization
 * 
 * Usage: v-autofill-stable
 */
export const vAutofillStable = {
  mounted(el: HTMLElement) {
    el.classList.add('autofill-stable');
  },
  unmounted(el: HTMLElement) {
    el.classList.remove('autofill-stable');
  }
};

/**
 * Utility function to check if current browser has autofill issues
 */
export function hasAutofillIssues(): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Chrome-based browsers are most likely to have autofill overlay issues
  const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
  const isEdge = userAgent.includes('edg');
  const isBrave = userAgent.includes('brave');
  
  return isChrome || isEdge || isBrave;
}

/**
 * Utility function to detect if autofill is likely active
 */
export function isAutofillActive(): boolean {
  // Check for common autofill indicators
  const autofillInputs = document.querySelectorAll('input:-webkit-autofill');
  return autofillInputs.length > 0;
}
