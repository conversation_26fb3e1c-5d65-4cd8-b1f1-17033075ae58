/**
 * Admin Dashboard Enhancement Demo
 * 
 * This file demonstrates how the enhanced admin dashboard now displays
 * diagnostic data from user log submissions.
 */

import type { ParsedReport, DiagnosticData } from '@/types/admin';

// Example of a legacy report (without diagnostic data)
export const legacyReportExample: ParsedReport = {
  reportId: 'report_legacy_001',
  timestamp: '2024-01-15T09:00:00.000Z',
  serverReceivedAt: '2024-01-15T09:00:01.000Z',
  clientTimestamp: '2024-01-15T09:00:00.000Z',
  sessionId: 'session_legacy_001',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  currentUrl: 'https://munygo.com/offers',
  reportType: 'bug',
  reportSeverity: 'high',
  reportTitle: 'Application crashes when submitting offer',
  reportDescription: 'The application crashes when I try to submit a new offer',
  stepsToReproduce: '1. Navigate to offers page\n2. Click "Create Offer"\n3. Fill form\n4. Click submit',
  expectedBehavior: 'Offer should be created successfully',
  actualBehavior: 'Application crashes with white screen',
  tags: ['crash', 'offers', 'form-submission'],
  hasTags: true,
  logCount: 15,
  logs: [
    {
      timestamp: '2024-01-15T08:59:45.123Z',
      level: 'ERROR',
      message: 'Form validation failed',
      context: { formId: 'offer-form', errorCode: 'VALIDATION_ERROR' },
      url: 'https://munygo.com/offers/create'
    },
    {
      timestamp: '2024-01-15T08:59:50.456Z',
      level: 'ERROR',
      message: 'Uncaught TypeError: Cannot read property of undefined',
      context: { component: 'OfferForm' },
      url: 'https://munygo.com/offers/create',
      stackTrace: 'TypeError: Cannot read property of undefined\n    at OfferForm.vue:123:45'
    }
  ],
  // No diagnosticData field - this is a legacy report
};

// Example of an enhanced report (with diagnostic data)
export const enhancedReportExample: ParsedReport = {
  reportId: 'report_enhanced_001',
  timestamp: '2024-01-15T10:30:00.000Z',
  serverReceivedAt: '2024-01-15T10:30:01.000Z',
  clientTimestamp: '2024-01-15T10:30:00.000Z',
  sessionId: 'session_enhanced_001',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  currentUrl: 'https://munygo.com/chat',
  reportType: 'performance',
  reportSeverity: 'medium',
  reportTitle: 'Chat messages loading slowly',
  reportDescription: 'Chat messages take more than 5 seconds to load',
  stepsToReproduce: '1. Navigate to chat\n2. Select a conversation\n3. Observe loading time',
  expectedBehavior: 'Messages should load within 2 seconds',
  actualBehavior: 'Messages take 5+ seconds to load',
  tags: ['performance', 'chat', 'slow-loading'],
  hasTags: true,
  logCount: 25,
  logs: [
    {
      timestamp: '2024-01-15T10:29:45.123Z',
      level: 'WARN',
      message: 'Chat API response time exceeded threshold',
      context: { 
        apiEndpoint: '/api/chat/messages',
        responseTime: 5200,
        threshold: 2000
      },
      url: 'https://munygo.com/chat'
    },
    {
      timestamp: '2024-01-15T10:29:50.456Z',
      level: 'INFO',
      message: 'WebSocket reconnection attempt',
      context: { 
        attempt: 2,
        reason: 'connection_timeout'
      },
      url: 'https://munygo.com/chat'
    }
  ],
  
  // NEW: Enhanced diagnostic data
  diagnosticData: {
    connectionStatus: {
      isConnected: true,
      connectionQuality: 'poor',
      connectionStatus: 'Connected - Limited functionality',
      transportType: 'websocket',
      reconnectAttempts: 2,
      isReconnecting: false,
      lastDisconnectReason: 'connection_timeout',
      socketId: 'socket_enhanced_001',
      socketConnected: true
    },
    piniaStoreSnapshot: {
      auth: {
        user: {
          id: 'user_123',
          email: '<EMAIL>',
          name: 'John Doe',
          avatar: 'https://example.com/avatar.jpg'
        },
        token: 'jwt_token_here',
        isAuthenticated: true,
        permissions: ['chat', 'offers', 'transactions']
      },
      connection: {
        isConnected: true,
        connectionQuality: 'poor',
        connectionStatus: 'Connected - Limited functionality',
        transportType: 'websocket',
        reconnectAttempts: 2,
        isReconnecting: false,
        lastDisconnectReason: 'connection_timeout'
      },
      chatStore: {
        activeConversation: {
          id: 'conv_456',
          participantId: 'user_789',
          participantName: 'Jane Smith',
          lastMessage: {
            id: 'msg_999',
            content: 'Hello, is this still available?',
            timestamp: '2024-01-15T10:25:00.000Z',
            senderId: 'user_789'
          }
        },
        conversations: [
          {
            id: 'conv_456',
            participantId: 'user_789',
            participantName: 'Jane Smith',
            unreadCount: 1,
            lastActivity: '2024-01-15T10:25:00.000Z'
          }
        ],
        isLoading: true,
        loadingMessages: true
      },
      theme: {
        isDark: false,
        currentTheme: 'light',
        primaryColor: '#1890ff',
        fontSize: 'medium'
      },
      myOffersStore: {
        offers: [
          {
            id: 'offer_123',
            title: 'MacBook Pro 2023',
            price: 2500,
            status: 'active',
            views: 45,
            interests: 3
          }
        ],
        totalOffers: 1,
        activeOffers: 1,
        isLoading: false
      },
      notificationStore: {
        notifications: [
          {
            id: 'notif_001',
            type: 'message',
            title: 'New message from Jane Smith',
            message: 'Hello, is this still available?',
            timestamp: '2024-01-15T10:25:00.000Z',
            isRead: false
          }
        ],
        unreadCount: 1,
        isEnabled: true
      },
      transactionStore: {
        activeTransactions: [],
        completedTransactions: [
          {
            id: 'txn_001',
            offerId: 'offer_456',
            buyerId: 'user_789',
            amount: 1200,
            status: 'completed',
            completedAt: '2024-01-10T15:30:00.000Z'
          }
        ],
        pendingPayments: [],
        totalEarnings: 1200
      }
    },
    captureTimestamp: '2024-01-15T10:30:00.000Z'
  }
};

/**
 * How the Admin Dashboard Now Displays This Data:
 * 
 * 1. LEGACY REPORTS (without diagnostic data):
 *    - Shows "Legacy Report" tag
 *    - Displays "No Diagnostic Data" message
 *    - Explains that diagnostic data wasn't available for older reports
 *    - All other report information displays normally
 * 
 * 2. ENHANCED REPORTS (with diagnostic data):
 *    - Shows "Enhanced Report" tag
 *    - Displays rich diagnostic information in organized sections:
 * 
 *    CONNECTION STATUS SECTION:
 *    ┌─────────────────────────────────────────┐
 *    │ 🌐 Connection Status                    │
 *    ├─────────────────────────────────────────┤
 *    │ Status: ⚠️ Connected - Limited functionality │
 *    │ Quality: 🟡 Poor                        │
 *    │ Transport: websocket                    │
 *    │ Socket ID: socket_enhanced_001          │
 *    │ Reconnect Attempts: 2                   │
 *    │ Last Disconnect: connection_timeout     │
 *    └─────────────────────────────────────────┘
 * 
 *    APPLICATION STATE SECTION:
 *    ┌─────────────────────────────────────────┐
 *    │ 💾 Application State Snapshot (7 stores) │
 *    ├─────────────────────────────────────────┤
 *    │ ▼ 🔐 Authentication (1.2 KB)           │
 *    │   └─ User: John Doe (user_123)          │
 *    │   └─ Authenticated: ✅                  │
 *    │                                         │
 *    │ ▼ 💬 Chat (2.1 KB)                     │
 *    │   └─ Active Conversation: Jane Smith    │
 *    │   └─ Loading Messages: ⏳              │
 *    │                                         │
 *    │ ▼ 🎨 Theme Settings (0.3 KB)           │
 *    │   └─ Mode: Light                        │
 *    │   └─ Primary Color: #1890ff             │
 *    │                                         │
 *    │ ... (expandable sections for each store) │
 *    └─────────────────────────────────────────┘
 * 
 *    CAPTURE INFORMATION:
 *    ┌─────────────────────────────────────────┐
 *    │ ⏰ Capture Information                  │
 *    ├─────────────────────────────────────────┤
 *    │ Captured At: 2024/01/15 10:30:00       │
 *    │ Time Since Capture: 5 minutes ago      │
 *    └─────────────────────────────────────────┘
 */

/**
 * Benefits for Support Teams:
 * 
 * 1. IMMEDIATE CONTEXT:
 *    - See exactly what the user was doing when the issue occurred
 *    - Understand the application state at the time of the problem
 *    - Identify connection issues that might be causing problems
 * 
 * 2. FASTER DEBUGGING:
 *    - No need to ask users for additional information
 *    - Connection quality immediately visible
 *    - Application state shows potential conflicts or issues
 * 
 * 3. BETTER PROBLEM RESOLUTION:
 *    - Can correlate user actions with application state
 *    - Identify patterns across multiple reports
 *    - Proactively address connection or performance issues
 * 
 * 4. ENHANCED USER EXPERIENCE:
 *    - Users don't need to provide technical details
 *    - Automatic capture means nothing is missed
 *    - Faster resolution leads to better user satisfaction
 */

export const adminDashboardFeatures = {
  legacySupport: {
    description: 'Gracefully handles reports without diagnostic data',
    features: [
      'Clear "Legacy Report" indication',
      'Explanation of missing diagnostic data',
      'All existing functionality preserved'
    ]
  },
  
  enhancedVisualization: {
    description: 'Rich display of diagnostic information',
    features: [
      'Color-coded connection status indicators',
      'Expandable/collapsible store data sections',
      'Formatted JSON with syntax highlighting',
      'Data size indicators for each store',
      'Responsive design for all devices'
    ]
  },
  
  userExperience: {
    description: 'Optimized for support team efficiency',
    features: [
      'Progressive disclosure of information',
      'Visual indicators for quick assessment',
      'Search and filter capabilities',
      'Export functionality for further analysis',
      'Mobile-friendly responsive design'
    ]
  }
};
