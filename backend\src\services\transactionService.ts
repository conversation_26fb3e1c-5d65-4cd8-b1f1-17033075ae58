import { z } from 'zod';
import { PrismaClient, Prisma, Transaction, TransactionStatus, User as PrismaUser, NotificationType as PrismaNotificationType } from '@prisma/client'; // Renamed User to PrismaUser to avoid conflict if User type is defined elsewhere
import { Server } from 'socket.io';
import { NotificationService } from './notificationService';
import { ChatService } from './chatService';
import { TRANSACTION_STATUS_UPDATED, SYSTEM_MESSAGE_RECEIVE, type SystemMessagePayload, type TransactionStatusUpdatePayload } from '../types/socketEvents'; // Ensure CHAT_MESSAGE_RECEIVE is correctly defined if used by createAndEmitSystemMessage

const prisma = new PrismaClient(); // Keep this for standalone functions if they remain

const DEFAULT_PAYMENT_WINDOW_HOURS = 2;

// --- Zod Schemas for API Validation ---
export const AgreeToTermsSchema = z.object({}); // This schema is for the route validator

export const DesignateFirstPayerSchema = z.object({
  designatedPayerId: z.string().cuid({ message: 'Valid User ID for designated payer is required.' }),
});

export const DeclarePaymentSchema = z.object({
  trackingNumber: z.string().optional(),
});

export const ConfirmReceiptSchema = z.object({});

export const CancelTransactionSchema = z.object({
  reason: z.string().min(1, { message: 'A reason for cancellation is required.' }),
});

export const DisputeTransactionSchema = z.object({
  reason: z.string().min(1, { message: 'A reason for the dispute is required.' }),
});
// --- End Zod Schemas ---

// Define the payload for socket emission, reflecting the Prisma model


async function determineSystemRecommendation(
  currencyAProviderId: string,
  currencyBProviderId: string,
  currencyA: string,
  currencyB: string
): Promise<string> {
  console.log(`[TransactionService] Determining system recommendation for first payer between ${currencyAProviderId} and ${currencyBProviderId}`);
  
  // Fetch user details including reputation
  const [userA, userB] = await Promise.all([
    prisma.user.findUnique({ 
      where: { id: currencyAProviderId },
      select: { id: true, reputationLevel: true, reputationScore: true }
    }),
    prisma.user.findUnique({ 
      where: { id: currencyBProviderId },
      select: { id: true, reputationLevel: true, reputationScore: true }
    })
  ]);

  if (!userA || !userB) {
    throw new Error('Unable to fetch user details for system recommendation');
  }

  // Business logic for determining system recommendation:
  // 1. Lower reputation user pays first (builds trust)
  if (userA.reputationLevel !== userB.reputationLevel) {
    const recommendedPayerId = userA.reputationLevel < userB.reputationLevel ? userA.id : userB.id;
    console.log(`[TransactionService] System recommendation based on reputation level: ${recommendedPayerId}`);
    return recommendedPayerId;
  }

  // 2. If reputation levels are equal, use reputation score
  if (userA.reputationScore !== userB.reputationScore) {
    const recommendedPayerId = userA.reputationScore < userB.reputationScore ? userA.id : userB.id;
    console.log(`[TransactionService] System recommendation based on reputation score: ${recommendedPayerId}`);
    return recommendedPayerId;
  }

  // 3. If reputation is equal, IRR payer goes first (if applicable)
  if (currencyA === 'IRR' && currencyB !== 'IRR') {
    console.log(`[TransactionService] System recommendation based on currency (IRR): ${userA.id}`);
    return userA.id; // currencyA provider (IRR) pays first
  }
  if (currencyB === 'IRR' && currencyA !== 'IRR') {
    console.log(`[TransactionService] System recommendation based on currency (IRR): ${userB.id}`);
    return userB.id; // currencyB provider (IRR) pays first
  }

  // 4. Default: currencyA provider pays first (arbitrary but consistent)
  console.log(`[TransactionService] System recommendation by default: ${userA.id}`);
  return userA.id;
}

export async function createInitialTransaction(
  tx: Prisma.TransactionClient, // Prisma.TransactionClient is correct for use within a $transaction
  chatSessionId: string,
  offerId: string, // Added offerId as per schema
  currencyA: string,
  amountA: number,
  currencyAProviderId: string,
  currencyB: string,
  amountB: number,
  currencyBProviderId: string
): Promise<Transaction> {
  console.log(`[TransactionService] Creating initial transaction. ChatID: ${chatSessionId}, OfferID: ${offerId}, CurrencyA: ${currencyA}, AmountA: ${amountA}, ProviderA: ${currencyAProviderId}, CurrencyB: ${currencyB}, AmountB: ${amountB}, ProviderB: ${currencyBProviderId}`);
  try {
    const transaction = await tx.transaction.create({
      data: {
        // Scalar fields (non-relational)
        status: 'AWAITING_FIRST_PAYER_DESIGNATION',
        currencyA: currencyA,
        amountA: amountA,
        currencyB: currencyB,
        amountB: amountB,

        // Relational connects
        chatSession: {
          connect: { id: chatSessionId }
        },
        offer: {
          connect: { id: offerId }
        },
        currencyAProvider: {
          connect: { id: currencyAProviderId }
        },
        currencyBProvider: {
          connect: { id: currencyBProviderId }
        }
      },    });

    console.log(`[TransactionService] Created initial transaction: ${transaction.id} with status AWAITING_FIRST_PAYER_DESIGNATION`);
    return transaction;
  } catch (error: any) {
    console.error('[transactionService] Error creating initial transaction:', error);
    throw error;
  }
}

export async function getTransactionByChatSessionId(chatSessionId: string, userId: string): Promise<Transaction | null> {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: { chatSessionId },
      include: {
        currencyAProvider: {
          select: {
            id: true,
            username: true,
          },
        },
        currencyBProvider: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });
    if (!transaction) {
      console.log(`[TransactionService] Transaction not found for chatSessionId: ${chatSessionId}`);
      return null;
    }
    if (userId !== transaction.currencyAProviderId && userId !== transaction.currencyBProviderId) {
      console.warn(`[TransactionService] Unauthorized attempt to get transaction for chat ${chatSessionId} (TX ID: ${transaction.id}) by user ${userId}`);
      return null;
    }
    console.log(`[TransactionService] Fetched transaction by chatSessionId: ${chatSessionId} (TX ID: ${transaction.id}) for user ${userId}`);
    return transaction as Transaction & {
      currencyAProvider: { id: string; username: string | null };
      currencyBProvider: { id: string; username: string | null };
    };
  } catch (error) {
    console.error(`[TransactionService] Error fetching transaction by chatSessionId ${chatSessionId}:`, error);
    throw error;
  }
}

export class TransactionService {
  private io: Server;
  private notificationService: NotificationService;
  private chatService: ChatService;

  constructor(io: Server, notificationService: NotificationService, chatService: ChatService) {
    this.io = io;
    this.notificationService = notificationService;
    this.chatService = chatService;
    console.log('[TransactionService] Initialized with IO, NotificationService, and ChatService');
  }  private calculatePaymentDueDate(baseDate: Date, durationInHours: number): Date {
    const dueDate = new Date(baseDate.getTime());
    dueDate.setHours(dueDate.getHours() + durationInHours);
    return dueDate;
  }
  private async emitTransactionStatusUpdate(
    transaction: Transaction & {
      currencyAProvider?: { username: string | null };
      currencyBProvider?: { username: string | null };
    }
  ): Promise<void> {
    // Ensure we have provider usernames - fetch if not included
    let currencyAProviderUsername = transaction.currencyAProvider?.username || null;
    let currencyBProviderUsername = transaction.currencyBProvider?.username || null;
    
    if (!currencyAProviderUsername || !currencyBProviderUsername) {
      const [currencyAProvider, currencyBProvider] = await Promise.all([
        prisma.user.findUnique({ where: { id: transaction.currencyAProviderId }, select: { username: true } }),
        prisma.user.findUnique({ where: { id: transaction.currencyBProviderId }, select: { username: true } })
      ]);
      currencyAProviderUsername = currencyAProvider?.username || null;
      currencyBProviderUsername = currencyBProvider?.username || null;
    }    const payload: TransactionStatusUpdatePayload = {
      transactionId: transaction.id,
      chatSessionId: transaction.chatSessionId,
      offerId: transaction.offerId,
      status: transaction.status,
      currencyA: transaction.currencyA,
      amountA: transaction.amountA,
      currencyAProviderId: transaction.currencyAProviderId,
      currencyAProviderUsername: currencyAProviderUsername,
      currencyB: transaction.currencyB,
      amountB: transaction.amountB,
      currencyBProviderId: transaction.currencyBProviderId,
      currencyBProviderUsername: currencyBProviderUsername,
      termsAgreementTimestampPayer1: transaction.termsAgreementTimestampPayer1?.toISOString() || null,
      termsAgreementTimestampPayer2: transaction.termsAgreementTimestampPayer2?.toISOString() || null,
      agreedFirstPayerId: transaction.agreedFirstPayerId,
      firstPayerDesignationTimestamp: transaction.firstPayerDesignationTimestamp?.toISOString() || null,
      paymentExpectedByPayer1: transaction.paymentExpectedByPayer1?.toISOString() || null,
      paymentDeclaredAtPayer1: transaction.paymentDeclaredAtPayer1?.toISOString() || null,
      paymentTrackingNumberPayer1: transaction.paymentTrackingNumberPayer1,
      firstPaymentConfirmedByPayer2At: transaction.firstPaymentConfirmedByPayer2At?.toISOString() || null,
      paymentExpectedByPayer2: transaction.paymentExpectedByPayer2?.toISOString() || null,
      paymentDeclaredAtPayer2: transaction.paymentDeclaredAtPayer2?.toISOString() || null,
      paymentTrackingNumberPayer2: transaction.paymentTrackingNumberPayer2,
      secondPaymentConfirmedByPayer1At: transaction.secondPaymentConfirmedByPayer1At?.toISOString() || null,
      cancellationReason: transaction.cancellationReason,
      cancelledByUserId: transaction.cancelledByUserId,
      disputeReason: transaction.disputeReason,
      disputedByUserId: transaction.disputedByUserId,
      disputeResolvedAt: transaction.disputeResolvedAt?.toISOString() || null,
      disputeResolutionNotes: transaction.disputeResolutionNotes,      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
    };
    
    // Debug log the payload being sent
    console.log(`[TransactionService] DEBUG: Emitting payload with usernames - A: "${currencyAProviderUsername}", B: "${currencyBProviderUsername}"`);
    
    // Get the chat session to find the participants
    const chatSession = await prisma.chatSession.findUnique({
      where: { id: transaction.chatSessionId },
      select: { userOneId: true, userTwoId: true },
    });

    if (!chatSession) {
      console.error(`[TransactionService] Error: Chat session not found for transaction status update: ${transaction.chatSessionId}`);
      return;
    }
    
    // Emit to both participants using their user ID rooms (same pattern as chat service)
    this.io.to(chatSession.userOneId).emit(TRANSACTION_STATUS_UPDATED, payload);
    if (chatSession.userTwoId !== chatSession.userOneId) {
      this.io.to(chatSession.userTwoId).emit(TRANSACTION_STATUS_UPDATED, payload);
    }
    
    console.log(`[TransactionService] Emitted ${TRANSACTION_STATUS_UPDATED} for transaction ${transaction.id} to user rooms ${chatSession.userOneId} and ${chatSession.userTwoId}`);
  }  private async createAndEmitSystemMessage(
    chatSessionId: string,
    messageContent: string,
    transactionId: string
  ): Promise<void> {
    try {
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        select: { userOneId: true, userTwoId: true },
      });

      if (!chatSession) {
        console.error(`[TransactionService] Error: Chat session not found for system message: ${chatSessionId}`);
        return;
      }

      // Save system message to database for persistence (fixing Issue 2)
      const message = await prisma.chatMessage.create({
        data: {
          chatSessionId,
          content: messageContent,
          isSystemMessage: true,
          senderId: null, // System messages don't have a user sender
          transactionId: transactionId,
        },
      });

      const systemMessagePayload: SystemMessagePayload = {
        messageId: message.id, // Use database message ID instead of generated one
        chatSessionId,
        content: messageContent,
        createdAt: message.createdAt.toISOString(), // Use actual database timestamp
        isSystemMessage: true,
        transactionId: transactionId,
      };

      if (chatSession.userOneId) {
        this.io.to(chatSession.userOneId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      }
      if (chatSession.userTwoId && chatSession.userTwoId !== chatSession.userOneId) {
        this.io.to(chatSession.userTwoId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      }
      
      console.log(`[TransactionService] System message saved to database and emitted via ${SYSTEM_MESSAGE_RECEIVE} to participants of chat ${chatSessionId}, transaction ${transactionId}: ${messageContent}`);
    } catch (error) {
      console.error(`[TransactionService] Error creating/emitting system message for chat ${chatSessionId}:`, error);
    }
  }

  async getTransactionById(transactionId: string, userId: string): Promise<Transaction | null> {
    const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          currencyAProvider: { select: { id: true, username: true } },
          currencyBProvider: { select: { id: true, username: true } },
        },
    });
    if (!transaction) return null;
    if (userId !== transaction.currencyAProviderId && userId !== transaction.currencyBProviderId) {
        console.warn(`[TransactionService] Unauthorized attempt to get transaction ${transactionId} by user ${userId}`);
        return null;
    }
    return transaction as Transaction & { // Cast is okay if include makes these fields available
      currencyAProvider: { id: string; username: string | null };
      currencyBProvider: { id: string; username: string | null };
    };
  }

  async agreeToTerms(transactionId: string, userId: string): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} attempting to agree to terms for transaction ${transactionId}`);

    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        currencyAProvider: { select: { id: true, username: true, email: true } }, // email for username fallback
        currencyBProvider: { select: { id: true, username: true, email: true } },
      },
    });

    if (!transaction) {
      console.error(`[TransactionService] agreeToTerms: Transaction ${transactionId} not found.`);
      throw new Error('Transaction not found.');
    }

    const isPartyA = transaction.currencyAProviderId === userId;
    const isPartyB = transaction.currencyBProviderId === userId;

    if (!isPartyA && !isPartyB) {
      console.error(`[TransactionService] agreeToTerms: User ${userId} is not a party to transaction ${transactionId}.`);
      throw new Error('User is not authorized to agree to terms for this transaction.');
    }
    
    // Allow agreeing if the transaction is in AWAITING_FIRST_PAYER_DESIGNATION
    // and the current user hasn't agreed yet.
    if (transaction.status !== TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION) {
        console.warn(`[TransactionService] agreeToTerms: Transaction ${transactionId} is not in AWAITING_FIRST_PAYER_DESIGNATION state. Current status: ${transaction.status}.`);
        throw new Error('Transaction is not in the correct state to agree to terms.');
    }

    let updatedData: Prisma.TransactionUpdateInput = {};
    let systemMessage = '';
    const currentUserUsername = isPartyA 
      ? (transaction.currencyAProvider.username || transaction.currencyAProvider.email || `User ${userId.substring(0,6)}`) 
      : (transaction.currencyBProvider.username || transaction.currencyBProvider.email || `User ${userId.substring(0,6)}`);
    const otherPartyUsername = isPartyA
      ? (transaction.currencyBProvider.username || transaction.currencyBProvider.email || 'The other party')
      : (transaction.currencyAProvider.username || transaction.currencyAProvider.email || 'The other party');
    const otherPartyId = isPartyA ? transaction.currencyBProviderId : transaction.currencyAProviderId;

    if (isPartyA) {
      if (transaction.termsAgreementTimestampPayer1) {
        console.warn(`[TransactionService] agreeToTerms: User ${userId} (Party A) has already agreed to terms for transaction ${transactionId}.`);
        throw new Error('You have already agreed to the terms.');
      }
      updatedData.termsAgreementTimestampPayer1 = new Date();
    } else { // isPartyB
      if (transaction.termsAgreementTimestampPayer2) {
        console.warn(`[TransactionService] agreeToTerms: User ${userId} (Party B) has already agreed to terms for transaction ${transactionId}.`);
        throw new Error('You have already agreed to the terms.');
      }
      updatedData.termsAgreementTimestampPayer2 = new Date();
    }
    
    systemMessage = `${currentUserUsername} has agreed to the transaction terms.`;    const tempUpdatedTransaction = { ...transaction, ...updatedData }; // For checking both timestamps

    // Check if both parties have now agreed
    const bothPartiesAgreed = tempUpdatedTransaction.termsAgreementTimestampPayer1 && tempUpdatedTransaction.termsAgreementTimestampPayer2;
    
    let finalUpdatedTransaction;
    
    if (bothPartiesAgreed) {
      // Both parties have agreed - automatically designate first payer and progress to payment
      console.log(`[TransactionService] Both parties agreed, auto-designating first payer for transaction ${transactionId}`);
      
      const designatedFirstPayerId = await this.determineFirstPayer(transaction);
      const paymentDueDate = this.calculatePaymentDueDate(new Date(), DEFAULT_PAYMENT_WINDOW_HOURS);
      const designationTimestamp = new Date();      // Update transaction with both agreement timestamps AND first payer designation
      const updateData: Prisma.TransactionUpdateInput = {
        termsAgreementTimestampPayer1: isPartyA ? new Date() : transaction.termsAgreementTimestampPayer1,
        termsAgreementTimestampPayer2: isPartyB ? new Date() : transaction.termsAgreementTimestampPayer2,
        agreedFirstPayer: { connect: { id: designatedFirstPayerId } },
        firstPayerDesignationTimestamp: designationTimestamp,
        status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
        paymentExpectedByPayer1: paymentDueDate,
      };
      
      finalUpdatedTransaction = await prisma.transaction.update({
        where: { id: transactionId },
        data: updateData,
        include: { 
          currencyAProvider: { select: { id: true, username: true, email: true } },
          currencyBProvider: { select: { id: true, username: true, email: true } },
          agreedFirstPayer: { select: { id: true, username: true, email: true } },
        }
      });
      
      const firstPayerUsername = finalUpdatedTransaction.agreedFirstPayer?.username || 
                                finalUpdatedTransaction.agreedFirstPayer?.email || 
                                'The first payer';
      
      systemMessage = `Both parties have agreed to the transaction terms. System has designated ${firstPayerUsername} as the first payer. Payment due by ${paymentDueDate.toLocaleString()}.`;
      
      // Create notifications for the new payment phase
      await this.notificationService.createNotification({
        userId: designatedFirstPayerId,
        type: PrismaNotificationType.TRANSACTION_ACTION_REQUIRED,
        message: `You have been designated as the first payer for transaction ${transaction.id.substring(0,8)}. Please make your payment by ${paymentDueDate.toLocaleString()}.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });

      const otherPartyId = designatedFirstPayerId === transaction.currencyAProviderId ? 
                          transaction.currencyBProviderId : transaction.currencyAProviderId;
      
      await this.notificationService.createNotification({
        userId: otherPartyId,
        type: PrismaNotificationType.TRANSACTION_UPDATE,
        message: `${firstPayerUsername} has been designated as the first payer for transaction ${transaction.id.substring(0,8)}. Waiting for their payment, due by ${paymentDueDate.toLocaleString()}.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
      
    } else {
      // Only one party has agreed so far
      systemMessage += ` Waiting for ${otherPartyUsername} to agree.`;
      
      finalUpdatedTransaction = await prisma.transaction.update({
        where: { id: transactionId },
        data: updatedData,
        include: { 
          currencyAProvider: { select: { id: true, username: true, email: true } },
          currencyBProvider: { select: { id: true, username: true, email: true } },
        }
      });
      
      // Create notification for the other party to agree
      await this.notificationService.createNotification({
        userId: otherPartyId,
        type: PrismaNotificationType.TRANSACTION_UPDATE,
        message: `${currentUserUsername} has agreed to terms for transaction ${transaction.id.substring(0,8)}. Waiting for your agreement.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
       await this.notificationService.createNotification({
        userId: userId,
        type: PrismaNotificationType.TRANSACTION_UPDATE,
        message: `You have agreed to terms for transaction ${transaction.id.substring(0,8)}. Waiting for ${otherPartyUsername}.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
    }

    // Auto-designate first payer if both parties have agreed to the terms
    if (finalUpdatedTransaction.termsAgreementTimestampPayer1 && finalUpdatedTransaction.termsAgreementTimestampPayer2) {
      const firstPayerId = await this.determineFirstPayer(finalUpdatedTransaction);
      await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          agreedFirstPayerId: firstPayerId,
          firstPayerDesignationTimestamp: new Date(),
          status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
          paymentExpectedByPayer1: this.calculatePaymentDueDate(new Date(), DEFAULT_PAYMENT_WINDOW_HOURS),
        },
      });
      console.log(`[TransactionService] Auto-designated first payer: ${firstPayerId}`);
    }

    console.log(`[TransactionService] User ${userId} successfully agreed to terms for transaction ${transactionId}.`);
    return finalUpdatedTransaction;
  }

  // Helper method to determine who should pay first based on business logic
  private async determineFirstPayer(transaction: Transaction): Promise<string> {
    console.log(`[TransactionService] Determining first payer for transaction ${transaction.id}`);
    
    // Fetch user details including reputation
    const [userA, userB] = await Promise.all([
      prisma.user.findUnique({ 
        where: { id: transaction.currencyAProviderId },
        select: { id: true, reputationLevel: true, reputationScore: true }
      }),
      prisma.user.findUnique({ 
        where: { id: transaction.currencyBProviderId },
        select: { id: true, reputationLevel: true, reputationScore: true }
      })
    ]);

    if (!userA || !userB) {
      throw new Error('Unable to fetch user details for first payer determination');
    }

    // Business logic for determining first payer:
    // 1. Lower reputation user pays first (builds trust)
    if (userA.reputationLevel !== userB.reputationLevel) {
      const firstPayerId = userA.reputationLevel < userB.reputationLevel ? userA.id : userB.id;
      console.log(`[TransactionService] First payer determined by reputation: ${firstPayerId}`);
      return firstPayerId;
    }

    // 2. If reputation levels are equal, use reputation score
    if (userA.reputationScore !== userB.reputationScore) {
      const firstPayerId = userA.reputationScore < userB.reputationScore ? userA.id : userB.id;
      console.log(`[TransactionService] First payer determined by reputation score: ${firstPayerId}`);
      return firstPayerId;
    }

    // 3. If reputation is equal, IRR payer goes first (if applicable)
    if (transaction.currencyA === 'IRR' && transaction.currencyB !== 'IRR') {
      console.log(`[TransactionService] First payer determined by currency (IRR): ${userA.id}`);
      return userA.id; // currencyA provider (IRR) pays first
    }
    if (transaction.currencyB === 'IRR' && transaction.currencyA !== 'IRR') {
      console.log(`[TransactionService] First payer determined by currency (IRR): ${userB.id}`);
      return userB.id; // currencyB provider (IRR) pays first
    }

    // 4. Default: currencyA provider pays first (arbitrary but consistent)
    console.log(`[TransactionService] First payer determined by default: ${userA.id}`);
    return userA.id;
  }

  async designateFirstPayer(transactionId: string, userId: string, designatedPayerId: string): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} designating ${designatedPayerId} as first payer for transaction ${transactionId}`);
    const transaction = await prisma.transaction.findUnique({ where: { id: transactionId } });

    if (!transaction) {
      throw new Error('Transaction not found.');
    }
    if (userId !== transaction.currencyAProviderId && userId !== transaction.currencyBProviderId) {
      throw new Error('User is not a party to this transaction.');
    }
    if (designatedPayerId !== transaction.currencyAProviderId && designatedPayerId !== transaction.currencyBProviderId) {
      throw new Error('Designated payer is not a party to this transaction.');
    }
    // Expecting AWAITING_FIRST_PAYER_DESIGNATION status
    if (transaction.status !== TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION) {
      throw new Error(`Transaction is not awaiting first payer designation. Current status: ${transaction.status}`);
    }
    // Removed check for termsAgreementTimestampPayer1 and termsAgreementTimestampPayer2
    if (transaction.agreedFirstPayerId) {
      throw new Error('First payer has already been designated.');
    }

    const paymentDueDate = this.calculatePaymentDueDate(new Date(), DEFAULT_PAYMENT_WINDOW_HOURS);
    const designationTimestamp = new Date();    const updatedTransaction = await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        agreedFirstPayerId: designatedPayerId,
        firstPayerDesignationTimestamp: designationTimestamp, // Set timestamp
        status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
        paymentExpectedByPayer1: paymentDueDate,
        // paymentExpectedByPayer2 will be set after first payment is confirmed
        // Set termsAgreementTimestamps if they are to signify this step
        // For now, assuming they are separate or handled by client acknowledgement if needed
        // If this designation implies agreement from both, set them here:
        // termsAgreementTimestampPayer1: designationTimestamp, 
        // termsAgreementTimestampPayer2: designationTimestamp,
      },
      include: {
        currencyAProvider: { select: { username: true } },
        currencyBProvider: { select: { username: true } },
      },
    });

    await this.emitTransactionStatusUpdate(updatedTransaction);    const firstPayerUser = await prisma.user.findUnique({ where: { id: designatedPayerId } });
    const firstPayerUsername = firstPayerUser?.username || 'The first payer';
    const otherPartyId = designatedPayerId === transaction.currencyAProviderId ? transaction.currencyBProviderId : transaction.currencyAProviderId;

    await this.createAndEmitSystemMessage(
      transaction.chatSessionId,
      `Both parties have agreed. ${firstPayerUsername} will make the first payment. Payment due by ${paymentDueDate.toLocaleString()}.`,
      transaction.id
    );

    await this.notificationService.createNotification({
      userId: designatedPayerId,
      type: PrismaNotificationType.TRANSACTION_ACTION_REQUIRED,
      message: `You are the agreed first payer for transaction ${transaction.id.substring(0,8)}. Please make your payment by ${paymentDueDate.toLocaleString()}.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });

    await this.notificationService.createNotification({
      userId: otherPartyId,
      type: PrismaNotificationType.TRANSACTION_UPDATE,
      message: `Both parties agreed. ${firstPayerUsername} will make the first payment for transaction ${transaction.id.substring(0,8)}, due by ${paymentDueDate.toLocaleString()}.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });

    return updatedTransaction;
  }

  async declarePayment(
    transactionId: string,
    userId: string,
    paymentTrackingNumber?: string
  ): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} declaring payment for transaction ${transactionId}`);
    const transaction = await prisma.transaction.findUnique({ where: { id: transactionId } });

    if (!transaction) {
      throw new Error('Transaction not found.');
    }
    if (!transaction.agreedFirstPayerId) {
      throw new Error('First payer has not been designated yet.');
    }

    let updatedData: Prisma.TransactionUpdateInput = {};
    
    let notificationRecipientId: string | undefined = undefined;
    let notificationMessage: string = '';
    let additionalNotificationRecipientId: string | undefined = undefined;
    let additionalNotificationMessage: string = '';
    let notificationMessageForOtherParty: string = ''; // Renamed for clarity
    let systemMessage: string = '';

    const payer1Id = transaction.agreedFirstPayerId;
    const payer2Id = payer1Id === transaction.currencyAProviderId ? transaction.currencyBProviderId : transaction.currencyAProviderId;
    const payer1User = await prisma.user.findUnique({ where: { id: payer1Id }});
    const payer2User = await prisma.user.findUnique({ where: { id: payer2Id }});
    const payer1Username = payer1User?.username || 'Payer 1';
    const payer2Username = payer2User?.username || 'Payer 2';
    let notificationMessageForSelf = ''; // For the user declaring payment

    if (userId === payer1Id && transaction.status === TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT) {
      updatedData = {
        paymentDeclaredAtPayer1: new Date(),
        paymentTrackingNumberPayer1: paymentTrackingNumber,
        status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION, // Fixed: waiting for second party to confirm first payment
      };
      systemMessage = `${payer1Username} has declared their payment. Waiting for ${payer2Username} to confirm receipt.`;
      notificationRecipientId = payer2Id; // Other party needs to confirm
      notificationMessageForOtherParty = `${payer1Username} has declared payment for transaction ${transaction.id.substring(0,8)}. Please confirm receipt.`;
      notificationMessageForSelf = `You have declared payment for transaction ${transaction.id.substring(0,8)}. Waiting for ${payer2Username} to confirm.`;
    } else if (userId === payer2Id && transaction.status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT) {
      updatedData = {
        paymentDeclaredAtPayer2: new Date(),
        paymentTrackingNumberPayer2: paymentTrackingNumber,
        status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION, // Fixed: waiting for first party to confirm second payment
      };
      systemMessage = `${payer2Username} has declared their payment. Waiting for ${payer1Username} to confirm receipt.`;
      notificationRecipientId = payer1Id; // Other party needs to confirm
      notificationMessage = `${payer2Username} has declared payment for transaction ${transaction.id.substring(0,8)}. Please confirm receipt.`;
      additionalNotificationRecipientId = payer2Id; // Self notification
      additionalNotificationMessage = `You have declared payment for transaction ${transaction.id.substring(0,8)}. Waiting for ${payer1Username} to confirm.`;
    } else {
      throw new Error('It is not your turn to declare payment or the transaction is in an invalid state.');
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id: transactionId },
      data: updatedData,
      include: {
        currencyAProvider: { select: { username: true } },
        currencyBProvider: { select: { username: true } },
      },
    });

    await this.emitTransactionStatusUpdate(updatedTransaction);
    await this.createAndEmitSystemMessage(transaction.chatSessionId, systemMessage, transaction.id);

    // Notify the other party (recipient of "action required")
    if (notificationRecipientId && notificationMessage) {
      await this.notificationService.createNotification({
        userId: notificationRecipientId,
        type: PrismaNotificationType.TRANSACTION_ACTION_REQUIRED,
        message: notificationMessage,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
    }

    // Notify additional recipient (usually the current user for status update)
    if (additionalNotificationRecipientId && additionalNotificationMessage) {
      await this.notificationService.createNotification({
        userId: additionalNotificationRecipientId,
        type: PrismaNotificationType.TRANSACTION_UPDATE,
        message: additionalNotificationMessage,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
    }

    return updatedTransaction;
  }

  async confirmReceipt(transactionId: string, userId: string): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} confirming receipt for transaction ${transactionId}`);
    const transaction = await prisma.transaction.findUnique({ where: { id: transactionId } });

    if (!transaction) {
      throw new Error('Transaction not found.');
    }
    if (!transaction.agreedFirstPayerId) {
      throw new Error('First payer has not been designated yet.');
    }

    let updatedData: Prisma.TransactionUpdateInput = {};
    
    let notificationRecipientId: string | undefined = undefined;
    let notificationMessage: string = '';
    let systemMessage: string = '';
    let additionalNotificationRecipientId: string | undefined = undefined;
    let additionalNotificationMessage: string = '';
    
    const payer1Id = transaction.agreedFirstPayerId;
    const payer2Id = payer1Id === transaction.currencyAProviderId ? transaction.currencyBProviderId : transaction.currencyAProviderId;
    const payer1User = await prisma.user.findUnique({ where: { id: payer1Id }});
    const payer2User = await prisma.user.findUnique({ where: { id: payer2Id }});
    const payer1Username = payer1User?.username || 'Payer 1';
    const payer2Username = payer2User?.username || 'Payer 2';    if (userId === payer2Id && transaction.status === TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION) {
      const paymentDueDateForPayer2 = this.calculatePaymentDueDate(new Date(), DEFAULT_PAYMENT_WINDOW_HOURS);
      updatedData = {
        firstPaymentConfirmedByPayer2At: new Date(),
        status: TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT,
        paymentExpectedByPayer2: paymentDueDateForPayer2,
      };
      systemMessage = `${payer2Username} has confirmed receipt of the first payment. Now waiting for ${payer2Username} to make their payment to ${payer1Username}. Payment due by ${paymentDueDateForPayer2.toLocaleString()}.`;
      notificationRecipientId = payer2Id; // Payer 2 (current user) gets "action required" for their next payment
      notificationMessage = `First payment confirmed for TX ${transaction.id.substring(0,8)}. Please make your payment to ${payer1Username} by ${paymentDueDateForPayer2.toLocaleString()}.`;
      additionalNotificationRecipientId = payer1Id; // Payer 1 (other party) gets an update
      additionalNotificationMessage = `${payer2Username} confirmed your payment for TX ${transaction.id.substring(0,8)}. Now awaiting their payment.`;
    } else if (userId === payer1Id && transaction.status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION) {
      updatedData = {
        secondPaymentConfirmedByPayer1At: new Date(),
        status: TransactionStatus.COMPLETED,
      };
      systemMessage = `${payer1Username} has confirmed receipt of the second payment. Transaction is now complete!`;
      notificationRecipientId = payer1Id; // Payer 1 (current user) gets "completed"
      notificationMessage = `Transaction ${transaction.id.substring(0,8)} is complete! You confirmed ${payer2Username}'s payment.`;
      additionalNotificationRecipientId = payer2Id; // CORRECTED: Payer 2 (other party) gets "completed"
      additionalNotificationMessage = `Transaction ${transaction.id.substring(0,8)} is complete! ${payer1Username} confirmed your payment.`;
    } else {
      throw new Error('It is not your turn to confirm receipt or the transaction is in an invalid state.');
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id: transactionId },
      data: updatedData,
    });

    await this.emitTransactionStatusUpdate(updatedTransaction);
    await this.createAndEmitSystemMessage(transaction.chatSessionId, systemMessage, transaction.id);

    if (notificationRecipientId) {
      await this.notificationService.createNotification({
        userId: notificationRecipientId,
        type: updatedData.status === TransactionStatus.COMPLETED ? PrismaNotificationType.TRANSACTION_COMPLETED : PrismaNotificationType.TRANSACTION_ACTION_REQUIRED,
        message: notificationMessage,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: transaction.id,
      });
    }
    if (additionalNotificationRecipientId) {
        await this.notificationService.createNotification({
            userId: additionalNotificationRecipientId,
            type: updatedData.status === TransactionStatus.COMPLETED ? PrismaNotificationType.TRANSACTION_COMPLETED : PrismaNotificationType.TRANSACTION_PAYMENT_CONFIRMED, // This type might need adjustment based on context
            message: additionalNotificationMessage,
            relatedEntityType: 'TRANSACTION',
            relatedEntityId: transaction.id,
        });
    }
    return updatedTransaction;
  }

  async cancelTransaction(transactionId: string, userId: string, reason: string): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} attempting to cancel transaction ${transactionId} for reason: ${reason}`);
    const transaction = await prisma.transaction.findUnique({ where: { id: transactionId } });

    if (!transaction) {
      throw new Error('Transaction not found.');
    }
    if (userId !== transaction.currencyAProviderId && userId !== transaction.currencyBProviderId) {
      throw new Error('User is not a party to this transaction.');
    }
    const nonCancellableStatuses: TransactionStatus[] = [TransactionStatus.COMPLETED, TransactionStatus.CANCELLED, TransactionStatus.DISPUTED];
    if (nonCancellableStatuses.includes(transaction.status)) {
      throw new Error(`Transaction cannot be cancelled as it is already ${transaction.status}.`);
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status: TransactionStatus.CANCELLED,
        cancellationReason: reason,
        cancelledByUserId: userId, // Add this
        updatedAt: new Date(),
      },
    });

    await this.emitTransactionStatusUpdate(updatedTransaction);

    const cancellingUser = await prisma.user.findUnique({ where: { id: userId } });
    const cancellingUsername = cancellingUser?.username || 'A user';
    const otherPartyId = userId === transaction.currencyAProviderId ? transaction.currencyBProviderId : transaction.currencyAProviderId;

    await this.createAndEmitSystemMessage(
      transaction.chatSessionId,
      `Transaction has been cancelled by ${cancellingUsername}. Reason: ${reason}`,
      transaction.id
    );

    await this.notificationService.createNotification({
      userId: userId,
      type: PrismaNotificationType.TRANSACTION_UPDATE, // Consider specific type e.g. TRANSACTION_CANCELLED_BY_YOU
      message: `You have cancelled transaction ${transaction.id.substring(0,8)}. Reason: ${reason}`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });

    await this.notificationService.createNotification({
      userId: otherPartyId,
      type: PrismaNotificationType.TRANSACTION_UPDATE, // Consider specific type e.g. TRANSACTION_CANCELLED_BY_OTHER
      message: `Transaction ${transaction.id.substring(0,8)} has been cancelled by ${cancellingUsername}. Reason: ${reason}`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });
    
    console.log(`[TransactionService] Transaction ${transactionId} cancelled by user ${userId}.`);
    return updatedTransaction;
  }

  async disputeTransaction(transactionId: string, userId: string, reason: string): Promise<Transaction> {
    console.log(`[TransactionService] User ${userId} attempting to dispute transaction ${transactionId} for reason: ${reason}`);
    const transaction = await prisma.transaction.findUnique({ where: { id: transactionId } });

    if (!transaction) {
      throw new Error('Transaction not found.');
    }
    if (userId !== transaction.currencyAProviderId && userId !== transaction.currencyBProviderId) {
      throw new Error('User is a party to this transaction.');
    }
    const nonDisputableStatuses: TransactionStatus[] = [TransactionStatus.COMPLETED, TransactionStatus.CANCELLED, TransactionStatus.DISPUTED];
    if (nonDisputableStatuses.includes(transaction.status)) {
      throw new Error(`Transaction cannot be disputed as it is already ${transaction.status}.`);
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status: TransactionStatus.DISPUTED,
        disputeReason: reason,
        disputedByUserId: userId, // Add this
        updatedAt: new Date(),
      },
    });

    await this.emitTransactionStatusUpdate(updatedTransaction);

    const disputingUser = await prisma.user.findUnique({ where: { id: userId } });
    const disputingUsername = disputingUser?.username || 'A user';
    const otherPartyId = userId === transaction.currencyAProviderId ? transaction.currencyBProviderId : transaction.currencyAProviderId;

    await this.createAndEmitSystemMessage(
      transaction.chatSessionId,
      `Transaction has been disputed by ${disputingUsername}. Reason: ${reason}. Please resolve this issue directly or contact support.`,
      transaction.id
    );

    // Using TRANSACTION_ACTION_REQUIRED as a generic notification type for disputes
    await this.notificationService.createNotification({
      userId: userId,
      type: PrismaNotificationType.TRANSACTION_ACTION_REQUIRED, 
      message: `You have disputed transaction ${transaction.id.substring(0,8)}. Reason: ${reason}. Await resolution.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });

    await this.notificationService.createNotification({
      userId: otherPartyId,
      type: PrismaNotificationType.TRANSACTION_ACTION_REQUIRED, 
      message: `Transaction ${transaction.id.substring(0,8)} has been disputed by ${disputingUsername}. Reason: ${reason}. Please engage to resolve.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: transaction.id,
    });
    
    console.log(`[TransactionService] Transaction ${transactionId} disputed by user ${userId}.`);
    return updatedTransaction;
  }
}
