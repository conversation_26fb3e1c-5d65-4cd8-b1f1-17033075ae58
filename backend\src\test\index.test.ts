import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Hono } from 'hono';

// --- Mock Dependencies ---

// Mock @hono/node-server
const mockServe = vi.fn(() => ({ // Mock the return object if needed
    close: vi.fn(),
}));
vi.mock('@hono/node-server', () => ({
    serve: mockServe,
}));

// Mock hono middleware
const mockLogger = vi.fn((..._args: any[]) => (_c: any, next: () => void) => next()); // Mock middleware signature
const mockCors = vi.fn((..._args: any[]) => (_c: any, next: () => void) => next());
vi.mock('hono/logger', () => ({ logger: mockLogger }));
vi.mock('hono/cors', () => ({ cors: mockCors }));

// Mock socket.io Server
const mockSocketOn = vi.fn();
const mockIoServer = vi.fn(() => ({
    on: mockSocketOn,
}));
vi.mock('socket.io', () => ({
    Server: mockIoServer,
}));

// Mock Prisma Client (consistent with other tests)
vi.mock('@prisma/client', () => {
    const mockPrisma = {
        // Add mock methods if index.ts directly uses prisma, currently it doesn't seem to
    };
    return { PrismaClient: vi.fn(() => mockPrisma) };
});

// Mock email service
const mockInitializeEmail = vi.fn().mockResolvedValue(undefined);
vi.mock('../services/email', () => ({
    initializeEmailTransporter: mockInitializeEmail,
}));

// Mock auth routes (provide a simple Hono app as the mock)
const mockAuthApp = new Hono().get('/test', (c) => c.text('auth mock'));
vi.mock('../routes/auth', () => ({
    default: mockAuthApp,
}));

// --- Test Setup ---

// Store original env vars
const originalPort = process.env.PORT;
const originalFrontendUrl = process.env.FRONTEND_URL;

describe('Backend Index Setup (src/index.ts)', () => {
    // let app: Hono; // To hold the Hono instance created in index.ts

    beforeEach(async () => {
        // Reset modules to ensure fresh state and env var reading
        vi.resetModules();

        // Reset mocks
        vi.clearAllMocks();

        // Set default environment variables for tests
        process.env.PORT = '3001'; // Use a different port for testing
        process.env.FRONTEND_URL = 'http://test-frontend.com';

        // Dynamically import the index file to execute its setup logic
        // This will create the Hono app instance and call initializers
        await import('../index');
        // Assuming index.ts doesn't export the app directly, we might need to test effects
        // Or modify index.ts slightly to export app for testing if needed.
        // For now, we test the mocks were called correctly during the import.

        // Re-assign mocks if they were reset by vi.resetModules() if needed
        // (Not strictly necessary here as we check calls made during the initial import)
    });

    afterEach(() => {
        // Restore original env vars
        process.env.PORT = originalPort;
        process.env.FRONTEND_URL = originalFrontendUrl;
    });

    it('should initialize email transporter on startup', () => {
        expect(mockInitializeEmail).toHaveBeenCalledTimes(1);
    });

    it('should apply logger middleware', () => {
        // Hono applies middleware during instantiation or via app.use()
        // We check if the mock middleware function was called by Hono
        expect(mockLogger).toHaveBeenCalled();
    });

    it('should apply CORS middleware', () => {
        expect(mockCors).toHaveBeenCalled();
        // Optionally check CORS options if they are complex and passed to the mock
        // expect(mockCors).toHaveBeenCalledWith(/* expected options */);
    });

    // Note: Testing routes mounted requires access to the Hono app instance.
    // If index.ts doesn't export `app`, we can't easily test routes directly here.
    // We *can* check that app.route was called if we mock Hono itself, but that's complex.
    // A common pattern is to export the app for testing:
    // // In index.ts: export const app = new Hono(); (if not already done)
    // Then in test: import { app } from '../index';
    // Assuming app is NOT exported, we focus on side effects and mock calls.

    it('should call serve with correct port and fetch handler', () => {
        expect(mockServe).toHaveBeenCalledTimes(1);
        expect(mockServe).toHaveBeenCalledWith(expect.objectContaining({
            port: 3001, // From process.env.PORT set in beforeEach
            fetch: expect.any(Function), // Check that Hono's fetch is passed
        }));
    });

    it('should instantiate Socket.IO Server with correct options', () => {
        expect(mockIoServer).toHaveBeenCalledTimes(1);
        expect(mockIoServer).toHaveBeenCalledWith(expect.anything(), { // First arg is the http server instance from serve()
            cors: {
                origin: 'http://test-frontend.com', // From process.env.FRONTEND_URL
                methods: ['GET', 'POST'],
            },
        });
    });

    it('should set up Socket.IO connection listener', () => {
        expect(mockSocketOn).toHaveBeenCalledTimes(1);
        expect(mockSocketOn).toHaveBeenCalledWith('connection', expect.any(Function));
    });

    // Test the health check endpoint requires the app instance
    // describe('GET / endpoint', () => {
    //   it('should return status ok', async () => {
    //     // This requires app to be exported from index.ts
    //     // const { app } = await import('../index'); // Assuming app is exported
    //     // const res = await app.request('/');
    //     // expect(res.status).toBe(200);
    //     // const body = await res.json();
    //     // expect(body.status).toBe('ok');
    //     // expect(body.message).toBe('MUNygo API is running');
    //   });
    // });

    // Test mounting auth routes requires the app instance
    // describe('Auth routes mounting', () => {
    //   it('should mount auth routes under /api/auth', async () => {
    //     // This requires app to be exported and potentially mocking app.route
    //     // Or making a request to a known auth route
    //     // const { app } = await import('../index'); // Assuming app is exported
    //     // const res = await app.request('/api/auth/test'); // Assuming mockAuthApp is mounted
    //     // expect(res.status).toBe(200);
    //     // expect(await res.text()).toBe('auth mock');
    //   });
    // });
});
