import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createP<PERSON>, setActivePinia } from 'pinia';

// Mock dependencies using vi.mock factory functions
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn().mockResolvedValue({
      data: { success: true, message: 'Report sent successfully', reportId: 'test-123' }
    })
  }
}));

vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    getSocket: vi.fn(() => ({
      id: 'socket-test-123',
      connected: true
    }))
  }
}));

vi.mock('@/stores/connection', () => ({
  useConnectionStore: vi.fn(() => ({
    isConnected: true,
    connectionQuality: 'excellent',
    connectionStatus: 'Connected - Real-time updates',
    transportType: 'websocket',
    reconnectAttempts: 0,
    isReconnecting: false,
    lastDisconnectReason: null
  }))
}));

vi.mock('@/stores/index', () => ({
  default: {
    state: {
      value: {
        auth: {
          user: { id: 'user-123', email: '<EMAIL>' },
          token: 'mock-token',
          isAuthenticated: true
        },
        connection: {
          isConnected: true,
          connectionQuality: 'excellent',
          connectionStatus: 'Connected - Real-time updates',
          transportType: 'websocket',
          reconnectAttempts: 0,
          isReconnecting: false,
          lastDisconnectReason: null
        },
        theme: {
          isDark: true,
          currentTheme: 'dark'
        }
      }
    }
  }
}));

import { useClientLogger } from '../useClientLogger';
import type { DiagnosticData } from '@/types/logging';

describe('useClientLogger - Diagnostic Data Enhancement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('captureDiagnosticData', () => {
    it('should capture connection status correctly', () => {
      const logger = useClientLogger();
      const diagnosticData: DiagnosticData = logger.captureDiagnosticData();

      expect(diagnosticData.connectionStatus).toEqual({
        isConnected: true,
        connectionQuality: 'excellent',
        connectionStatus: 'Connected - Real-time updates',
        transportType: 'websocket',
        reconnectAttempts: 0,
        isReconnecting: false,
        lastDisconnectReason: null,
        socketId: 'socket-test-123',
        socketConnected: true
      });
    });

    it('should capture Pinia store snapshot safely', () => {
      const logger = useClientLogger();
      const diagnosticData: DiagnosticData = logger.captureDiagnosticData();

      expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('auth');
      expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('connection');
      expect(diagnosticData.piniaStoreSnapshot).toHaveProperty('theme');
      
      // Verify auth store data is captured
      expect(diagnosticData.piniaStoreSnapshot.auth).toEqual({
        user: { id: 'user-123', email: '<EMAIL>' },
        token: 'mock-token',
        isAuthenticated: true
      });
    });

    it('should include capture timestamp', () => {
      const logger = useClientLogger();
      const beforeCapture = new Date().toISOString();
      const diagnosticData: DiagnosticData = logger.captureDiagnosticData();
      const afterCapture = new Date().toISOString();

      expect(diagnosticData.captureTimestamp).toBeDefined();
      expect(diagnosticData.captureTimestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(diagnosticData.captureTimestamp >= beforeCapture).toBe(true);
      expect(diagnosticData.captureTimestamp <= afterCapture).toBe(true);
    });

    it('should handle missing socket gracefully', async () => {
      // Import the mocked module and modify its behavior
      const centralizedSocketManager = await import('@/services/centralizedSocketManager');
      vi.mocked(centralizedSocketManager.default.getSocket).mockReturnValue(null);

      const logger = useClientLogger();
      const diagnosticData: DiagnosticData = logger.captureDiagnosticData();

      expect(diagnosticData.connectionStatus.socketId).toBeUndefined();
      expect(diagnosticData.connectionStatus.socketConnected).toBeUndefined();
    });
  });

  describe('sendLogsToServer with diagnostic data', () => {
    it('should include diagnostic data in report payload', async () => {
      const apiClient = await import('@/services/apiClient');
      const logger = useClientLogger();

      const reportDetails = {
        type: 'bug' as const,
        severity: 'medium' as const,
        title: 'Test Bug Report',
        description: 'This is a test bug report with diagnostic data'
      };

      await logger.sendLogsToServer(reportDetails);

      expect(vi.mocked(apiClient.default.post)).toHaveBeenCalledWith(
        '/debug/report-issue',
        expect.objectContaining({
          diagnosticData: expect.objectContaining({
            connectionStatus: expect.any(Object),
            piniaStoreSnapshot: expect.any(Object),
            captureTimestamp: expect.any(String)
          })
        })
      );
    });

    it('should capture diagnostic data at submission time', async () => {
      // Reset the socket mock to return the expected socket
      const centralizedSocketManager = await import('@/services/centralizedSocketManager');
      vi.mocked(centralizedSocketManager.default.getSocket).mockReturnValue({
        id: 'socket-test-123',
        connected: true
      });

      const apiClient = await import('@/services/apiClient');
      const logger = useClientLogger();

      const reportDetails = {
        type: 'bug' as const,
        severity: 'high' as const,
        title: 'Test Report',
        description: 'Test description'
      };

      await logger.sendLogsToServer(reportDetails);

      // Verify the API was called with diagnostic data
      expect(vi.mocked(apiClient.default.post)).toHaveBeenCalledTimes(1);
      const callArgs = vi.mocked(apiClient.default.post).mock.calls[0];
      const payload = callArgs[1];

      expect(payload.diagnosticData).toBeDefined();
      expect(payload.diagnosticData.connectionStatus).toEqual({
        isConnected: true,
        connectionQuality: 'excellent',
        connectionStatus: 'Connected - Real-time updates',
        transportType: 'websocket',
        reconnectAttempts: 0,
        isReconnecting: false,
        lastDisconnectReason: null,
        socketId: 'socket-test-123',
        socketConnected: true
      });
      expect(payload.diagnosticData.piniaStoreSnapshot).toHaveProperty('auth');
      expect(payload.diagnosticData.piniaStoreSnapshot).toHaveProperty('connection');
      expect(payload.diagnosticData.piniaStoreSnapshot).toHaveProperty('theme');
    });
  });
});
