Write-Host "🚀 Setting up MUNygo dual environment..." -ForegroundColor Yellow
Write-Host ""

# Check current environment
Write-Host "Checking current environment..." -ForegroundColor Cyan
if (Test-Path "prisma/dev.db") {
    Write-Host "✅ SQLite database found: prisma/dev.db" -ForegroundColor Green
} else {
    Write-Host "⚠️  No SQLite database found" -ForegroundColor Yellow
}

if (Test-Path ".env") {
    $currentEnv = Get-Content ".env" | Select-String "DATABASE_URL"
    if ($currentEnv -match "file:") {
        Write-Host "✅ Currently configured for SQLite" -ForegroundColor Green
    } elseif ($currentEnv -match "postgresql:") {
        Write-Host "✅ Currently configured for PostgreSQL" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Available commands:" -ForegroundColor White
Write-Host ""
Write-Host "🔄 Environment Switching:" -ForegroundColor Cyan
Write-Host "  npm run env:sqlite       - Switch to SQLite" -ForegroundColor Gray
Write-Host "  npm run env:postgres     - Switch to PostgreSQL" -ForegroundColor Gray
Write-Host ""
Write-Host "🚀 Development:" -ForegroundColor Cyan
Write-Host "  npm run dev:sqlite       - Start with SQLite" -ForegroundColor Gray
Write-Host "  npm run dev:postgres     - Start with PostgreSQL" -ForegroundColor Gray
Write-Host ""
Write-Host "🗄️  Database Management:" -ForegroundColor Cyan
Write-Host "  npm run migrate:sqlite   - Run migrations on SQLite" -ForegroundColor Gray
Write-Host "  npm run migrate:postgres - Run migrations on PostgreSQL" -ForegroundColor Gray
Write-Host "  npm run reset:sqlite     - Reset SQLite database" -ForegroundColor Gray
Write-Host "  npm run reset:postgres   - Reset PostgreSQL database" -ForegroundColor Gray
Write-Host ""
Write-Host "🔍 Database Studio:" -ForegroundColor Cyan
Write-Host "  npm run studio:sqlite    - Open Prisma Studio for SQLite" -ForegroundColor Gray
Write-Host "  npm run studio:postgres  - Open Prisma Studio for PostgreSQL" -ForegroundColor Gray
Write-Host ""
Write-Host "🐳 Docker (PostgreSQL):" -ForegroundColor Cyan
Write-Host "  docker-compose up -d     - Start PostgreSQL container" -ForegroundColor Gray
Write-Host "  docker-compose down      - Stop containers" -ForegroundColor Gray
Write-Host ""

Write-Host "Quick start:" -ForegroundColor Green
Write-Host "1. For SQLite (local):     npm run dev:sqlite" -ForegroundColor White
Write-Host "2. For PostgreSQL (Docker): npm run dev:postgres" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Would you like to start with SQLite (s) or PostgreSQL (p)? [s/p]"

if ($choice -eq "s" -or $choice -eq "sqlite") {
     Write-Host "Starting with SQLite..." -ForegroundColor Yellow
    try {
        npm run dev:sqlite
    } catch {
        Write-Host "❌ Failed to start SQLite environment. Check if npm dependencies are installed." -ForegroundColor Red
    }
 } elseif ($choice -eq "p" -or $choice -eq "postgres") {
     Write-Host "Starting with PostgreSQL..." -ForegroundColor Yellow
     Write-Host "Make sure Docker is running first!" -ForegroundColor Yellow
    try {
        npm run dev:postgres
    } catch {
        Write-Host "❌ Failed to start PostgreSQL environment. Check if Docker is running and npm dependencies are installed." -ForegroundColor Red
    }
} else {
    Write-Host "No selection made. Use the commands above when ready!" -ForegroundColor Cyan
}
