{"reportId":"report_1748856318594_f3uc7rn6b","timestamp":"2025-06-02T09:25:18.594Z","serverReceivedAt":"2025-06-02T09:25:18.594Z","clientTimestamp":"2025-06-02T09:25:18.563Z","sessionId":"session_1748856298353_m2oz7b52g","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"test","logCount":8,"logs":[{"timestamp":"2025-06-02T09:24:58.374Z","level":"INFO","message":"MUNygo application starting","context":{"userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T09:24:58.374Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.383Z","level":"INFO","message":"Socket connection initializing for authenticated user","context":{"userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.395Z","level":"INFO","message":"MUNygo application mounted successfully","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:58.585Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.574Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.690Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:24:59.802Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T09:25:18.563Z","level":"INFO","message":"User initiated debug report send","context":{"logCount":7,"hasUserNotes":true,"currentPage":"http://localhost:5173/home"},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748858892146_68dyb2d2v","timestamp":"2025-06-02T10:08:12.146Z","serverReceivedAt":"2025-06-02T10:08:12.146Z","clientTimestamp":"2025-06-02T10:08:12.003Z","sessionId":"session_1748858812570_b5i7cxuw1","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","logCount":14,"logs":[{"timestamp":"2025-06-02T10:06:52.597Z","level":"INFO","message":"MUNygo application starting","context":{"userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T10:06:52.597Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.604Z","level":"INFO","message":"Socket connection initializing for authenticated user","context":{"userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.616Z","level":"INFO","message":"MUNygo application mounted successfully","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.813Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:52.841Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.639Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-02T10:06:53.639Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.640Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:53.810Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:06:59.590Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:46.739Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:47.658Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ویژگی جدید"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:07:48.418Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهینه‌سازی"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T10:08:12.002Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748864532649_b7z12r0fo","timestamp":"2025-06-02T11:42:12.649Z","serverReceivedAt":"2025-06-02T11:42:12.649Z","clientTimestamp":"2025-06-02T11:42:12.584Z","sessionId":"session_1748864474570_zeszl7q20","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"dsfdsf","logCount":14,"logs":[{"timestamp":"2025-06-02T11:41:14.591Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T11:41:14.591Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.596Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.606Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.785Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:14.808Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.464Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-02T11:41:15.464Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.465Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.528Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:15.609Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":19,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:39.134Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:56.547Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کمک"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:57.072Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:41:57.685Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T11:42:12.583Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748868758189_qer5mlz54","timestamp":"2025-06-02T12:52:38.190Z","serverReceivedAt":"2025-06-02T12:52:38.190Z","clientTimestamp":"2025-06-02T12:52:38.125Z","sessionId":"session_1748868460401_bxqgaqnh1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.101.0-insider Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36","currentUrl":"http://localhost:5173/home","userNotes":"","logCount":45,"logs":[{"timestamp":"2025-06-02T12:47:40.428Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.101.0-insider Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36","url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","timestamp":"2025-06-02T12:47:40.428Z"},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.438Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.587Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.594Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.595Z","level":"WARN","message":"Route access denied - authentication required","context":{"route":"/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872","isAuthenticated":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.597Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/home?id=e7e9d0be-b14c-452d-9ce8-28e931102643&vscodeBrowserReqId=1748868459872"},{"timestamp":"2025-06-02T12:47:40.731Z","level":"INFO","message":"User action: navigation","context":{"to":"/","from":"/","timestamp":"2025-06-02T12:47:40.731Z"},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:40.731Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/","routeName":"landing"},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:42.357Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/"},{"timestamp":"2025-06-02T12:47:42.390Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/","timestamp":"2025-06-02T12:47:42.390Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:42.391Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.000Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T12:47:48.067Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-02T12:47:48.067Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.067Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.168Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.169Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:48.248Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:57.026Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/create-offer","routeName":"CreateOffer","requiresAuth":true,"requiresPhoneVerified":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:47:57.274Z","level":"INFO","message":"User action: navigation","context":{"to":"/create-offer","from":"/home","timestamp":"2025-06-02T12:47:57.274Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:47:57.274Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/create-offer","routeName":"CreateOffer"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.357Z","level":"INFO","message":"Creating new offer","context":{"amount":123},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.427Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.434Z","level":"INFO","message":"Offer created successfully","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.469Z","level":"INFO","message":"Route navigation started","context":{"from":"/create-offer","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.478Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-02T12:48:07.616Z","level":"INFO","message":"User action: navigation","context":{"to":"/my-offers","from":"/create-offer","timestamp":"2025-06-02T12:48:07.616Z"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:48:07.616Z","level":"INFO","message":"Route navigation completed","context":{"from":"/create-offer","to":"/my-offers","routeName":"MyOffers"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:49:10.191Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers","to":"/chat/cmbf35wmm0005vl9kokya0ozg","routeName":"ChatSession","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T12:49:10.663Z","level":"INFO","message":"User action: navigation","context":{"to":"/chat/cmbf35wmm0005vl9kokya0ozg","from":"/my-offers","timestamp":"2025-06-02T12:49:10.663Z"},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:49:10.663Z","level":"INFO","message":"Route navigation completed","context":{"from":"/my-offers","to":"/chat/cmbf35wmm0005vl9kokya0ozg","routeName":"ChatSession"},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:32.627Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.731Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.731Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.732Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbf35wmm0005vl9kokya0ozg"},{"timestamp":"2025-06-02T12:50:33.734Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/chat/cmbf35wmm0005vl9kokya0ozg","timestamp":"2025-06-02T12:50:33.734Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.734Z","level":"INFO","message":"Route navigation completed","context":{"from":"/chat/cmbf35wmm0005vl9kokya0ozg","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.796Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:33.858Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":0,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:35.348Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:50:54.349Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:51:02.807Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"نیاز به رفع"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:51:03.549Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T12:52:38.124Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748877414046_3wmvp1anq","timestamp":"2025-06-02T15:16:54.047Z","serverReceivedAt":"2025-06-02T15:16:54.047Z","clientTimestamp":"2025-06-02T15:16:53.981Z","sessionId":"session_1748877269763_2a1ig8wm8","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"test extra","reportType":"bug","reportSeverity":"high","reportTitle":"test title","reportDescription":"test description","stepsToReproduce":"test steps to reproduce","expectedBehavior":"test expected","actualBehavior":"test real","tags":["خطا","ایده","سرعت"],"hasTags":true,"logCount":12,"logs":[{"timestamp":"2025-06-02T15:14:29.813Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T15:14:29.813Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.826Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.845Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.255Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.307Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:31.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.098Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:34.757Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:45.823Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:46.635Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:47.505Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"high","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748878105697_hkfskybt8","timestamp":"2025-06-02T15:28:25.697Z","serverReceivedAt":"2025-06-02T15:28:25.697Z","clientTimestamp":"2025-06-02T15:28:25.631Z","sessionId":"session_1748877269763_2a1ig8wm8","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/my-offers","userNotes":"اسا اضافی","reportType":"bug","reportSeverity":"medium","reportTitle":"تست عنوان","reportDescription":"تست توضیح","stepsToReproduce":"تست مراحل","expectedBehavior":"تست رفتار مورد انتظار","actualBehavior":"تست رفتار واقعی","tags":["خطا","ایده","سرعت","رابط کاربری","پیشنهاد"],"hasTags":true,"logCount":43,"logs":[{"timestamp":"2025-06-02T15:14:29.813Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-02T15:14:29.813Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.826Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:29.845Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.255Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:30.307Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:31.709Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.098Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:34.757Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:45.823Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:46.635Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:47.505Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"high","hasTags":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:53.981Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":12,"correlatedLogCount":12,"userActionCount":6,"reportType":"bug","reportSeverity":"high","hasStepsToReproduce":true,"tags":["خطا","ایده","سرعت"]},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:16:54.060Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748877414046_3wmvp1anq","reportType":"bug"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:22:17.030Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:22:17.606Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:17.679Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/browse-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:22:17.755Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:18.750Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:29.690Z","level":"INFO","message":"Fetching browsable offer by ID","context":{"offerId":"cmbe8iyp40001vlaomppvj1wc"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:29.712Z","level":"INFO","message":"Browsable offer fetched successfully","context":{"offerId":"cmbe8iyp40001vlaomppvj1wc"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:32.357Z","level":"INFO","message":"Route navigation started","context":{"from":"/browse-offers","to":"/chat/cmbeazxls000evlyctlocn2wx","routeName":"ChatSession","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-02T15:22:33.107Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:22:34.972Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:22:55.690Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:07.978Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:08.923Z","level":"INFO","message":"Route navigation started","context":{"from":"/chat/cmbeazxls000evlyctlocn2wx","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/chat/cmbeazxls000evlyctlocn2wx"},{"timestamp":"2025-06-02T15:23:09.270Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:10.842Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers","to":"/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","routeName":"EditOffer","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:23:11.009Z","level":"INFO","message":"Fetching offer by ID","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:11.016Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:11.091Z","level":"INFO","message":"Offer fetched successfully","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.745Z","level":"INFO","message":"Updating offer","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0","payload":{"type":"SELL","amount":100,"baseRate":100,"adjustmentForLowerRep":0,"adjustmentForHigherRep":2.25}},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.783Z","level":"INFO","message":"Offer updated successfully","context":{"offerId":"cmbdevmrt0001vlx0akhgl0q0"},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.794Z","level":"INFO","message":"Route navigation started","context":{"from":"/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/my-offers/cmbdevmrt0001vlx0akhgl0q0/edit"},{"timestamp":"2025-06-02T15:23:15.901Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/my-offers","stackTrace":"ReferenceError: logger is not defined\n    at http://localhost:5173/src/router/index.ts?t=1748877267433:162:3\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:64\n    at Object.runWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-J4DVLWB4.js?v=784dc2c3:6083:18)\n    at runWithContext (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2599:66)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:43\n    at Array.forEach (<anonymous>)\n    at triggerAfterEach (http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2661:24)\n    at http://localhost:5173/node_modules/.vite/deps/vue-router.js?v=784dc2c3:2589:7"},{"timestamp":"2025-06-02T15:23:17.178Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:22.728Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:23.882Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:24.607Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:25.452Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"رابط کاربری"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:26:26.369Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"پیشنهاد"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-02T15:28:25.631Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/my-offers"}]}
{"reportId":"report_1748878263051_lsi74rgj2","timestamp":"2025-06-02T15:31:03.051Z","serverReceivedAt":"2025-06-02T15:31:03.051Z","clientTimestamp":"2025-06-02T15:31:02.961Z","sessionId":"session_1748877269830_i65biscz9","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":null,"reportType":"bug","reportSeverity":"medium","reportTitle":"ظظظ","reportDescription":"zzz","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["خطا","ایده","سرعت","رابط کاربری","پیشنهاد","مستندات"],"hasTags":true,"logCount":19,"logs":[{"timestamp":"2025-06-02T15:14:29.881Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********","url":"http://localhost:5173/login","timestamp":"2025-06-02T15:14:29.880Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:29.894Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:29.915Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.305Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.350Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.352Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"login","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:30.355Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-02T15:14:31.500Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:14:32.126Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:23:15.782Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:23:15.876Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:30.606Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:35.092Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"خطا"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:35.736Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ایده"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:36.432Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"سرعت"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:37.015Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"رابط کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:37.843Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"پیشنهاد"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:30:38.853Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"مستندات"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-02T15:31:02.960Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/home"}]}
{"reportId":"report_1748951567945_ssatgr3jr","timestamp":"2025-06-03T11:52:47.945Z","serverReceivedAt":"2025-06-03T11:52:47.945Z","clientTimestamp":"2025-06-03T11:52:47.890Z","sessionId":"session_1748951516751_h1c4chi3e","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"حوسو اضافی","reportType":"bug","reportSeverity":"medium","reportTitle":"تست حوسو","reportDescription":"توضیح حوسو","stepsToReproduce":"مراحل حوسو","expectedBehavior":"رفتار مورد انتظار حوسو","actualBehavior":"رفتار واقعی حوسو","tags":["فوری","کند"],"hasTags":true,"logCount":10,"logs":[{"timestamp":"2025-06-03T11:51:56.759Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T11:51:56.759Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.761Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.764Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.825Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.852Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:11.504Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:12.657Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کند"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:47.889Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportDetails":{"type":"bug","severity":"medium","title":"تست حوسو","description":"توضیح حوسو","stepsToReproduce":"مراحل حوسو","expectedBehavior":"رفتار مورد انتظار حوسو","actualBehavior":"رفتار واقعی حوسو","additionalNotes":"حوسو اضافی","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1872,"height":991},"timestamp":"2025-06-03T11:52:47.890Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T11:51:56.825Z"},{"action":"navigation","timestamp":"2025-06-03T11:51:57.367Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T11:52:11.504Z","details":{"tag":"فوری"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T11:52:12.657Z","details":{"tag":"کند"}},{"action":"debug-report-submit","timestamp":"2025-06-03T11:52:47.889Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T11:51:56.759Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T11:51:56.759Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.761Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.764Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.825Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:56.852Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T11:51:57.367Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:51:57.367Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:11.504Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:12.657Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"کند"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T11:52:47.889Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["فوری","کند"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"VcvymD1koc7_QQghAAAR","socketConnected":true},"piniaStoreSnapshot":{},"captureTimestamp":"2025-06-03T11:52:47.890Z"}}
{"reportId":"report_1748952316266_89q2v0isf","timestamp":"2025-06-03T12:05:16.266Z","serverReceivedAt":"2025-06-03T12:05:16.266Z","clientTimestamp":"2025-06-03T12:05:16.216Z","sessionId":"session_1748952286667_aebupwgwg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"تست 1","reportType":"bug","reportSeverity":"critical","reportTitle":"تست 1","reportDescription":"تست 1","stepsToReproduce":"تست 1","expectedBehavior":"تست 1","actualBehavior":"تست 1","tags":["بهبود","طراحی"],"hasTags":true,"logCount":10,"logs":[{"timestamp":"2025-06-03T12:04:46.682Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:04:46.681Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.684Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.687Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.786Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.812Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:55.191Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:56.112Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:05:16.215Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"critical","title":"تست 1","description":"تست 1","stepsToReproduce":"تست 1","expectedBehavior":"تست 1","actualBehavior":"تست 1","additionalNotes":"تست 1","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1872,"height":991},"timestamp":"2025-06-03T12:05:16.216Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:04:46.786Z"},{"action":"navigation","timestamp":"2025-06-03T12:04:46.938Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:04:55.191Z","details":{"tag":"بهبود"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:04:56.112Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:05:16.215Z","details":{"reportType":"bug","reportSeverity":"critical","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:04:46.682Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:04:46.681Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.684Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.687Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.786Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.812Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:04:46.938Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:46.938Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:55.191Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:04:56.112Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:05:16.215Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["بهبود","طراحی"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"r6u2Ny8NSEG_iZWLAAAP","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952286881},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":8,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:05:16.216Z"}}
{"reportId":"report_1748953380614_66y3zstrj","timestamp":"2025-06-03T12:23:00.614Z","serverReceivedAt":"2025-06-03T12:23:00.615Z","clientTimestamp":"2025-06-03T12:23:00.232Z","sessionId":"session_1748952956568_k3i12xb48","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"","reportType":"bug","reportSeverity":"medium","reportTitle":"xzvhpd","reportDescription":"طراحی","stepsToReproduce":"مراحل طراحی","expectedBehavior":"","actualBehavior":"","tags":["طراحی"],"hasTags":true,"logCount":9,"logs":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"medium","title":"xzvhpd","description":"طراحی","stepsToReproduce":"مراحل طراحی","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1241,"height":991},"timestamp":"2025-06-03T12:23:00.232Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:15:56.723Z"},{"action":"navigation","timestamp":"2025-06-03T12:15:57.176Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:22:31.146Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:00.231Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["طراحی"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"W8rvT0uSnfgNqaNDAAAR","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952957026},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":9,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:23:00.232Z"}}
{"reportId":"report_1748953425757_zvd1sxvj2","timestamp":"2025-06-03T12:23:45.757Z","serverReceivedAt":"2025-06-03T12:23:45.757Z","clientTimestamp":"2025-06-03T12:23:45.347Z","sessionId":"session_1748952956568_k3i12xb48","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/admin/debug-dashboard","userNotes":"","reportType":"bug","reportSeverity":"low","reportTitle":"بهبود","reportDescription":"سیبیس","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["بهبود","روش بهتر"],"hasTags":true,"logCount":14,"logs":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.232Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":9,"correlatedLogCount":9,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":true,"tags":["طراحی"],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":7},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.626Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748953380614_66y3zstrj","reportType":"bug"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:24.264Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:25.906Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"روش بهتر"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:45.345Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"low","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"low","title":"بهبود","description":"سیبیس","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/admin/debug-dashboard","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1241,"height":991},"timestamp":"2025-06-03T12:23:45.347Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:15:56.723Z"},{"action":"navigation","timestamp":"2025-06-03T12:15:57.176Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:22:31.146Z","details":{"tag":"طراحی"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:00.231Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:23:24.264Z","details":{"tag":"بهبود"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T12:23:25.906Z","details":{"tag":"روش بهتر"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:23:45.345Z","details":{"reportType":"bug","reportSeverity":"low","hasTags":true}}],"routeHistory":["/admin/debug-dashboard"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:15:56.582Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:15:56.582Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.588Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.598Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.723Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:56.758Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:15:57.176Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:15:57.176Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:22:31.147Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"طراحی"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.231Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.232Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":9,"correlatedLogCount":9,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":true,"tags":["طراحی"],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":7},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:00.626Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"report_1748953380614_66y3zstrj","reportType":"bug"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:24.264Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"بهبود"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:25.906Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"روش بهتر"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:23:45.345Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"low","hasTags":true},"url":"http://localhost:5173/admin/debug-dashboard"}],"reportTags":["بهبود","روش بهتر"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"W8rvT0uSnfgNqaNDAAAR","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.c3czyRCItnN1Yp8esQaIl3m1kznkSc6GzZyY6TnxZrs","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748952957026},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":10,"totalPages":1,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:23:45.347Z"}}
{"reportId":"report_1748954394950_5x6f6am51","timestamp":"2025-06-03T12:39:54.950Z","serverReceivedAt":"2025-06-03T12:39:54.950Z","clientTimestamp":"2025-06-03T12:39:54.782Z","sessionId":"session_1748954235767_p7a5tsohq","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/my-offers","userNotes":"","reportType":"bug","reportSeverity":"critical","reportTitle":"در حالت قطع","reportDescription":"الان ک دارم اینو میفرستم قطع هستم","stepsToReproduce":"توکن از کار افتاد\nمجبور شدم لاگین کنم\nبعد میزنه هنوز وصل نیست\n","expectedBehavior":"باید وصل باشه","actualBehavior":"","tags":[],"hasTags":false,"logCount":43,"logs":[{"timestamp":"2025-06-03T12:37:15.784Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:37:15.784Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.790Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.797Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.959Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.015Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.420Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.425Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.427Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.640Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:37:16.640Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.642Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.654Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.654Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.654Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.655Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.655Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.655Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.657Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.657Z"},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.657Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.724Z","level":"INFO","message":"Route navigation started","context":{"from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.727Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","timestamp":"2025-06-03T12:37:16.727Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:16.728Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.354Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.355Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.356Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.582Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-03T12:37:18.582Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.582Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.708Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.710Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.826Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:30.798Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/home","stackTrace":"Error: Socket connection timeout\n    at http://localhost:5173/src/services/centralizedSocketManager.ts:262:20"},{"timestamp":"2025-06-03T12:37:30.799Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/home","stackTrace":"Error: Socket connection timeout\n    at http://localhost:5173/src/services/centralizedSocketManager.ts:262:20"},{"timestamp":"2025-06-03T12:37:51.906Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:52.085Z","level":"INFO","message":"User action: navigation","context":{"to":"/browse-offers","from":"/home","timestamp":"2025-06-03T12:37:52.085Z"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.085Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.253Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.297Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:53.816Z","level":"INFO","message":"Fetching browsable offer by ID","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:53.842Z","level":"INFO","message":"Browsable offer fetched successfully","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:38:25.818Z","level":"INFO","message":"Route navigation started","context":{"from":"/browse-offers","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:38:25.820Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/browse-offers","timestamp":"2025-06-03T12:38:25.820Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:25.820Z","level":"INFO","message":"Route navigation completed","context":{"from":"/browse-offers","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:29.412Z","level":"INFO","message":"Route navigation started","context":{"from":"/admin/debug-dashboard","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:29.499Z","level":"INFO","message":"User action: navigation","context":{"to":"/my-offers","from":"/admin/debug-dashboard","timestamp":"2025-06-03T12:38:29.499Z"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-03T12:38:29.500Z","level":"INFO","message":"Route navigation completed","context":{"from":"/admin/debug-dashboard","to":"/my-offers","routeName":"MyOffers"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-03T12:39:54.780Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":false},"url":"http://localhost:5173/my-offers"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"critical","title":"در حالت قطع","description":"الان ک دارم اینو میفرستم قطع هستم","stepsToReproduce":"توکن از کار افتاد\nمجبور شدم لاگین کنم\nبعد میزنه هنوز وصل نیست\n","expectedBehavior":"باید وصل باشه","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/my-offers","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1241,"height":991},"timestamp":"2025-06-03T12:39:54.782Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:37:15.959Z"},{"action":"navigation","timestamp":"2025-06-03T12:37:16.640Z","details":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:37:16.640Z"}},{"action":"navigation","timestamp":"2025-06-03T12:37:16.654Z","details":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.654Z"}},{"action":"navigation","timestamp":"2025-06-03T12:37:16.655Z","details":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.655Z"}},{"action":"navigation","timestamp":"2025-06-03T12:37:16.657Z","details":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.657Z"}},{"action":"navigation","timestamp":"2025-06-03T12:37:16.727Z","details":{"to":"/login","from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","timestamp":"2025-06-03T12:37:16.727Z"}},{"action":"navigation","timestamp":"2025-06-03T12:37:18.582Z","details":{"to":"/home","from":"/login","timestamp":"2025-06-03T12:37:18.582Z"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T12:37:18.708Z"},{"action":"navigation","timestamp":"2025-06-03T12:37:52.085Z","details":{"to":"/browse-offers","from":"/home","timestamp":"2025-06-03T12:37:52.085Z"}},{"action":"navigation","timestamp":"2025-06-03T12:38:25.820Z","details":{"to":"/admin/debug-dashboard","from":"/browse-offers","timestamp":"2025-06-03T12:38:25.820Z"}},{"action":"navigation","timestamp":"2025-06-03T12:38:29.499Z","details":{"to":"/my-offers","from":"/admin/debug-dashboard","timestamp":"2025-06-03T12:38:29.499Z"}},{"action":"debug-report-submit","timestamp":"2025-06-03T12:39:54.780Z","details":{"reportType":"bug","reportSeverity":"critical","hasTags":false}}],"routeHistory":["/admin/debug-dashboard","/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","/login","/home","/browse-offers","/admin/debug-dashboard","/my-offers"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T12:37:15.784Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/admin/debug-dashboard","timestamp":"2025-06-03T12:37:15.784Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.790Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.797Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:15.959Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.015Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.420Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.425Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.427Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.640Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/","timestamp":"2025-06-03T12:37:16.640Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.642Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.654Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.654Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.654Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.655Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.655Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.655Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:37:16.657Z","level":"INFO","message":"User action: navigation","context":{"to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","from":"/","timestamp":"2025-06-03T12:37:16.657Z"},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.657Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","routeName":"login"},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.724Z","level":"INFO","message":"Route navigation started","context":{"from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF."},{"timestamp":"2025-06-03T12:37:16.727Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","timestamp":"2025-06-03T12:37:16.727Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:16.728Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login?message=%D8%AC%D9%84%D8%B3%D9%87+%D9%85%D9%86%D9%82%D8%B6%DB%8C+%D8%B4%D8%AF%D9%87.+%D9%84%D8%B7%D9%81%D8%A7%D9%8B+%D8%AF%D9%88%D8%A8%D8%A7%D8%B1%D9%87+%D9%88%D8%A7%D8%B1%D8%AF+%D8%B4%D9%88%DB%8C%D8%AF.","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.354Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.355Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.356Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T12:37:18.582Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-03T12:37:18.582Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.582Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.708Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.710Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:18.826Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:30.798Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/home","stackTrace":"Error: Socket connection timeout\n    at http://localhost:5173/src/services/centralizedSocketManager.ts:262:20"},{"timestamp":"2025-06-03T12:37:30.799Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/home","stackTrace":"Error: Socket connection timeout\n    at http://localhost:5173/src/services/centralizedSocketManager.ts:262:20"},{"timestamp":"2025-06-03T12:37:51.906Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T12:37:52.085Z","level":"INFO","message":"User action: navigation","context":{"to":"/browse-offers","from":"/home","timestamp":"2025-06-03T12:37:52.085Z"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.085Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/browse-offers","routeName":"BrowseOffers"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.253Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:52.297Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:53.816Z","level":"INFO","message":"Fetching browsable offer by ID","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:37:53.842Z","level":"INFO","message":"Browsable offer fetched successfully","context":{"offerId":"cmbf3559f0001vl9kvbulrk6h"},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:38:25.818Z","level":"INFO","message":"Route navigation started","context":{"from":"/browse-offers","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/browse-offers"},{"timestamp":"2025-06-03T12:38:25.820Z","level":"INFO","message":"User action: navigation","context":{"to":"/admin/debug-dashboard","from":"/browse-offers","timestamp":"2025-06-03T12:38:25.820Z"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:25.820Z","level":"INFO","message":"Route navigation completed","context":{"from":"/browse-offers","to":"/admin/debug-dashboard","routeName":"AdminDebugDashboard"},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:29.412Z","level":"INFO","message":"Route navigation started","context":{"from":"/admin/debug-dashboard","to":"/my-offers","routeName":"MyOffers","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/admin/debug-dashboard"},{"timestamp":"2025-06-03T12:38:29.499Z","level":"INFO","message":"User action: navigation","context":{"to":"/my-offers","from":"/admin/debug-dashboard","timestamp":"2025-06-03T12:38:29.499Z"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-03T12:38:29.500Z","level":"INFO","message":"Route navigation completed","context":{"from":"/admin/debug-dashboard","to":"/my-offers","routeName":"MyOffers"},"url":"http://localhost:5173/my-offers"},{"timestamp":"2025-06-03T12:39:54.780Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"critical","hasTags":false},"url":"http://localhost:5173/my-offers"}],"reportTags":[]},"diagnosticData":{"connectionStatus":{"isConnected":false,"connectionQuality":"disconnected","connectionStatus":"Disconnected - Attempting to reconnect","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.iFfFbQFez9nag_eR_Cq7oSBoNGCqB0ZBuzoJiUP9IUU","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[],"isLoading":false,"error":"Request failed with status code 401","lastFetchedTimestamp":null},"connection":{"isConnected":false,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"offerStore":{"offers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"}],"isLoading":false,"error":null,"currentlyViewedOfferDetails":null},"interestStore":{"interestRequests":[],"loading":{}},"adminDebug":{"reports":[{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","userId":"[Max depth reached]","userEmail":"[Max depth reached]","username":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]","reportDetails":"[Max depth reached]","diagnosticData":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","reportType":"[Max depth reached]","reportSeverity":"[Max depth reached]","reportTitle":"[Max depth reached]","reportDescription":"[Max depth reached]","stepsToReproduce":"[Max depth reached]","expectedBehavior":"[Max depth reached]","actualBehavior":"[Max depth reached]","tags":"[Max depth reached]","hasTags":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","userNotes":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"},{"reportId":"[Max depth reached]","timestamp":"[Max depth reached]","serverReceivedAt":"[Max depth reached]","clientTimestamp":"[Max depth reached]","sessionId":"[Max depth reached]","userAgent":"[Max depth reached]","currentUrl":"[Max depth reached]","logCount":"[Max depth reached]","logs":"[Max depth reached]"}],"total":11,"totalPages":2,"currentPage":1,"limit":10,"loading":false,"error":null,"selectedReport":null,"selectedReportLoading":false,"filters":{},"sort":{"sortBy":"serverReceivedAt","sortOrder":"desc"}}},"captureTimestamp":"2025-06-03T12:39:54.781Z"}}
{"reportId":"report_1748958110014_1dr62ba8c","timestamp":"2025-06-03T13:41:50.014Z","serverReceivedAt":"2025-06-03T13:41:50.014Z","clientTimestamp":"2025-06-03T13:41:49.786Z","sessionId":"session_1748957680487_js2s0dtqe","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/home","userNotes":"","reportType":"bug","reportSeverity":"medium","reportTitle":"تست بک اند قطع","reportDescription":"aaaa","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["فوری"],"hasTags":true,"logCount":80,"logs":[{"timestamp":"2025-06-03T13:34:40.503Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-03T13:34:40.503Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.509Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.518Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.667Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.725Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.233Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-03T13:34:41.233Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.234Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.296Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.375Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:52.131Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"نیاز به رفع"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:53.411Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"تجربه کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:03.138Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:08.980Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":null,"lastCleanup":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:08.994Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:21.525Z","level":"INFO","message":"User action: debug-form-reset","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:26.999Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":null,"lastCleanup":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:27.021Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:30.368Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:30.370Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:35.014Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"تجربه کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:35.674Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ویژگی جدید"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:43.329Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:47.272Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:47.285Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:49.052Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:49.052Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:04.362Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:04.363Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:07.503Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:04.362Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:07.513Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:08.982Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:08.983Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:29.307Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:08.982Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:29.316Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:49.030Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:49.031Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:51.153Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:49.030Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:51.171Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:01.332Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:01.332Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:10.341Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:10.342Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:13.440Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:13.627Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/profile","routeName":"profile","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:14.028Z","level":"INFO","message":"User action: navigation","context":{"to":"/profile","from":"/home","timestamp":"2025-06-03T13:37:14.028Z"},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:14.028Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/profile","routeName":"profile"},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:17.558Z","level":"INFO","message":"Route navigation started","context":{"from":"/profile","to":"/create-offer","routeName":"CreateOffer","requiresAuth":true,"requiresPhoneVerified":true},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:17.613Z","level":"INFO","message":"User action: navigation","context":{"to":"/create-offer","from":"/profile","timestamp":"2025-06-03T13:37:17.613Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:17.613Z","level":"INFO","message":"Route navigation completed","context":{"from":"/profile","to":"/create-offer","routeName":"CreateOffer"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:30.811Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:47.643Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:37:10.341Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:53.583Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:59.360Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:03.163Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:03.164Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:09.901Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:09.902Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:39.841Z","level":"INFO","message":"Route navigation started","context":{"from":"/create-offer","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:40.051Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/create-offer","timestamp":"2025-06-03T13:38:40.051Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:40.051Z","level":"INFO","message":"Route navigation completed","context":{"from":"/create-offer","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.071Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.072Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.073Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.076Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-03T13:38:42.076Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.077Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.194Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.196Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.316Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:47.065Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:47.066Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:54.791Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:54.792Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:06.755Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:06.799Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:18.987Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:26.864Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:48.870Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:40:06.754Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:48.871Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:54.340Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:41:49.785Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":true},"url":"http://localhost:5173/home"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"medium","title":"تست بک اند قطع","description":"aaaa","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/home","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1001,"height":991},"timestamp":"2025-06-03T13:41:49.786Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:34:40.667Z"},{"action":"navigation","timestamp":"2025-06-03T13:34:41.233Z","details":{"to":"/home","from":"/","timestamp":"2025-06-03T13:34:41.233Z"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T13:34:52.131Z","details":{"tag":"نیاز به رفع"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T13:34:53.411Z","details":{"tag":"تجربه کاربری"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:35:08.994Z"},{"action":"debug-form-reset","timestamp":"2025-06-03T13:35:21.525Z"},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:35:27.021Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:35:30.370Z"},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T13:35:35.014Z","details":{"tag":"تجربه کاربری"}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T13:35:35.674Z","details":{"tag":"ویژگی جدید"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:35:47.285Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:35:49.052Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:36:04.363Z"},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:36:07.513Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:36:08.983Z"},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:36:29.316Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:36:49.031Z"},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:36:51.170Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:37:01.332Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:37:10.342Z"},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:37:13.440Z"},{"action":"navigation","timestamp":"2025-06-03T13:37:14.028Z","details":{"to":"/profile","from":"/home","timestamp":"2025-06-03T13:37:14.028Z"}},{"action":"navigation","timestamp":"2025-06-03T13:37:17.613Z","details":{"to":"/create-offer","from":"/profile","timestamp":"2025-06-03T13:37:17.613Z"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:37:30.811Z"},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T13:37:59.360Z","details":{"tag":"فوری"}},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:38:03.164Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:38:09.902Z"},{"action":"navigation","timestamp":"2025-06-03T13:38:40.051Z","details":{"to":"/login","from":"/create-offer","timestamp":"2025-06-03T13:38:40.051Z"}},{"action":"navigation","timestamp":"2025-06-03T13:38:42.076Z","details":{"to":"/home","from":"/login","timestamp":"2025-06-03T13:38:42.076Z"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T13:38:42.194Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:38:47.066Z"},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:38:54.792Z"},{"action":"debug-report-submit","timestamp":"2025-06-03T13:40:06.799Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false}},{"action":"debug-report-submit","timestamp":"2025-06-03T13:40:18.987Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false}},{"action":"debug-report-submit","timestamp":"2025-06-03T13:40:26.863Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false}},{"action":"debug-draft-restored","timestamp":"2025-06-03T13:40:48.871Z"},{"action":"debug-report-submit","timestamp":"2025-06-03T13:40:54.340Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false}},{"action":"debug-report-submit","timestamp":"2025-06-03T13:41:49.785Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":true}}],"routeHistory":["/home","/profile","/create-offer","/login","/home"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T13:34:40.503Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/home","timestamp":"2025-06-03T13:34:40.503Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.509Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.518Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.667Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:40.725Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.233Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/","timestamp":"2025-06-03T13:34:41.233Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.234Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.296Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:41.375Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:52.131Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"نیاز به رفع"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:34:53.411Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"تجربه کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:03.138Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:08.980Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":null,"lastCleanup":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:08.994Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:21.525Z","level":"INFO","message":"User action: debug-form-reset","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:26.999Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":null,"lastCleanup":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:27.021Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:30.368Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:03.138Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:30.370Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:35.014Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"تجربه کاربری"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:35.674Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"ویژگی جدید"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:43.329Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:47.272Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:47.285Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:49.052Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:35:49.052Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:04.362Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:04.363Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:07.503Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:04.362Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:07.513Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:08.982Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:08.983Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:29.307Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:08.982Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:29.316Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:49.030Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:49.031Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:51.153Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:36:49.030Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:36:51.171Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:01.332Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:01.332Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:10.341Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:35:43.329Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:10.342Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:13.440Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:13.627Z","level":"INFO","message":"Route navigation started","context":{"from":"/home","to":"/profile","routeName":"profile","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:37:14.028Z","level":"INFO","message":"User action: navigation","context":{"to":"/profile","from":"/home","timestamp":"2025-06-03T13:37:14.028Z"},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:14.028Z","level":"INFO","message":"Route navigation completed","context":{"from":"/home","to":"/profile","routeName":"profile"},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:17.558Z","level":"INFO","message":"Route navigation started","context":{"from":"/profile","to":"/create-offer","routeName":"CreateOffer","requiresAuth":true,"requiresPhoneVerified":true},"url":"http://localhost:5173/profile"},{"timestamp":"2025-06-03T13:37:17.613Z","level":"INFO","message":"User action: navigation","context":{"to":"/create-offer","from":"/profile","timestamp":"2025-06-03T13:37:17.613Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:17.613Z","level":"INFO","message":"Route navigation completed","context":{"from":"/profile","to":"/create-offer","routeName":"CreateOffer"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:30.811Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:47.643Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748957703138_zirix5rd6","lastCleanup":"2025-06-03T13:37:10.341Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:53.583Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:37:59.360Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"فوری"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:03.163Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:03.164Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:09.901Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:09.902Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:39.841Z","level":"INFO","message":"Route navigation started","context":{"from":"/create-offer","to":"/login","routeName":"login","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/create-offer"},{"timestamp":"2025-06-03T13:38:40.051Z","level":"INFO","message":"User action: navigation","context":{"to":"/login","from":"/create-offer","timestamp":"2025-06-03T13:38:40.051Z"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:40.051Z","level":"INFO","message":"Route navigation completed","context":{"from":"/create-offer","to":"/login","routeName":"login"},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.071Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/","routeName":"landing","requiresAuth":false,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.072Z","level":"INFO","message":"Redirecting authenticated user from auth page to home","context":{"attemptedRoute":"landing","isAuthenticated":true},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.073Z","level":"INFO","message":"Route navigation started","context":{"from":"/login","to":"/home","routeName":"home","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/login"},{"timestamp":"2025-06-03T13:38:42.076Z","level":"INFO","message":"User action: navigation","context":{"to":"/home","from":"/login","timestamp":"2025-06-03T13:38:42.076Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.077Z","level":"INFO","message":"Route navigation completed","context":{"from":"/login","to":"/home","routeName":"home"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.194Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.196Z","level":"INFO","message":"Fetching browse offers","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:42.316Z","level":"INFO","message":"Browse offers fetched successfully","context":{"count":20,"hasTargetOffer":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:47.065Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:47.066Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:54.791Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:37:53.583Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:38:54.792Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:06.755Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748957703138_zirix5rd6","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:06.799Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:18.987Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:26.864Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:48.870Z","level":"INFO","message":"Form draft loaded","context":{"draftId":"draft_1748957703138_zirix5rd6","timestamp":"2025-06-03T13:35:03.138Z","lastModified":"2025-06-03T13:40:06.754Z"},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:48.871Z","level":"INFO","message":"User action: debug-draft-restored","url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:40:54.340Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/home"},{"timestamp":"2025-06-03T13:41:49.785Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":true},"url":"http://localhost:5173/home"}],"reportTags":["فوری"]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"Qmw6AnFv8rsrsfA3AAAE","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.vBBDwDGL_OHQwfMLa4GNOvtNUsLHOSpObEJFIbpy0bA","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"fa"},"theme":{"isDark":false},"myOffers":{"myOffers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","currencyPair":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","user":"[Max depth reached]","interests":"[Max depth reached]"}],"loading":false,"error":null,"message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"},{"id":"[Max depth reached]","userId":"[Max depth reached]","type":"[Max depth reached]","message":"[Max depth reached]","isRead":"[Max depth reached]","createdAt":"[Max depth reached]","updatedAt":"[Max depth reached]","relatedEntityType":"[Max depth reached]","relatedEntityId":"[Max depth reached]","actorId":"[Max depth reached]","actorUsername":"[Max depth reached]","data":"[Max depth reached]"}],"isLoading":false,"error":null,"lastFetchedTimestamp":1748957681101},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false},"offerStore":{"offers":[{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"},{"id":"[Max depth reached]","type":"[Max depth reached]","amount":"[Max depth reached]","baseRate":"[Max depth reached]","adjustmentForLowerRep":"[Max depth reached]","adjustmentForHigherRep":"[Max depth reached]","status":"[Max depth reached]","createdAt":"[Max depth reached]","offerCreatorId":"[Max depth reached]","offerCreatorUsername":"[Max depth reached]","offerCreatorReputationLevel":"[Max depth reached]","calculatedApplicableRate":"[Max depth reached]","currentUserHasShownInterest":"[Max depth reached]","currentUserInterestStatus":"[Max depth reached]","chatSessionId":"[Max depth reached]","transactionStatus":"[Max depth reached]","negotiationStatus":"[Max depth reached]","currencyPair":"[Max depth reached]"}],"isLoading":false,"error":null,"currentlyViewedOfferDetails":null},"interestStore":{"interestRequests":[],"loading":{}}},"captureTimestamp":"2025-06-03T13:41:49.786Z"}}
{"reportId":"report_1748960439355_3wuprxvg9","timestamp":"2025-06-03T14:20:39.355Z","serverReceivedAt":"2025-06-03T14:20:39.355Z","clientTimestamp":"2025-06-03T14:20:39.253Z","sessionId":"session_1748960364802_cx1jjge87","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/test/offline-reports","userNotes":"","reportType":"bug","reportSeverity":"medium","reportTitle":"test offline","reportDescription":"test offline des","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":[],"hasTags":false,"logCount":22,"logs":[{"timestamp":"2025-06-03T14:19:24.824Z","level":"INFO","message":"Initializing autofill handler","context":{"config":{"enableMutationObserver":true,"enableErrorSuppression":true,"enableFormStabilization":true,"debugMode":true}},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.827Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/test/offline-reports","timestamp":"2025-06-03T14:19:24.827Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.833Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.843Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.986Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.037Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.470Z","level":"INFO","message":"User action: navigation","context":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.471Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.476Z","level":"INFO","message":"Offline reports loaded","context":{"count":0,"lastCleanup":"2025-06-03T14:05:54.013Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.527Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:29.302Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/test/offline-reports","stackTrace":"Error: websocket error\n    at WS.onError (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:495:33)\n    at ws.onerror (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:1010:35)"},{"timestamp":"2025-06-03T14:19:51.828Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748958798151_syjahetp9","lastCleanup":"2025-06-03T14:06:09.837Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:01.699Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748958798151_syjahetp9","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.569Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.571Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":14,"correlatedLogCount":14,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"disconnected","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.617Z","level":"ERROR","message":"Failed to send enhanced debug report to server","context":{"errorType":"REPORT_SEND_FAILED","reportType":"bug","isNetworkError":true,"browserOnline":false},"url":"http://localhost:5173/test/offline-reports","stackTrace":"AxiosError: Network Error\n    at XMLHttpRequest.handleError (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:1591:14)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:2119:41)\n    at async Object.sendLogsToServer (http://localhost:5173/src/composables/useClientLogger.ts?t=1748960158258:274:24)\n    at async submitReport (http://localhost:5173/src/components/DebugReportButtonEnhanced.vue?t=1748960158258:206:26)"},{"timestamp":"2025-06-03T14:20:02.659Z","level":"INFO","message":"Report added to offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","totalOfflineReports":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.661Z","level":"INFO","message":"Form draft deleted","context":{"draftId":"draft_1748958798151_syjahetp9","remainingDrafts":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.243Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.245Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.247Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.252Z","level":"INFO","message":"Processing offline reports queue","context":{"queueSize":1},"url":"http://localhost:5173/test/offline-reports"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"medium","title":"test offline","description":"test offline des","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/test/offline-reports","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1041,"height":991},"timestamp":"2025-06-03T14:20:39.253Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T14:19:24.986Z"},{"action":"navigation","timestamp":"2025-06-03T14:19:25.470Z","details":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T14:19:25.527Z"},{"action":"debug-report-submit","timestamp":"2025-06-03T14:20:02.569Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false}}],"routeHistory":["/test/offline-reports"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T14:19:24.824Z","level":"INFO","message":"Initializing autofill handler","context":{"config":{"enableMutationObserver":true,"enableErrorSuppression":true,"enableFormStabilization":true,"debugMode":true}},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.827Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/test/offline-reports","timestamp":"2025-06-03T14:19:24.827Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.833Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.843Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.986Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.037Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.470Z","level":"INFO","message":"User action: navigation","context":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.471Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.476Z","level":"INFO","message":"Offline reports loaded","context":{"count":0,"lastCleanup":"2025-06-03T14:05:54.013Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.527Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:29.302Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/test/offline-reports","stackTrace":"Error: websocket error\n    at WS.onError (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:495:33)\n    at ws.onerror (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:1010:35)"},{"timestamp":"2025-06-03T14:19:51.828Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748958798151_syjahetp9","lastCleanup":"2025-06-03T14:06:09.837Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:01.699Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748958798151_syjahetp9","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.569Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.571Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":14,"correlatedLogCount":14,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"disconnected","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.617Z","level":"ERROR","message":"Failed to send enhanced debug report to server","context":{"errorType":"REPORT_SEND_FAILED","reportType":"bug","isNetworkError":true,"browserOnline":false},"url":"http://localhost:5173/test/offline-reports","stackTrace":"AxiosError: Network Error\n    at XMLHttpRequest.handleError (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:1591:14)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:2119:41)\n    at async Object.sendLogsToServer (http://localhost:5173/src/composables/useClientLogger.ts?t=1748960158258:274:24)\n    at async submitReport (http://localhost:5173/src/components/DebugReportButtonEnhanced.vue?t=1748960158258:206:26)"},{"timestamp":"2025-06-03T14:20:02.659Z","level":"INFO","message":"Report added to offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","totalOfflineReports":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.661Z","level":"INFO","message":"Form draft deleted","context":{"draftId":"draft_1748958798151_syjahetp9","remainingDrafts":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.243Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.245Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.247Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.252Z","level":"INFO","message":"Processing offline reports queue","context":{"queueSize":1},"url":"http://localhost:5173/test/offline-reports"}],"reportTags":[]},"diagnosticData":{"connectionStatus":{"isConnected":true,"connectionQuality":"excellent","connectionStatus":"Connected - Real-time updates","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":null,"socketId":"0dUDNHjfYYXXGRc0AAAB","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.sqOCNLVYWHC5AmbKj3yUgFsrB8fGYs3olaHC-aTO1n4","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"en"},"theme":{"isDark":false},"myOffers":{"myOffers":[],"loading":false,"error":"Real-time features may be limited. Try refreshing the page.","message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[],"isLoading":false,"error":"Request failed with status code 500","lastFetchedTimestamp":null},"connection":{"isConnected":true,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":null,"isReconnecting":false}},"captureTimestamp":"2025-06-03T14:20:39.252Z"}}
{"reportId":"report_1748960464285_bsvanaqsr","timestamp":"2025-06-03T14:21:04.285Z","serverReceivedAt":"2025-06-03T14:21:04.285Z","clientTimestamp":"2025-06-03T14:21:04.197Z","sessionId":"session_1748960364802_cx1jjge87","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","currentUrl":"http://localhost:5173/test/offline-reports","userNotes":"","reportType":"bug","reportSeverity":"medium","reportTitle":"a new test of offline","reportDescription":"offline test","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","tags":["Idea"],"hasTags":true,"logCount":30,"logs":[{"timestamp":"2025-06-03T14:19:24.824Z","level":"INFO","message":"Initializing autofill handler","context":{"config":{"enableMutationObserver":true,"enableErrorSuppression":true,"enableFormStabilization":true,"debugMode":true}},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.827Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/test/offline-reports","timestamp":"2025-06-03T14:19:24.827Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.833Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.843Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.986Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.037Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.470Z","level":"INFO","message":"User action: navigation","context":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.471Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.476Z","level":"INFO","message":"Offline reports loaded","context":{"count":0,"lastCleanup":"2025-06-03T14:05:54.013Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.527Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:29.302Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/test/offline-reports","stackTrace":"Error: websocket error\n    at WS.onError (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:495:33)\n    at ws.onerror (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:1010:35)"},{"timestamp":"2025-06-03T14:19:51.828Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748958798151_syjahetp9","lastCleanup":"2025-06-03T14:06:09.837Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:01.699Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748958798151_syjahetp9","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.569Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.571Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":14,"correlatedLogCount":14,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"disconnected","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.617Z","level":"ERROR","message":"Failed to send enhanced debug report to server","context":{"errorType":"REPORT_SEND_FAILED","reportType":"bug","isNetworkError":true,"browserOnline":false},"url":"http://localhost:5173/test/offline-reports","stackTrace":"AxiosError: Network Error\n    at XMLHttpRequest.handleError (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:1591:14)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:2119:41)\n    at async Object.sendLogsToServer (http://localhost:5173/src/composables/useClientLogger.ts?t=1748960158258:274:24)\n    at async submitReport (http://localhost:5173/src/components/DebugReportButtonEnhanced.vue?t=1748960158258:206:26)"},{"timestamp":"2025-06-03T14:20:02.659Z","level":"INFO","message":"Report added to offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","totalOfflineReports":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.661Z","level":"INFO","message":"Form draft deleted","context":{"draftId":"draft_1748958798151_syjahetp9","remainingDrafts":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.243Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.245Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.247Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.252Z","level":"INFO","message":"Processing offline reports queue","context":{"queueSize":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.253Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":22,"correlatedLogCount":22,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.370Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"DBG-1748960439287-2ihpdlmqn","reportType":"bug"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.370Z","level":"INFO","message":"Offline report submitted successfully","context":{"reportId":"offline_1748960402659_qy1ronmag","serverReportId":"DBG-1748960439287-2ihpdlmqn"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.371Z","level":"INFO","message":"Report removed from offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","remainingReports":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:40.385Z","level":"INFO","message":"Offline reports processing completed","context":{"successCount":1,"failureCount":0,"remainingReports":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:01.810Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748960461809_2leeiwjlu","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:02.060Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"Idea"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:04.196Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"}],"userId":"cmbapkhbl0001vlrks0ttv80w","userEmail":"<EMAIL>","username":"hosami","reportDetails":{"type":"bug","severity":"medium","title":"a new test of offline","description":"offline test","stepsToReproduce":"","expectedBehavior":"","actualBehavior":"","additionalNotes":"","userContext":{"currentPage":"http://localhost:5173/test/offline-reports","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","viewport":{"width":1041,"height":991},"timestamp":"2025-06-03T14:21:04.196Z","userActions":[{"action":"debug-report-button-mounted","timestamp":"2025-06-03T14:19:24.986Z"},{"action":"navigation","timestamp":"2025-06-03T14:19:25.470Z","details":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"}},{"action":"debug-report-button-mounted","timestamp":"2025-06-03T14:19:25.527Z"},{"action":"debug-report-submit","timestamp":"2025-06-03T14:20:02.569Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false}},{"action":"debug-predefined-tag-added","timestamp":"2025-06-03T14:21:02.060Z","details":{"tag":"Idea"}},{"action":"debug-report-submit","timestamp":"2025-06-03T14:21:04.196Z","details":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false}}],"routeHistory":["/test/offline-reports"]},"correlatedLogEntries":[{"timestamp":"2025-06-03T14:19:24.824Z","level":"INFO","message":"Initializing autofill handler","context":{"config":{"enableMutationObserver":true,"enableErrorSuppression":true,"enableFormStabilization":true,"debugMode":true}},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.827Z","level":"INFO","message":"app","context":{"message":"MUNygo application starting","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","url":"http://localhost:5173/test/offline-reports","timestamp":"2025-06-03T14:19:24.827Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.833Z","level":"INFO","message":"auth","context":{"message":"Socket connection initializing for authenticated user","userId":"cmbapkhbl0001vlrks0ttv80w"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.843Z","level":"INFO","message":"app","context":{"message":"MUNygo application mounted successfully"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:24.986Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.037Z","level":"INFO","message":"Route navigation started","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage","requiresAuth":true,"requiresPhoneVerified":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.470Z","level":"INFO","message":"User action: navigation","context":{"to":"/test/offline-reports","from":"/","timestamp":"2025-06-03T14:19:25.470Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.471Z","level":"INFO","message":"Route navigation completed","context":{"from":"/","to":"/test/offline-reports","routeName":"OfflineTestPage"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.476Z","level":"INFO","message":"Offline reports loaded","context":{"count":0,"lastCleanup":"2025-06-03T14:05:54.013Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:25.527Z","level":"INFO","message":"User action: debug-report-button-mounted","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:19:29.302Z","level":"ERROR","message":"Unhandled promise rejection","context":{"errorType":"UNHANDLED_PROMISE_REJECTION","reasonType":"object"},"url":"http://localhost:5173/test/offline-reports","stackTrace":"Error: websocket error\n    at WS.onError (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:495:33)\n    at ws.onerror (http://localhost:5173/node_modules/.vite/deps/socket__io-client.js?v=1d34028f:1010:35)"},{"timestamp":"2025-06-03T14:19:51.828Z","level":"INFO","message":"Form drafts loaded","context":{"count":1,"activeDraftId":"draft_1748958798151_syjahetp9","lastCleanup":"2025-06-03T14:06:09.837Z"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:01.699Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748958798151_syjahetp9","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.569Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":false,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.571Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":14,"correlatedLogCount":14,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"disconnected","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.617Z","level":"ERROR","message":"Failed to send enhanced debug report to server","context":{"errorType":"REPORT_SEND_FAILED","reportType":"bug","isNetworkError":true,"browserOnline":false},"url":"http://localhost:5173/test/offline-reports","stackTrace":"AxiosError: Network Error\n    at XMLHttpRequest.handleError (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:1591:14)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=1d34028f:2119:41)\n    at async Object.sendLogsToServer (http://localhost:5173/src/composables/useClientLogger.ts?t=1748960158258:274:24)\n    at async submitReport (http://localhost:5173/src/components/DebugReportButtonEnhanced.vue?t=1748960158258:206:26)"},{"timestamp":"2025-06-03T14:20:02.659Z","level":"INFO","message":"Report added to offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","totalOfflineReports":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:02.661Z","level":"INFO","message":"Form draft deleted","context":{"draftId":"draft_1748958798151_syjahetp9","remainingDrafts":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.243Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.245Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:34.247Z","level":"INFO","message":"Connection restored, processing offline reports","url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.252Z","level":"INFO","message":"Processing offline reports queue","context":{"queueSize":1},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.253Z","level":"INFO","message":"Sending enhanced debug report to server","context":{"logCount":22,"correlatedLogCount":22,"userActionCount":4,"reportType":"bug","reportSeverity":"medium","hasStepsToReproduce":false,"tags":[],"hasDiagnosticData":true,"hasUserIdentification":true,"userId":"cmbapkhbl0001vlrks0ttv80w","connectionStatus":"excellent","storeCount":6},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.370Z","level":"INFO","message":"Enhanced debug report sent successfully","context":{"reportId":"DBG-1748960439287-2ihpdlmqn","reportType":"bug"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.370Z","level":"INFO","message":"Offline report submitted successfully","context":{"reportId":"offline_1748960402659_qy1ronmag","serverReportId":"DBG-1748960439287-2ihpdlmqn"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:39.371Z","level":"INFO","message":"Report removed from offline storage","context":{"reportId":"offline_1748960402659_qy1ronmag","remainingReports":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:20:40.385Z","level":"INFO","message":"Offline reports processing completed","context":{"successCount":1,"failureCount":0,"remainingReports":0},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:01.810Z","level":"INFO","message":"Form draft saved","context":{"draftId":"draft_1748960461809_2leeiwjlu","totalDrafts":1,"hasTitle":true,"hasDescription":true},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:02.060Z","level":"INFO","message":"User action: debug-predefined-tag-added","context":{"tag":"Idea"},"url":"http://localhost:5173/test/offline-reports"},{"timestamp":"2025-06-03T14:21:04.196Z","level":"INFO","message":"User action: debug-report-submit","context":{"reportType":"bug","reportSeverity":"medium","hasTags":true,"isOnline":false},"url":"http://localhost:5173/test/offline-reports"}],"reportTags":["Idea"]},"diagnosticData":{"connectionStatus":{"isConnected":false,"connectionQuality":"disconnected","connectionStatus":"Disconnected - Attempting to reconnect","transportType":"websocket","reconnectAttempts":0,"isReconnecting":false,"lastDisconnectReason":"simulated_offline","socketId":"0dUDNHjfYYXXGRc0AAAB","socketConnected":true},"piniaStoreSnapshot":{"auth":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.sqOCNLVYWHC5AmbKj3yUgFsrB8fGYs3olaHC-aTO1n4","user":{"id":"cmbapkhbl0001vlrks0ttv80w","email":"<EMAIL>","createdAt":"2025-05-30T11:17:03.537Z","updatedAt":"2025-05-30T14:41:23.026Z","emailVerified":true,"phoneNumber":"+121231","phoneVerified":true,"otpSecret":null,"otpTimestamp":null,"username":"hosami","reputationScore":0,"reputationLevel":1}},"language":{"currentLanguage":"en"},"theme":{"isDark":false},"myOffers":{"myOffers":[],"loading":false,"error":"Real-time features may be limited. Try refreshing the page.","message":null,"showDeclineModal":false,"interestToDecline":null},"notificationStore":{"notifications":[],"isLoading":false,"error":"Request failed with status code 500","lastFetchedTimestamp":null},"connection":{"isConnected":false,"transportType":"websocket","reconnectAttempts":0,"maxReconnectAttempts":5,"lastDisconnectReason":"simulated_offline","isReconnecting":false}},"captureTimestamp":"2025-06-03T14:21:04.196Z"}}
