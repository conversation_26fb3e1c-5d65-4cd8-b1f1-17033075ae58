import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { logger as honoLogger } from 'hono/logger';
import { cors } from 'hono/cors';
import { verify } from 'hono/jwt';
import { Server as SocketIOServer, Server } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import authRoutes from './routes/auth';
import offerRoutes from './routes/offer';
import notificationRoutes from './routes/notificationRoutes';
import { createChatRouter } from './routes/chatRoutes';
import createTransactionRoutes from './routes/transactionRoutes'; // Import factory function
import createPayerNegotiationRoutes from './routes/payerNegotiationRoutes';
import createDebugRoutes from './routes/debugRoutes'; // Import debug routes factory
import { initializeEmailTransporter } from './services/email';
import { NotificationService } from './services/notificationService';
import { ChatService } from './services/chatService';
import { TransactionService } from './services/transactionService';
import { PayerNegotiationService } from './services/payerNegotiationService';
import { ClientLogService } from './services/clientLogService'; // Import client log service
import { ConsoleLogger, ILogger } from './utils/logger';
import { CHAT_MESSAGE_SEND } from './types/socketEvents';
import dotenv from 'dotenv';
import { AuthVariables } from './middleware/auth';

dotenv.config();

interface AppEnvVars {
  io: Server;
  notificationService: NotificationService;
  chatService: ChatService;
}

const app = new Hono<{ Variables: AppEnvVars & AuthVariables }>();
const prisma = new PrismaClient();
const consoleAppLogger: ILogger = new ConsoleLogger();

const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key';

initializeEmailTransporter().catch(console.error);

const port = parseInt(process.env.PORT || '3000', 10);

const server = serve({
  fetch: app.fetch,
  port
});

// Set up Socket.IO
export const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    methods: ['GET', 'POST']
  }
});

// Initialize services in proper dependency order
const notificationServiceInstance = new NotificationService(io);
export const chatServiceInstance = new ChatService(io);
const transactionServiceInstance = new TransactionService(io, notificationServiceInstance, chatServiceInstance);
const clientLogServiceInstance = new ClientLogService(); // Initialize client log service

// PayerNegotiationService with all dependencies
const payerNegotiationServiceInstance = new PayerNegotiationService(
  prisma,
  chatServiceInstance,
  io,
  consoleAppLogger,
  transactionServiceInstance,
  notificationServiceInstance
);

// Middleware
app.use('*', honoLogger());
app.use('*', cors());

app.use('*', async (c, next) => {
  c.set('io', io);
  c.set('notificationService', notificationServiceInstance);
  c.set('chatService', chatServiceInstance);
  await next();
});

// Basic health check endpoint
app.get('/', (c) => c.json({ status: 'ok', message: 'MUNygo API is running' }));

// Comprehensive health check endpoint for Docker
app.get('/health', async (c) => {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return c.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      database: 'connected',
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    consoleAppLogger.error('Health check failed:', error);
    return c.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 503);
  }
});

// API Health check endpoint
app.get('/api/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mount routes
app.route('/api/auth', authRoutes);
app.route('/api/offers', offerRoutes);
app.route('/api/notifications', notificationRoutes);
app.route('/api/debug', createDebugRoutes(clientLogServiceInstance, prisma)); // Mount debug routes with dual-write capability

// Use factory functions for routes that need service injection
app.route('/api/transactions', createTransactionRoutes(payerNegotiationServiceInstance));
app.route('/api/payer-negotiation', createPayerNegotiationRoutes(payerNegotiationServiceInstance));

// Socket.IO authentication middleware
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return next(new Error('Authentication error: Token required'));
    }

    const decoded = await verify(token, JWT_SECRET) as { userId: string };
    if (!decoded || !decoded.userId) {
      return next(new Error('Authentication error: Invalid token payload'));
    }

    socket.data.userId = decoded.userId;
    await socket.join(decoded.userId);
    consoleAppLogger.info(`Socket authenticated and joined room for user: ${decoded.userId}`);
    return next();
  } catch (err) {
    consoleAppLogger.error('Socket authentication error:', err);
    return next(new Error('Authentication error: Invalid token'));
  }
});

// Create chat router with transactionService and mount it
const chatRouter = createChatRouter(transactionServiceInstance);
app.route('/api/chat', chatRouter);

// Export getters for services if needed elsewhere
export const getNotificationService = () => {
  if (!notificationServiceInstance) {
    consoleAppLogger.error("NotificationService not initialized during getNotificationService call.");
    throw new Error("NotificationService has not been initialized.");
  }
  return notificationServiceInstance;
};

export const getTransactionService = () => {
  if (!transactionServiceInstance) {
    throw new Error("TransactionService not initialized.");
  }
  return transactionServiceInstance;
};

export const getPayerNegotiationService = () => {
    if (!payerNegotiationServiceInstance) {
        throw new Error("PayerNegotiationService not initialized.");
    }
    return payerNegotiationServiceInstance;
};

// Socket.IO connection handling
io.on('connection', (socket) => {
  consoleAppLogger.info(`✅ User connected: ${socket.data.userId} via ${socket.conn.transport.name} (ID: ${socket.id})`);

  // Monitor transport changes
  socket.conn.on('upgrade', () => {
    consoleAppLogger.info(`⬆️ User ${socket.data.userId} upgraded to: ${socket.conn.transport.name}`);
  });

  socket.conn.on('upgradeError', (error) => {
    consoleAppLogger.info(`⬇️ User ${socket.data.userId} upgrade failed, using: ${socket.conn.transport.name}`);
  });

  socket.on(CHAT_MESSAGE_SEND, (payload) => {
    chatServiceInstance.handleChatMessageSend(socket, payload);
  });

  socket.on('disconnect', (reason) => {
    consoleAppLogger.info(`🔌 User disconnected: ${socket.data.userId} (ID: ${socket.id}), reason: ${reason}`);
    
    // Enhanced disconnect reason logging
    if (reason === 'client namespace disconnect') {
      consoleAppLogger.info(`User ${socket.data.userId} disconnected voluntarily`);
    } else if (reason === 'transport close') {
      consoleAppLogger.info(`User ${socket.data.userId} lost connection (transport closed)`);
    } else if (reason === 'server namespace disconnect') {
      consoleAppLogger.info(`User ${socket.data.userId} disconnected by server`);
    }
  });

  socket.on('JOIN_TRANSACTION_ROOM', (transactionId: string) => {
    consoleAppLogger.info(`User ${socket.id} joining transaction room ${transactionId}`);
    socket.join(transactionId);
  });

  socket.on('LEAVE_TRANSACTION_ROOM', (transactionId: string) => {
    consoleAppLogger.info(`User ${socket.id} leaving transaction room ${transactionId}`);
    socket.leave(transactionId);
  });
});

consoleAppLogger.info(`Server is running on port ${port}`);
