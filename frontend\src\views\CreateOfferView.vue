<template>
  <OfferForm
    mode="create"
    @submit="handleSubmit"
    :loading="isLoading"
    :user-tier="authStore.user?.reputationLevel ?? 3"
  />
</template>

<style scoped>
.create-offer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 1.5rem;
  padding: 20px; /* Added padding similar to mock-up */
}

/* Ensure form items take full width within their context */
:deep(.n-form-item .n-form-item-blank),
:deep(.n-form-item .n-form-item-feedback-wrapper) {
  width: 100%;
}

.explanation-card {
  width: 100%;
  max-width: 700px; /* Match form max-width */
}

.card-title {
  font-size: 1.2rem; /* Increased size */
  font-weight: bold;
  display: flex;
  align-items: center;
}

.tier-box {
  border-radius: 6px; /* Standardized border-radius */
  padding: 12px 15px; /* Adjusted padding */
  height: 100%;
}

[data-theme="light"] .tier-box {
  background-color: #f9f9f9; /* Light background for contrast */
}

[data-theme="dark"] .tier-box {
  background-color: #2a2a2a; /* Dark background for contrast */
}

.lower-rep {
  border: 1px solid var(--n-warning-color-suppl);
  background-color: var(--n-warning-color-pressed); /* Softer warning background */
}

.higher-rep {
  border: 1px solid var(--n-success-color-suppl);
  background-color: var(--n-success-color-pressed); /* Softer success background */
}

.tier-box h3 { /* Naive UI NH3 used, style it */
  display: flex;
  align-items: center;
  font-size: 1.05rem; /* Adjusted size */
  margin-bottom: 8px;
}

.tier-icon {
  vertical-align: middle;
  margin-right: 6px; /* Adjusted margin */
  font-size: 20px; /* Explicit size */
}
.lower-rep .tier-icon { color: var(--n-warning-color); }
.higher-rep .tier-icon { color: var(--n-success-color); }


.tier-content {
  font-size: 0.9rem; /* Slightly smaller for content */
}

.tier-content p {
  margin: 4px 0; /* Reduced margin */
  line-height: 1.5;
}

.text-worse {
  color: var(--n-error-color); /* Use Naive UI var */
  font-weight: bold;
}

.text-better {
  color: var(--n-success-color); /* Use Naive UI var */
  font-weight: bold;
}

.adjustment-card {
  width: 100%;
  max-width: 700px;
}

.adjustment-card :deep(.n-card-header) {
  padding: 12px 16px; /* Adjust card header padding */
}
.adjustment-card :deep(.n-card__content) {
  padding: 16px; /* Adjust card content padding */
}


.helper-text {
  font-size: 0.85em;
  color: var(--n-text-color-disabled); /* Softer color for helper */
  margin-top: 4px;
  line-height: 1.4;
}
.helper-text .worse { color: var(--n-error-color); font-weight: bold; }
.helper-text .better { color: var(--n-success-color); font-weight: bold; }

.calculated-impact {
  margin-top: 15px;
  padding: 12px;
  background-color: var(--n-action-color); /* Subtle background */
  border: 1px dashed var(--n-border-color);
  border-radius: var(--n-border-radius);
}

.calculated-impact .rate-value {
  font-weight: bold;
  color: var(--n-text-color-base);
}

.calculated-impact .rate-direction-worse {
  color: var(--n-error-color);
}
.calculated-impact .rate-direction-better {
  color: var(--n-success-color);
}

/* General styling for form elements if needed */

:deep(.n-form-item-label) {
  font-weight: 600 !important; /* Make labels a bit bolder */
}
:deep(.n-h2) { /* Style for the "Tiered Adjustments" header */
    color: var(--n-title-text-color);
    font-weight: 600;
}
:deep(.n-h3) {
    color: var(--n-title-text-color);
    font-weight: 600;
}
:deep(.n-h4) {
    color: var(--n-title-text-color);
    font-weight: 600;
}

</style>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useMessage } from 'naive-ui';
import OfferForm from '@/components/OfferForm.vue';
import { offerService } from '@/services/offerService'; 
import type { CreateOfferPayload } from '@/types/offer'; // Corrected: Import type from @/types/offer
import { ZodError } from 'zod';

const router = useRouter();
const authStore = useAuthStore();
const message = useMessage();
const isLoading = ref(false);

async function handleSubmit(payload: any) {
  isLoading.value = true;
  try {
    if (!authStore.user?.id) {
      message.error('User ID not found. Please log in again.');
      isLoading.value = false;
      return;
    }
    const offerPayload: CreateOfferPayload = { // Corrected type usage
      ...payload,
      userId: authStore.user.id,
    };
    await offerService.createOffer(offerPayload); // Corrected call
    message.success('Offer created successfully!');
    router.push({ name: 'MyOffers' });
  } catch (error) {
    if (error instanceof ZodError) {
      message.error('Please check the form for errors.');
    } else {
      const errorMessage = (error as any)?.response?.data?.message || (error as Error)?.message || 'Failed to create offer. Please try again.';
      message.error(errorMessage);
    }
  } finally {
    isLoading.value = false;
  }
}
</script>
