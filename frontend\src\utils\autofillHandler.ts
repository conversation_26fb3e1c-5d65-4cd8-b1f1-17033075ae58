// filepath: c:\Code\MUNygo\frontend\src\utils\autofillHandler.ts

/**
 * Autofill Handler Utility
 * 
 * Handles browser autofill overlay conflicts with Vue.js DOM manipulation.
 * Prevents DOM manipulation errors from browser autofill services.
 */

import { useClientLogger } from '@/composables/useClientLogger';

interface AutofillConfig {
  enableMutationObserver: boolean;
  enableErrorSuppression: boolean;
  enableFormStabilization: boolean;
  debugMode: boolean;
}

const defaultConfig: AutofillConfig = {
  enableMutationObserver: true,
  enableErrorSuppression: true,
  enableFormStabilization: true,
  debugMode: import.meta.env.DEV
};

class AutofillHandler {
  private config: AutofillConfig;
  private logger = useClientLogger();
  private mutationObserver: MutationObserver | null = null;
  private originalInsertBefore: typeof Node.prototype.insertBefore | null = null;
  private originalRemoveChild: typeof Node.prototype.removeChild | null = null;
  private isInitialized = false;

  constructor(config: Partial<AutofillConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Initialize the autofill handler
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    if (this.config.debugMode) {
      this.logger.logInfo('Initializing autofill handler', {
        config: this.config
      });
    }

    if (this.config.enableErrorSuppression) {
      this.setupDOMMethodInterception();
    }

    if (this.config.enableMutationObserver) {
      this.setupMutationObserver();
    }

    if (this.config.enableFormStabilization) {
      this.setupFormStabilization();
    }

    this.isInitialized = true;
  }

  /**
   * Cleanup the autofill handler
   */
  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    if (this.originalInsertBefore) {
      Node.prototype.insertBefore = this.originalInsertBefore;
      this.originalInsertBefore = null;
    }

    if (this.originalRemoveChild) {
      Node.prototype.removeChild = this.originalRemoveChild;
      this.originalRemoveChild = null;
    }

    this.isInitialized = false;
  }

  /**
   * Setup DOM method interception to catch and handle autofill errors gracefully
   */
  private setupDOMMethodInterception(): void {
    // Store original methods
    this.originalInsertBefore = Node.prototype.insertBefore;
    this.originalRemoveChild = Node.prototype.removeChild;

    // Intercept insertBefore
    const self = this;
    Node.prototype.insertBefore = function(newNode: Node, referenceNode: Node | null) {
      try {
        return self.originalInsertBefore!.call(this, newNode, referenceNode);
      } catch (error: any) {
        if (AutofillHandler.isAutofillError(error)) {
          if (self.config.debugMode) {
            console.warn('[AutofillHandler] Suppressed autofill insertBefore error:', error.message);
          }
          // Return the newNode to maintain API compatibility
          return newNode;
        }
        // Re-throw non-autofill errors
        throw error;
      }
    };

    // Intercept removeChild
    Node.prototype.removeChild = function(child: Node) {
      try {
        return self.originalRemoveChild!.call(this, child);
      } catch (error: any) {
        if (AutofillHandler.isAutofillError(error)) {
          if (self.config.debugMode) {
            console.warn('[AutofillHandler] Suppressed autofill removeChild error:', error.message);
          }
          // Return the child to maintain API compatibility
          return child;
        }
        // Re-throw non-autofill errors
        throw error;
      }
    };
  }

  /**
   * Setup mutation observer to monitor DOM changes that might conflict with autofill
   */
  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check for autofill-related nodes being removed
          mutation.removedNodes.forEach((node) => {
            if (this.isAutofillNode(node)) {
              if (this.config.debugMode) {
                this.logger.logInfo('Autofill node removed by Vue.js', {
                  nodeName: node.nodeName,
                  nodeType: node.nodeType
                });
              }
            }
          });
        }
      });
    });

    // Observe the entire document for changes
    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Setup form stabilization to reduce DOM restructuring during autofill
   */
  private setupFormStabilization(): void {
    // Add CSS to stabilize form elements
    const style = document.createElement('style');
    style.textContent = `
      /* Autofill stabilization styles */
      .autofill-stable {
        position: relative !important;
      }

      .autofill-stable input,
      .autofill-stable .n-input,
      .autofill-stable .n-input-wrapper {
        position: relative !important;
        z-index: 1 !important;
      }

      /* Prevent autofill overlay positioning issues */
      .autofill-stable::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
        pointer-events: none;
      }
    `;

    // Safely append to head if it exists
    if (document.head) {
      document.head.appendChild(style);
    } else if (document.documentElement) {
      document.documentElement.appendChild(style);
    }

    // Apply stabilization to form elements
    this.applyFormStabilization();
  }

  /**
   * Apply stabilization classes to form elements
   */
  private applyFormStabilization(): void {
    // Use a timeout to ensure DOM is ready
    setTimeout(() => {
      const forms = document.querySelectorAll('form');
      forms.forEach((form) => {
        form.classList.add('autofill-stable');
      });

      const inputs = document.querySelectorAll('input[type="email"], input[type="password"], input[type="text"]');
      inputs.forEach((input) => {
        const parent = input.closest('.n-form-item, .input-wrapper');
        if (parent) {
          parent.classList.add('autofill-stable');
        }
      });
    }, 100);
  }

  /**
   * Check if an error is related to autofill functionality
   */
  private static isAutofillError(error: any): boolean {
    if (!error || typeof error.message !== 'string') {
      return false;
    }

    const autofillErrorPatterns = [
      /bootstrap-autofill-overlay/i,
      /AutofillInlineMenuContentService/i,
      /insertBefore.*not a child/i,
      /removeChild.*not a child/i,
      /autofill/i
    ];

    return autofillErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Check if a DOM node is related to autofill functionality
   */
  private isAutofillNode(node: Node): boolean {
    if (node.nodeType !== Node.ELEMENT_NODE) {
      return false;
    }

    const element = node as Element;
    const className = element.className || '';
    const id = element.id || '';

    const autofillPatterns = [
      /autofill/i,
      /password-manager/i,
      /credential/i,
      /overlay/i
    ];

    return autofillPatterns.some(pattern => 
      pattern.test(className) || pattern.test(id)
    );
  }
}

// Create and export a singleton instance
export const autofillHandler = new AutofillHandler();

// Export the class for testing
export { AutofillHandler };

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      autofillHandler.initialize();
    });
  } else {
    autofillHandler.initialize();
  }
}
