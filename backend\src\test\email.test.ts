import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'; // Added afterEach
import * as nodemailer from 'nodemailer';
import * as emailService from '../services/email';

// Mock nodemailer
vi.mock('nodemailer', async () => {
  const actual = await vi.importActual<typeof nodemailer>('nodemailer');
  // Keep track of the mock sendMail function
  const mockSendMail = vi.fn(async (opts) => ({
    messageId: 'test-message-id',
    ...opts,
  }));
  const mockCreateTransport = vi.fn(() => ({
    sendMail: mockSendMail,
  }));
  return {
    ...actual,
    createTransport: mockCreateTransport,
    getTestMessageUrl: vi.fn(() => 'http://ethereal.test/preview'),
    createTestAccount: vi.fn(async () => ({
      user: 'testuser',
      pass: 'testpass',
      // Add properties expected by initializeEmailTransporter's console.log
      smtp: { host: 'smtp.ethereal.email', port: 587, secure: false },
      web: 'https://ethereal.email'
    })),
    // Expose mocks for inspection if needed, though direct calls are better
    _mockSendMail: mockSendMail,
    _mockCreateTransport: mockCreateTransport,
  };
});

// Store original NODE_ENV
const originalNodeEnv = process.env.NODE_ENV;
const originalFrontendUrl = process.env.FRONTEND_URL;

describe('email service', () => {
  beforeEach(async () => {
    // Reset mocks and environment before each test
    vi.clearAllMocks();
    process.env.NODE_ENV = 'test'; // Default to test/dev environment
    delete process.env.FRONTEND_URL; // Ensure FRONTEND_URL is reset
    // Initialize transporter with current env settings
    await emailService.initializeEmailTransporter();
  });

  afterEach(() => {
    // Restore original environment variables after each test
    process.env.NODE_ENV = originalNodeEnv;
    process.env.FRONTEND_URL = originalFrontendUrl;
  });

  it('sends verification email with correct parameters', async () => {
    const email = '<EMAIL>';
    const token = 'sometoken';
    const info = await emailService.sendVerificationEmail(email, token);

    // Get the arguments passed to the mocked sendMail
    const sendMailMock = (nodemailer as any)._mockSendMail;
    expect(sendMailMock).toHaveBeenCalledTimes(1);
    const mailOptions = sendMailMock.mock.calls[0][0];

    expect(mailOptions.to).toBe(email);
    expect(mailOptions.subject).toBe('Verify your email address');
    expect(mailOptions.text).toContain(token);
    expect(mailOptions.html).toContain(token);
    expect(mailOptions.from).toBe('"MUNygo" <<EMAIL>>');
  });

  it('uses the default verification URL when FRONTEND_URL is not set', async () => {
    const email = '<EMAIL>';
    const token = 'othertoken';
    const defaultFrontendUrl = 'http://localhost:5173'; // Default from email.ts
    await emailService.sendVerificationEmail(email, token);

    const sendMailMock = (nodemailer as any)._mockSendMail;
    const mailOptions = sendMailMock.mock.calls[0][0];
    const expectedUrl = `${defaultFrontendUrl}/verify-email?token=${token}`;

    expect(mailOptions.text).toContain(expectedUrl);
    expect(mailOptions.html).toContain(`href="${expectedUrl}"`);
  });

  it('uses the FRONTEND_URL environment variable for verification URL when set', async () => {
    const email = '<EMAIL>';
    const token = 'customtoken';
    const customFrontendUrl = 'https://munygo.app';
    process.env.FRONTEND_URL = customFrontendUrl; // Set custom URL for this test

    // Re-initialize transporter is not needed here as sendVerificationEmail reads the env var directly
    await emailService.sendVerificationEmail(email, token);

    const sendMailMock = (nodemailer as any)._mockSendMail;
    const mailOptions = sendMailMock.mock.calls[0][0];
    const expectedUrl = `${customFrontendUrl}/verify-email?token=${token}`;

    expect(mailOptions.text).toContain(expectedUrl);
    expect(mailOptions.html).toContain(`href="${expectedUrl}"`);
  });


  it('logs preview URL in development environment', async () => {
    process.env.NODE_ENV = 'development'; // Explicitly set development
    await emailService.initializeEmailTransporter(); // Re-initialize for this env

    const spy = vi.spyOn(console, 'log');
    await emailService.sendVerificationEmail('<EMAIL>', 'devtoken');

    // Check if getTestMessageUrl was called and console.log includes its result
    expect(nodemailer.getTestMessageUrl).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('Preview URL:', 'http://ethereal.test/preview');
    spy.mockRestore();
  });

  it('does NOT log preview URL in production environment', async () => {
    process.env.NODE_ENV = 'production'; // Set production environment
    // Mock production transport setup
    process.env.SMTP_HOST = 'smtp.prod.com';
    process.env.SMTP_PORT = '465';
    process.env.SMTP_USER = 'produser';
    process.env.SMTP_PASS = 'prodpass';

    await emailService.initializeEmailTransporter(); // Re-initialize for production

    const spy = vi.spyOn(console, 'log');
    await emailService.sendVerificationEmail('<EMAIL>', 'prodtoken');

    // Ensure getTestMessageUrl was NOT called in production path
    expect(nodemailer.getTestMessageUrl).not.toHaveBeenCalled();
    // Ensure console.log was not called with the preview URL message
    expect(spy).not.toHaveBeenCalledWith(expect.stringContaining('Preview URL:'), expect.anything());

    spy.mockRestore();
    // Clean up production env vars if necessary, though afterEach handles NODE_ENV
    delete process.env.SMTP_HOST;
    delete process.env.SMTP_PORT;
    delete process.env.SMTP_USER;
    delete process.env.SMTP_PASS;
  });

  // Optional: Test initializeEmailTransporter directly
  describe('initializeEmailTransporter', () => {
    it('should use ethereal settings in development', async () => {
      process.env.NODE_ENV = 'development';
      await emailService.initializeEmailTransporter();
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
      }));
    });

    it('should use production settings in production', async () => {
      process.env.NODE_ENV = 'production';
      process.env.SMTP_HOST = 'smtp.prod.com';
      process.env.SMTP_PORT = '465';
      process.env.SMTP_USER = 'produser';
      process.env.SMTP_PASS = 'prodpass';

      await emailService.initializeEmailTransporter();
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.prod.com',
        port: 465,
        secure: true,
        auth: { user: 'produser', pass: 'prodpass' },
      }));

      delete process.env.SMTP_HOST;
      delete process.env.SMTP_PORT;
      delete process.env.SMTP_USER;
      delete process.env.SMTP_PASS;
    });
  });
});
