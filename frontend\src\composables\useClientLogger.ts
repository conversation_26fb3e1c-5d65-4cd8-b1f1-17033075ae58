import { ref, readonly } from 'vue';
import type { LogEntry, LogLevel, ClientReportPayload, ClientReportResponse, ReportDetails, UserAction, UserContext, DiagnosticData, UserIdentification } from '@/types/logging';
import apiClient from '@/services/apiClient';
import { useConnectionStore } from '@/stores/connection';
import { useAuthStore } from '@/stores/auth';
import { useThemeStore } from '@/stores/theme';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { getActivePinia } from 'pinia';

const MAX_LOG_ENTRIES = 200;
const MAX_USER_ACTIONS = 50;
const MAX_ROUTE_HISTORY = 20;
const SESSION_ID = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Global log buffer
const logs = ref<LogEntry[]>([]);
// User action tracking
const userActions = ref<UserAction[]>([]);
// Route history tracking
const routeHistory = ref<string[]>([]);

/**
 * Core client-side logging composable for debug reporting
 */
export function useClientLogger() {
  
  /**
   * Add a log entry to the buffer
   */
  const addLogEntry = (level: LogLevel, message: string, context?: Record<string, any>, error?: any) => {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context ? { ...context } : undefined,
      url: window.location.href,
    };

    // Extract stack trace from error object if available
    if (error && error.stack) {
      entry.stackTrace = error.stack;
    } else if (error && typeof error === 'string') {
      entry.context = { ...entry.context, errorDetails: error };
    }

    // Add to buffer
    logs.value.push(entry);

    // Maintain buffer size limit (circular buffer behavior)
    if (logs.value.length > MAX_LOG_ENTRIES) {
      logs.value.shift(); // Remove oldest entry
    }

    // Also log to browser console for immediate debugging
    const consoleMessage = `[${level}] ${message}`;
    switch (level) {
      case 'ERROR':
        console.error(consoleMessage, context, error);
        break;
      case 'WARN':
        console.warn(consoleMessage, context);
        break;
      case 'DEBUG':
        console.debug(consoleMessage, context);
        break;
      default:
        console.log(consoleMessage, context);
    }
  };

  /**
   * Log info level message
   */
  const logInfo = (message: string, context?: Record<string, any>) => {
    addLogEntry('INFO', message, context);
  };

  /**
   * Log warning level message
   */
  const logWarn = (message: string, context?: Record<string, any>) => {
    addLogEntry('WARN', message, context);
  };

  /**
   * Log error level message
   */
  const logError = (message: string, error?: any, context?: Record<string, any>) => {
    addLogEntry('ERROR', message, context, error);
  };

  /**
   * Log debug level message
   */
  const logDebug = (message: string, context?: Record<string, any>) => {
    addLogEntry('DEBUG', message, context);
  };

  /**
   * Log user action for better correlation with issues
   */
  const logUserAction = (action: string, details?: Record<string, any>) => {
    const userAction: UserAction = {
      action,
      timestamp: new Date().toISOString(),
      details
    };

    userActions.value.push(userAction);
    if (userActions.value.length > MAX_USER_ACTIONS) {
      userActions.value.shift();
    }

    // Also add to regular logs for comprehensive tracking
    logInfo(`User action: ${action}`, details);
  };

  /**
   * Log navigation events
   */
  const logNavigation = (to: string, from?: string) => {
    routeHistory.value.push(to);
    if (routeHistory.value.length > MAX_ROUTE_HISTORY) {
      routeHistory.value.shift();
    }

    logUserAction('navigation', { to, from, timestamp: new Date().toISOString() });
  };

  /**
   * Get current logs (read-only copy)
   */
  const getLogs = (): LogEntry[] => {
    return [...logs.value];
  };

  /**
   * Clear all logs from buffer
   */
  const clearLogs = () => {
    logs.value.length = 0;
  };

  /**
   * Get current log count
   */
  const getLogCount = () => {
    return logs.value.length;
  };

  /**
   * Safely serialize a value, handling non-serializable objects
   */
  const safeSerialize = (value: any, maxDepth: number = 3, currentDepth: number = 0): any => {
    if (currentDepth >= maxDepth) {
      return '[Max depth reached]';
    }

    if (value === null || value === undefined) {
      return value;
    }

    if (typeof value === 'function') {
      return '[Function]';
    }

    if (typeof value === 'symbol') {
      return '[Symbol]';
    }

    if (value instanceof Date) {
      return value.toISOString();
    }

    if (value instanceof Error) {
      return {
        name: value.name,
        message: value.message,
        stack: value.stack
      };
    }

    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        return value.map(item => safeSerialize(item, maxDepth, currentDepth + 1));
      }

      // Handle circular references and complex objects
      try {
        const serialized: any = {};
        for (const key in value) {
          if (value.hasOwnProperty(key)) {
            try {
              serialized[key] = safeSerialize(value[key], maxDepth, currentDepth + 1);
            } catch (error) {
              serialized[key] = '[Serialization Error]';
            }
          }
        }
        return serialized;
      } catch (error) {
        return '[Object Serialization Error]';
      }
    }

    return value;
  };

  /**
   * Capture diagnostic data including connection status and Pinia store snapshot
   */
  const captureDiagnosticData = (): DiagnosticData => {
    const connectionStore = useConnectionStore();
    const socket = centralizedSocketManager.getSocket();

    // Capture connection status
    const connectionStatus = {
      isConnected: connectionStore.isConnected,
      connectionQuality: connectionStore.connectionQuality,
      connectionStatus: connectionStore.connectionStatus,
      transportType: connectionStore.transportType,
      reconnectAttempts: connectionStore.reconnectAttempts,
      isReconnecting: connectionStore.isReconnecting,
      lastDisconnectReason: connectionStore.lastDisconnectReason,
      socketId: socket?.id,
      socketConnected: socket?.connected
    };

    // Capture Pinia store snapshot
    const piniaStoreSnapshot: { [storeName: string]: any } = {};

    try {
      // Get the active Pinia instance
      const activePinia = getActivePinia();

      if (activePinia && activePinia.state.value) {
        // Get all store states from the active Pinia instance
        const storeStates = activePinia.state.value;

        for (const [storeName, storeState] of Object.entries(storeStates)) {
          try {
            // Safely serialize each store's state
            piniaStoreSnapshot[storeName] = safeSerialize(storeState);
          } catch (error) {
            piniaStoreSnapshot[storeName] = {
              error: 'Failed to serialize store state',
              errorMessage: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        }
      } else {
        // Fallback: manually capture known stores
        try {
          const authStore = useAuthStore();
          const connectionStore = useConnectionStore();
          const themeStore = useThemeStore();

          piniaStoreSnapshot['auth'] = safeSerialize({
            user: authStore.user,
            isAuthenticated: authStore.isAuthenticated,
            token: authStore.token ? '[REDACTED]' : null // Don't expose actual token
          });

          piniaStoreSnapshot['connection'] = safeSerialize({
            isConnected: connectionStore.isConnected,
            connectionQuality: connectionStore.connectionQuality,
            connectionStatus: connectionStore.connectionStatus,
            transportType: connectionStore.transportType,
            reconnectAttempts: connectionStore.reconnectAttempts,
            isReconnecting: connectionStore.isReconnecting,
            lastDisconnectReason: connectionStore.lastDisconnectReason
          });

          piniaStoreSnapshot['theme'] = safeSerialize({
            isDark: themeStore.isDark
          });
        } catch (fallbackError) {
          piniaStoreSnapshot['_fallback_error'] = {
            error: 'Failed to capture stores manually',
            errorMessage: fallbackError instanceof Error ? fallbackError.message : 'Unknown error'
          };
        }
      }
    } catch (error) {
      piniaStoreSnapshot['_error'] = {
        error: 'Failed to access Pinia state',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    return {
      connectionStatus,
      piniaStoreSnapshot,
      captureTimestamp: new Date().toISOString()
    };
  };

  /**
   * Capture user identification if user is authenticated
   */
  const captureUserIdentification = (): UserIdentification | undefined => {
    try {
      const authStore = useAuthStore();
      if (authStore.isAuthenticated && authStore.user) {
        return {
          userId: authStore.user.id,
          email: authStore.user.email,
          username: authStore.user.username
        };
      }
    } catch (error) {
      // Silently fail if auth store is not available or user is not authenticated
      console.debug('Could not capture user identification:', error);
    }
    return undefined;
  };

  /**
   * Check if an error indicates a network connectivity issue
   */
  const isNetworkError = (error: any): boolean => {
    if (!error) return false;

    // Check for common network error indicators
    const networkErrorPatterns = [
      'Network Error',
      'ERR_NETWORK',
      'ERR_INTERNET_DISCONNECTED',
      'ERR_CONNECTION_REFUSED',
      'ERR_CONNECTION_TIMED_OUT',
      'Failed to fetch',
      'NetworkError',
      'fetch is not defined'
    ];

    const errorMessage = error.message || error.toString() || '';
    const errorCode = error.code || '';

    // Check if it's a network-related error
    const isNetworkIssue = networkErrorPatterns.some(pattern =>
      errorMessage.includes(pattern) || errorCode.includes(pattern)
    );

    // Check for specific HTTP status codes that indicate connectivity issues
    const status = error.response?.status;
    const isConnectivityStatus = !status || status === 0;

    // Check if browser is offline
    const isBrowserOffline = typeof navigator !== 'undefined' && !navigator.onLine;

    return isNetworkIssue || isConnectivityStatus || isBrowserOffline;
  };

  /**
   * Send logs to server for debugging with structured report details and enhanced context
   */
  const sendLogsToServer = async (reportDetails: ReportDetails): Promise<ClientReportResponse> => {
    try {
      // Capture diagnostic data at the time of submission
      const diagnosticData = captureDiagnosticData();

      // Capture user identification if available
      const userIdentification = captureUserIdentification();

      // Generate enhanced report with correlation and context
      const enhancedReportDetails: ReportDetails = {
        ...reportDetails,
        userContext: generateUserContext(),
        correlatedLogEntries: getCorrelatedLogs(10), // Get last 10 minutes of logs
        reportTags: reportDetails.reportTags || []
      };

      const reportPayload: ClientReportPayload = {
        logs: getLogs(),
        reportDetails: enhancedReportDetails,
        timestamp: new Date().toISOString(),
        sessionId: SESSION_ID,
        diagnosticData: diagnosticData,
        userIdentification: userIdentification,
      };

      logInfo('Sending enhanced debug report to server', {
        logCount: reportPayload.logs.length,
        correlatedLogCount: enhancedReportDetails.correlatedLogEntries?.length || 0,
        userActionCount: enhancedReportDetails.userContext?.userActions.length || 0,
        reportType: reportDetails.type,
        reportSeverity: reportDetails.severity,
        hasStepsToReproduce: !!reportDetails.stepsToReproduce,
        tags: enhancedReportDetails.reportTags,
        hasDiagnosticData: !!reportPayload.diagnosticData,
        hasUserIdentification: !!userIdentification,
        userId: userIdentification?.userId,
        connectionStatus: diagnosticData.connectionStatus.connectionQuality,
        storeCount: Object.keys(diagnosticData.piniaStoreSnapshot).length
      });

      const response = await apiClient.post<ClientReportResponse>(
        '/debug/report-issue',
        reportPayload
      );

      logInfo('Enhanced debug report sent successfully', {
        reportId: response.data.reportId,
        reportType: reportDetails.type
      });

      return response.data;

    } catch (error: any) {
      const errorMessage = 'Failed to send enhanced debug report to server';
      logError(errorMessage, error, {
        errorType: 'REPORT_SEND_FAILED',
        statusCode: error.response?.status,
        responseData: error.response?.data,
        reportType: reportDetails.type,
        isNetworkError: isNetworkError(error),
        browserOnline: typeof navigator !== 'undefined' ? navigator.onLine : 'unknown'
      });

      // Determine if this is a network connectivity issue
      const isConnectivityIssue = isNetworkError(error);

      // Return a standardized error response with network error flag
      return {
        success: false,
        message: error.response?.data?.message || (isConnectivityIssue
          ? 'Unable to connect to server. Report will be saved offline.'
          : 'Failed to send report. Please try again.'),
        isNetworkError: isConnectivityIssue
      };
    }
  };

  /**
   * Check if an error is related to browser autofill functionality
   */
  const isAutofillError = (error: any): boolean => {
    if (!error) return false;

    const errorMessage = typeof error === 'string' ? error : error.message || '';
    const errorStack = error.stack || '';

    const autofillPatterns = [
      /bootstrap-autofill-overlay/i,
      /AutofillInlineMenuContentService/i,
      /insertBefore.*not a child/i,
      /removeChild.*not a child/i,
      /autofill.*overlay/i,
      /credential.*manager/i,
      /password.*manager/i
    ];

    return autofillPatterns.some(pattern =>
      pattern.test(errorMessage) || pattern.test(errorStack)
    );
  };

  /**
   * Handle global JavaScript errors
   */
  const handleGlobalError = (
    message: string | Event,
    source?: string,
    lineno?: number,
    colno?: number,
    error?: Error
  ) => {
    // Check if this is an autofill-related error
    if (isAutofillError(error) || isAutofillError(message)) {
      // Log autofill errors at info level instead of error level
      logInfo('Browser autofill DOM manipulation suppressed', {
        message: typeof message === 'string' ? message : 'Autofill error event',
        source,
        lineno,
        colno,
        errorType: 'AUTOFILL_DOM_ERROR',
        suppressed: true
      });
      return;
    }

    logError('Global JavaScript error', error, {
      message: typeof message === 'string' ? message : 'Global error event',
      source,
      lineno,
      colno,
      errorType: 'GLOBAL_JS_ERROR',
    });
  };

  /**
   * Handle unhandled promise rejections
   */
  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    // Check if this is an autofill-related promise rejection
    if (isAutofillError(event.reason)) {
      // Log autofill promise rejections at info level
      logInfo('Browser autofill promise rejection suppressed', {
        reason: event.reason?.message || event.reason,
        errorType: 'AUTOFILL_PROMISE_REJECTION',
        reasonType: typeof event.reason,
        suppressed: true
      });
      return;
    }

    logError('Unhandled promise rejection', event.reason, {
      errorType: 'UNHANDLED_PROMISE_REJECTION',
      reasonType: typeof event.reason,
    });
  };

  /**
   * Handle Vue component errors
   */
  const handleVueError = (err: unknown, instance: any, info: string) => {
    logError('Vue component error', err, {
      componentName: instance?.$options?.name || instance?.__name || 'Unknown',
      lifecycleHook: info,
      errorType: 'VUE_COMPONENT_ERROR',
    });  };

  /**
   * Get correlated logs (recent entries that might be related to current issue)
   */
  const getCorrelatedLogs = (lookbackMinutes: number = 5): LogEntry[] => {
    const cutoffTime = new Date(Date.now() - lookbackMinutes * 60 * 1000);
    return logs.value.filter(log => new Date(log.timestamp) > cutoffTime);
  };

  /**
   * Generate comprehensive user context for reports
   */
  const generateUserContext = (): UserContext => {
    return {
      currentPage: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      timestamp: new Date().toISOString(),
      userActions: [...userActions.value],
      routeHistory: [...routeHistory.value]
    };
  };

  /**
   * Get user actions history
   */
  const getUserActions = (): UserAction[] => {
    return [...userActions.value];
  };

  /**
   * Get route navigation history
   */
  const getRouteHistory = (): string[] => {
    return [...routeHistory.value];
  };
  return {
    // Logging functions
    logInfo,
    logWarn,
    logError,
    logDebug,

    // Enhanced tracking functions
    logUserAction,
    logNavigation,

    // Buffer management
    getLogs,
    clearLogs,
    getLogCount,

    // Enhanced correlation and context
    getCorrelatedLogs,
    generateUserContext,
    getUserActions,
    getRouteHistory,

    // Diagnostic data capture
    captureDiagnosticData,
    captureUserIdentification,

    // Report sending
    sendLogsToServer,

    // Error handlers for global setup
    handleGlobalError,
    handleUnhandledRejection,
    handleVueError,

    // Read-only access to current data for debugging
    logs: readonly(logs),
    userActions: readonly(userActions),
    routeHistory: readonly(routeHistory),
  };
}
