export interface InterestRequestFrontend {
  id: string; // Interest ID
  offerId: string; // Added: ID of the offer this interest is for
  interestedUserId: string;
  username: string;
  reputationLevel: number;
  status: InterestStatus; // Changed to use the more general InterestStatus
  chatSessionId?: string | null; // MODIFIED: Allow null
  transactionStatus?: string | null; // Add transaction status
  negotiationStatus?: string | null; // Add negotiation status
  reasonCode?: string | null; // MODIFIED: Allow null
  // Added: Offer details for display in notifications/lists
  offerType?: 'BUY' | 'SELL'; 
  offerAmount?: number;
  offerCurrencyPair?: string; 
  createdAt: string; // Changed from Date | string to string, assuming ISO string
}

export type OfferStatusFrontend = 'ACTIVE' | 'INACTIVE' | 'DEACTIVATED' | 'COMPLETED' | 'CANCELLED';

export interface MyOffer {
  id: string;
  type: 'BUY' | 'SELL';
  amount: number;
  baseRate: number;
  adjustmentForLowerRep: number;
  adjustmentForHigherRep: number;
  status: OfferStatusFrontend;
  currencyPair: string;
  createdAt: string; // ISO string
  updatedAt?: string; // ISO string
  user: { // The offer creator (current user)
    username: string;
    reputationLevel: number;
  };
  interests: InterestRequestFrontend[];
}

export interface BrowseOffer {
  id: string;
  type: 'BUY' | 'SELL';
  amount: number;
  baseRate: number;
  adjustmentForLowerRep: number;
  adjustmentForHigherRep: number;
  status: OfferStatusFrontend;
  createdAt: string; // ISO string
  offerCreatorId: string;
  offerCreatorUsername: string;
  offerCreatorReputationLevel: number | null;
  calculatedApplicableRate: number;
  currentUserHasShownInterest: boolean;
  currentUserInterestStatus?: 'PENDING' | 'ACCEPTED' | 'DECLINED' | null; // Added
  chatSessionId?: string | null; // ADDED for chat navigation
  transactionStatus?: string | null; // Transaction status from backend
  negotiationStatus?: string | null; // Negotiation status from backend
  currencyPair?: string; // Add this field that's missing but present in backend
}

export interface ViewedOfferDetails extends BrowseOffer {
  isNoLongerActive?: boolean;
  isLoading?: boolean;
  fetchError?: string | null;
  lastUpdated?: string; // ISO date string
}

export type InterestStatus = 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'CANCELLED';

// Updated InterestNotification interface
export interface InterestNotification {
  id: string; // Unique ID for the notification item, typically the interestId.
  type: 'INTEREST_RECEIVED'; // Type of notification.
  interestId: string; // The actual ID of the interest record from the database.
  offerId: string;
  offerDetails: {
    type: string; // 'BUY' or 'SELL'
    amount: number;
    currencyPair: string;
  };
  interestedUser: {
    id: string; // ID of the user who showed interest.
    username: string;
    reputationLevel: number;
  };
  timestamp: Date; // Date object for when the interest was shown/notification created.
  read: boolean; // UI state for whether the notification has been read.
  status: 'PENDING'; // All notifications in this store are for pending interests.
}

export interface OfferWithUser {
  id: string;
  type: 'BUY' | 'SELL';
  amount: number;
  baseRate: number;
  adjustmentForLowerRep: number;
  adjustmentForHigherRep: number;
  rate?: number; // Calculated rate
  status: OfferStatusFrontend;
  currencyPair: string;
  description?: string;
  createdAt: string; // ISO string
  updatedAt?: string; // ISO string
  userId: string;
  user: {
    username: string;
    reputationLevel: number;
  };
  userInterest?: {
    id: string;
    status: InterestStatus;
  } | null;
  isOwner: boolean;
}
