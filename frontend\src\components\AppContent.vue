<template>
  <router-view v-slot="{ Component, route }">
    <template v-if="route.name === 'landing' || route.name === 'login' || route.name === 'register'">
      <component :is="Component" />
    </template>
    <template v-else>
      <div class="app-container">
        <!-- Use the new NavBar component -->
        <NavBar />
        
        <!-- Main Content -->
        <div class="main-content">
          <component :is="Component" />
        </div>
      </div>
    </template>
  </router-view>
  <DeclineInterestModal v-if="isAuthenticated" />
</template>

<script setup lang="ts">
import { onMounted, computed, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useMyOffersStore } from '@/stores/myOffersStore';
import { useNotificationStore } from '@/stores/notificationStore';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import DeclineInterestModal from '@/components/DeclineInterestModal.vue';
import NavBar from '@/components/NavBar.vue';

const authStore = useAuthStore();
const myOffersStore = useMyOffersStore();
const notificationStore = useNotificationStore();

const isAuthenticated = computed(() => authStore.isAuthenticated);

// Initialize socket management when authenticated
watch(isAuthenticated, (authenticated) => {
  if (authenticated) {
    centralizedSocketManager.initializeSocket();
    console.log('🔌 [AppContent] Socket manager initialized for authenticated user');
  } else {
    centralizedSocketManager.disconnect();
    console.log('🔌 [AppContent] Socket manager cleaned up for logged out user');
  }
}, { immediate: true });

onMounted(() => {
  console.log('🔥 [AppContent] Component mounted');
  if (authStore.isAuthenticated) {
    console.log('🔥 [AppContent] User is authenticated, fetching data...');
    myOffersStore.fetchMyOffers();
    notificationStore.fetchNotifications();
    // Note: fetchTransaction requires an ID, so we skip it here
  }
});
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  min-height: 0;
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }
}
</style>
