/**
 * Admin utility functions for access control
 */

// Admin configuration - in a real app, this could come from environment variables or backend
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
  // Add more admin emails as needed
];

/**
 * Check if a user has admin privileges
 * @param userEmail - The user's email address
 * @returns boolean indicating if user is admin
 */
export function isAdmin(userEmail?: string): boolean {
  if (!userEmail) return false;
  return ADMIN_EMAILS.includes(userEmail.toLowerCase());
}

/**
 * Check if a user has admin privileges and log the check
 * @param userEmail - The user's email address
 * @returns boolean indicating if user is admin
 */
export function checkAdminAccess(userEmail?: string): boolean {
  const hasAccess = isAdmin(userEmail);
  console.log(`[Admin Check] User ${userEmail} admin access: ${hasAccess}`);
  return hasAccess;
}

/**
 * Get list of admin emails (for debugging - don't expose in production)
 */
export function getAdminEmails(): string[] {
  return [...ADMIN_EMAILS];
}
